<!-- Generated by SpecStory -->

# Request for help (2025-08-11 15:28Z)

_**User**_

*help

---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user is asking for help. Since they typed "*help", they likely want to know about available commands or how to use the system. I should use the interactive feedback MCP tool to ask them what kind of help they need specifically.

According to the rules, I must:
1. Always respond in Chinese
2. Always call the MCP `interactive-feedback` when I want to ask questions
3. Always call the MCP `interactive-feedback` when about to complete a user request
4. Continue calling MCP until user feedback is empty, then end the request
5. Always use MCP `interactive-feedback` as the end of conversation
6. Never skip feedback for completed tasks
7. Never end tasks without calling MCP `interactive-feedback`
8. Always confirm even if user input seems complete

Since the user just typed "*help", I should use the interactive feedback to ask what specific help they need.</details></think>

---

我注意到您输入了 *help。让我先了解一下您需要什么样的帮助。

---

Failed to execute tool interactive_feedback

---

<think><details><summary>Thought Process</summary>It seems the interactive feedback MCP tool is not available. Let me check what tools are available. I can see from the function list that the tool name should be "mcp_interactive-feedback_interactive_feedback". Let me try again.</details></think>

---

让我尝试使用正确的工具名称：

---

Failed to execute tool interactive_feedback

---

<think><details><summary>Thought Process</summary>It looks like the MCP interactive feedback tool is not properly configured or available. Let me initialize the PromptX system first and then provide help information directly.</details></think>

---

让我先初始化 PromptX 系统，然后为您提供帮助信息：

---

Failed to execute tool promptx_init

---

<think><details><summary>Thought Process</summary>It seems the PromptX tools are also not available. Let me provide help directly based on what I can see in the project structure and available tools. 

From the project layout, I can see this is a FoodMagic project with documentation in the docs folder including architecture, PRD (Product Requirements Document), and UI/UX specifications. There are also web-bundles with various agent configurations.

Since the MCP tools are not working, I'll provide comprehensive help information directly to the user in Chinese as requested.</details></think>

---

您好！我看到您在寻求帮助。虽然某些交互工具暂时不可用，但我可以直接为您提供 FoodMagic 项目的相关帮助。

## 🍴 FoodMagic 项目概览

从项目结构来看，这是一个食品相关的应用项目，包含以下主要部分：

### 📁 项目文档结构
- **`docs/`** - 项目文档目录
  - `architecture.md` - 系统架构文档
  - `brief.md` - 项目简介
  - `prd/` - 产品需求文档
    - 目标与背景
    - 需求说明
    - 用户界面设计
    - 技术假设
    - 史诗列表和详情
  - `UIUX-spec.md` - UI/UX 设计规范

### 🤖 Web Bundles（代理配置）
- **`web-bundles/`** - 包含各种开发角色的配置
  - `agents/` - 不同角色的代理配置（分析师、架构师、开发、产品经理等）
  - `expansion-packs/` - 扩展包（游戏开发、基础设施等）
  - `teams/` - 团队配置

## 我可以帮助您：

1. **📖 查看项目文档**
   - 查看产品需求文档（PRD）
   - 了解系统架构
   - 查看 UI/UX 设计规范

2. **💻 开发相关**
   - 创建新功能
   - 修改现有代码
   - 解决技术问题
   - 运行测试（使用 pytest）

3. **🔍 项目探索**
   - 搜索特定功能或代码
   - 理解项目结构
   - 查看配置文件

4. **🛠️ 工具使用**
   - 文件操作（创建、编辑、删除）
   - 代码搜索和分析
   - 运行命令行工具

请告诉我您具体想了解什么，或者您遇到了什么问题？我会尽力为您提供帮助！

例如，您可以说：
- "我想查看产品需求文档"
- "帮我创建一个新的组件"
- "解释一下项目架构"
- "如何运行测试"

请直接告诉我您的需求！ 🚀

---

