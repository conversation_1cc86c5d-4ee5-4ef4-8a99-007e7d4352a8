<!-- Generated by SpecStory -->

# Request for help (2025-08-11 13:30Z)

_**User**_

@pm.mdc  *help

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Planning interactive feedback**

I'm noting that the user's rules require interactive feedback at the end, but it doesn't specify that no text can come before it. I think it makes sense to include a summary before calling the tool. I also need to keep in mind the "communication" aspects, like using markdown headings and bolding list headers. Presenting commands in a numbered list with backticks is a good plan, along with short descriptions for clarity.
**Crafting command list**

I'm working on crafting commands based on the YAML provided. Here's what I've got so far: there's a help command to show a numbered list of options. I've also got various tasks like creating PRD documents, epics, and user stories. However, I noticed redundancy between `create-brownfield-epic` and `create-epic`, as they both refer to similar tasks, so I'll list them both. It might also be helpful to add brief descriptions in Chinese for clarity.
**Understanding interaction rules**

I’m keeping in mind that the mandatory interaction rule states I need to present tasks and templates as a numbered list for user selection. I’ll ask users to respond with a number to execute their choice. Using interactive feedback, I might present options like "1 - *create-prd — 使用PRD模板创建新文档." I’ll also include a greeting: "我是<PERSON> 📋 产品经理，已激活。你可以输入 *help 查看可用命令。” Even though they've seen that, it’s good to greet them. I’m aware our action doesn’t involve loading files, just serving as the agent!
**Preparing for tool call**

I don't need to make any code edits or read files. According to the "non_compliance" rule, I should provide a status update before tool calls. I’ll say something like, "已激活PM代理并根据 *help 输出命令清单；现在请你选择下一步要执行的命令." Then, I’ll list the options for clarity. Finally, I’ll summarize with, "已激活并展示命令清单；等待你选择." I’ll also ask the user to reply with command names or numbers, but I need to keep things concise!</details></think>

---

进度小结：已按 `@pm` 激活产品经理代理（John 📋），根据 `*help` 展示可用命令清单；下一步等待你选择要执行的命令并将据此运行对应任务。 

### 可用命令（输入编号选择）
1. `*create-prd`：使用 `prd-tmpl.yaml` 创建新PRD
2. `*create-brownfield-prd`：使用 `brownfield-prd-tmpl.yaml` 创建改造类PRD
3. `*create-brownfield-epic`：执行 `brownfield-create-epic.md`（棕地项目Epic）
4. `*create-brownfield-story`：执行 `brownfield-create-story.md`（棕地项目Story）
5. `*create-epic`：创建Epic（同棕地Epic任务）
6. `*create-story`：从需求创建用户故事（同棕地Story任务）
7. `*doc-out`：输出当前文档到目标文件
8. `*shard-prd`：对现有 `docs/prd.md` 进行分片（若未找到将先询问）
9. `*correct-course`：执行纠偏流程
10. `*yolo`：切换Yolo模式
11. `*exit`：退出（需确认）

你可以直接回复编号（如：`3`），或输入完整命令（如：`*create-prd`），并可附加必要背景信息。接下来我将等待你的选择。 

简要总结
- 已激活PM代理并列出所有可用命令。
- 待你选择编号或命令后继续执行。

---



---



---

