<!-- Generated by SpecStory -->

# 查看GitHub项目介绍 (2025-08-12 14:05Z)

_**User**_

联网查看@https://github.com/disler/claude-code-hooks-mastery 输出完整的项目介绍

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Summarizing repo details**

I can summarize the repo details effectively, including purpose, prerequisites, hook lifecycle events, and configurations. I'll mention optional integrations like ElevenLabs and Firecrawl, along with disclaimers and quickstart steps. The tooling in the functions namespace includes web_search, but since the content has been provided already, it's not necessary to call it. The memory features seem general and not relevant to this GitHub repo, so I won’t include those citations.
**Preparing project introduction**

I need to follow the instruction to provide status updates before tool calls and summarize at the end. It’s important to maximize parallel tool calls, but for this case, I think I only need to call interactive-feedback. I might want to ask a brief question about including installation steps or comparison. The critical execution rule reminds me to act immediately if I say I’m doing something. I'll prepare the project introduction, then confirm its details with interactive-feedback just to finalize.
**Confirming project details**

I see that user rules indicate I need to call the MCP interactive-feedback tool when I'm close to completing the user request instead of just finishing up. This means I need an interactive step to confirm before I wrap things up. I also know I should provide status updates before calling tools and summarize at the end. So, I’ll give a brief status update before using interactive-feedback to confirm if they want any specific expansions or changes, while ensuring the content is in Chinese with citations included.
**Structuring project overview**

I need to ensure the project introduction is thorough yet clear, accommodating the user’s request for a complete overview. While I want to keep it concise, I can use headings and bullet points for organization. The developer guidelines state that I can use markdown for formatting, so I'll include '###' headings and bullet lists. I might present code fences only if necessary, like for an example file structure. Lastly, I’ll remember to cite any referenced links in markdown format, including the GitHub link.
**Finalizing project introduction**

I’m ready to include important details like "日志路径" and "阻断 dangerous commands" such as `rm -rf`. While I might mention a sample `UserPromptSubmit` log JSON, it could be too much, so I’ll keep it concise. I need to call the interactive-feedback tool with specific questions about configurations, like migrating Hook settings and generating a Quickstart guide in Chinese. I will keep everything formatted with headings, bullet points, and a code snippet. 

Before the call, I'll provide a brief status update and present the full project introduction. The options for user feedback will ensure clarity and engagement.</details></think>

---

我已整理并撰写了该仓库的完整项目介绍（目标、特性、目录、依赖、8 大 Hook、Sub‑Agents、Meta‑Agent、配置与最佳实践等），下面向你确认是否需要补充安装命令或落地到你的项目。### Claude Code Hooks Mastery 项目介绍
参考仓库：[`disler/claude-code-hooks-mastery`](https://github.com/disler/claude-code-hooks-mastery)

- 这是一个围绕 Claude Code（Anthropic 的本地编码智能体 CLI）的“Hook 与子代理（Sub‑Agents）”深度示例工程，展示如何以确定性或非确定性的方式控制 Claude Code 的行为、治理工具使用安全、增加可观测性，并通过子代理与 Meta‑Agent 构建复杂的多代理工作流。

### 核心价值
- **可控性与合规**：在工具执行前后、用户输入前、对话收尾、会话压缩等关键点插入 Hook，执行审计、阻断危险命令、注入上下文、记录日志。
- **高级工作流**：用 Sub‑Agents 将复杂任务拆解给专门代理，支持链式调用；用 Meta‑Agent 自动生成新代理，快速规模化。
- **工程化最佳实践**：提供 Hook 生命周期、载荷结构与配置样例，以及常见安全策略与提示。

### 主要特性
- **完整的 8 大 Hook 生命周期与载荷演示（含 JSON 结构）**：
  - UserPromptSubmit：用户提交提示词即刻触发。载荷含 prompt、session_id、时间戳。用途：提示词校验、合规记录、上下文注入与安全过滤。
  - PreToolUse：任何工具执行之前触发。载荷含 tool_name、tool_input。用途：阻断危险命令（如 `rm -rf`）、禁止读取 `.env` 等。
  - PostToolUse：工具成功执行后触发。载荷含 tool_name、tool_input、tool_response。用途：结果校验、追加反馈。
  - Notification：Claude Code 发送通知（等待输入等）时触发。用途：可接 TTS（如 “Your agent needs your input”）。
  - Stop：主代理响应结束触发。用途：保证收尾一致性、统一完成消息（可 TTS）。
  - SubagentStop：子代理结束触发。用途：回放“Subagent Complete”之类 TTS。
  - PreCompact：会话压缩前触发。载荷含 trigger（manual/auto）、custom_instructions 等。用途：转录备份、详尽反馈。
  - PostCompact：会话压缩后触发。用途：记录压缩结果、校验会话状态。
- **Sub‑Agents（子代理）要点**：
  - 代理文件是“系统提示词”（System Prompt），不是用户提示词。
  - 信息流：用户 → 主代理 → 子代理 → 主代理 → 用户。子代理不直接与用户对话，且无历史上下文，需要明确、冗余的指令。
  - 通过 `description` 字段告诉主代理“何时/为何调用该子代理”，可包含触发词（如“如果提到 TTS 则使用我”）。
  - 可限制工具权限、独立上下文窗口、专业化任务（调试、评审、跑测等）。
- **Meta‑Agent（生成代理的代理）**：
  - 根据你的描述自动生成新的子代理文件，标准化结构、自动选择工具、对齐最新文档，极大提升“创建大量专用代理”的速度。
- **安全策略与治理**：
  - 在 PreToolUse 中阻断高风险命令（`rm -rf`、curl | sh 等）与敏感文件访问（`.env`）。
  - 在 UserPromptSubmit 前置校验危险/越权请求，先阻断后解释。
  - Stop/PreCompact/PostCompact 保障流程有“完成态”和“可回溯”。

### 目录结构（关键项）
- `.claude/`：项目级 Claude Code 配置与代理/Hook 脚本（示例）
  - `agents/`：子代理定义（Markdown + YAML Frontmatter，定义 name、description、tools、model、颜色等）
  - `settings.json`：Hook 配置与流水线挂载点
  - `hooks/`：Hook 实现（示例以 Python 为主）
- `ai_docs/`：AI 编程原则、说明与文档素材
- `apps/`、`images/`：演示素材与示例
- `.env.sample`、`.mcp.json.sample`：环境变量与 MCP 配置示例
- `README.md`、`CLAUDE.md`：总览与使用说明（含 Hook 生命周期说明与示例）

### 依赖与环境
- 必需：
  - Astral UV：快速 Python 包安装/解析
  - Claude Code：本地 CLI
- 可选集成：
  - ElevenLabs 与其 MCP Server：文本转语音（TTS）
  - Firecrawl MCP Server：网页抓取/爬取
  - OpenAI / Anthropic：作为模型或 TTS 提供方

### 快速上手（概念性）
- 安装 UV 与 Claude Code CLI，克隆仓库并根据 `.env.sample`、`.mcp.json.sample` 配置环境。
- 在 `.claude/settings.json` 中挂载 Hook（示例：UserPromptSubmit）：
```json
"UserPromptSubmit": [
  {
    "hooks": [
      {
        "type": "command",
        "command": "uv run .claude/hooks/user_prompt_submit.py --log-only"
      }
    ]
  }
]
```
- 常用参数：
  - `--log-only`：仅记录日志
  - `--validate`：开启安全校验（阻断危险提示）
  - `--context`：为提示词自动注入项目上下文
- 日志查看（示例）：`cat logs/user_prompt_submit.json | jq '.'`

### 典型演示
- 正常询问：“当前目录有哪些文件？” → 正常记录并处理。
- 危险请求：“rm -rf / --no-preserve-root” → 在 UserPromptSubmit/PreToolUse 阶段被拦截并给出原因。
- 场景化增强：提交“写一个新 API 端点”，Hook 注入“项目：电商 API；标准：REST + OpenAPI 3.0；时间戳：…”作为工程上下文后再交给 Claude 处理。

### 最佳实践
- **流控策略**：前置（UserPromptSubmit）介入提示词质量与安全；PreToolUse 阻断风险；PostToolUse 校验结果；Stop 保证收尾。
- **避免循环**：在 Stop/子代理 Stop 中检查 `stop_hook_active`，避免重复触发。
- **信息冗余**：子代理无历史上下文，System Prompt 需明确详尽；`description` 字段决定主代理何时调用。
- **安全优先**：对高危命令与敏感资源建立黑名单；所有 Hook 都应失败可回退、错误信息清晰。
- **充分测试**：在安全环境验证 Hook，先小范围再扩大。

### 项目状态与信息
- 语言：Python
- 演示/文档：提供完整 Hook 生命周期示例与子代理组织方式
- 贡献者：`disler`，`claude`
- 生态：无发布版（Releases），目标作为示例与参考实现
- Star/Fork（以仓库页面为准）

### 适用人群与场景
- 需要为 Claude Code 引入“可控性/可观测性/合规”的工程团队
- 想要构建“多代理、链式任务、自动生成子代理”的研发团队
- 希望将 TTS、抓取能力（MCP）融入编码代理工作流的开发者

引用链接：[`disler/claude-code-hooks-mastery`](https://github.com/disler/claude-code-hooks-mastery)

- 完整介绍已给出，涵盖目标、特性、目录、依赖、8 个 Hook 机制、Sub‑Agents、Meta‑Agent、配置示例与最佳实践。

- 需要我继续生成中文 Quickstart（包含 UV/CLI 安装与演示命令）、补充 Hook 对照表/流程图，或演示如何迁移到你的 `FoodMagic` 项目吗？

我已整理并撰写了该仓库的完整项目介绍（目标、特性、目录、依赖、8 大 Hook、Sub‑Agents、Meta‑Agent、配置与最佳实践等），下面向你确认是否需要补充安装命令或落地到你的项目。

---

_**User**_

我该如何安装？

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Clarifying installation methods**

According to the Astral UV documentation, the recommended installation command is: `curl -LsSf https://astral.sh/uv/install.sh | sh`. Alternatively, I think "brew install uv" might also be available as "astral-sh/uv/uv" as of 2025, but I shouldn’t assume this is always correct.  

As for "Claude Code," which is Anthropic's new CLI, installation might be via "brew install anthropic/claude/claude" or "pipx install claude." I could also check the repository for the README with specific installation instructions. Let's confirm!
**Finding installation instructions**

I can use "web_search" to find accurate instructions for installing the "Claude Code CLI." While the instructions say to prefer "codebase_search" for code results, in this case, I need the web. I believe I should confirm the installation steps for Astral UV and the Claude Code CLI, including optional MCP servers like ElevenLabs and Firecrawl.  

The user asked, "我该如何安装？" which likely means I should provide concrete steps for installing UV and the Claude CLI, cloning the repo, copying sample config files, setting environment variables, and setting up the .claude directory. I may also suggest running terminal commands directly.
**Setting up interactive feedback**

I see that the workspace path is given, and I could navigate to that directory, but I need to ensure that the commands I run have user approval first. The user specified, "每当您想要询问问题时，请始终调用MCP interactive-feedback," which means I should ask for preferences on methods like using Homebrew, curl scripts, or globally installing via pipx. I think I should use interactive-feedback to help guide their choices.  

I should present two options: an automated install using brew or curl, or let them choose their preferred method. However, the developer instructions suggest I should continue until the query is fully resolved, so I’ll keep the interaction ongoing until I get their input!
**Planning installation steps**

I think it’s important to provide clear installation steps by default while also using interactive feedback for the user to select their preferred open-source dependency installation method. First, I need to check if Homebrew is installed, as many Macs have it pre-installed. If not, I'll propose installing Homebrew.

For installing Astral UV, I can recommend first trying Homebrew with the command `brew install uv`. If that doesn't work, I can provide the official script to install it. After installation, I’ll suggest adding `$HOME/.local/bin` to the PATH.

Next, for the Claude Code CLI, I need to search for accurate commands using a web search for installation instructions, including relevant phrases related to Claude Code. I’ll run multiple searches in parallel to gather the necessary information.
**Clarifying installation steps**

The "Potentially Relevant Websearch Results" provided details but lack specific installation commands. I plan to use parallel web searches to gather accurate installation steps for the Astral UV and Claude Code CLI, plus optional installation for Firecrawl and ElevenLabs MCP servers, with minimal references.

I want to propose creating directories like `/.claude/`, and show how to copy configuration files. I might also suggest running "uv run" for hook scripts. Given the user's question, "我该如何安装？", I can offer two tracks: installing and running the demo repo or integrating into the FoodMagic project. I’ll seek their preference through interactive feedback. Lastly, I need to issue a quick status update before running the web searches for accurate commands.</details></think>

---

我先核对官方安装方式与命令，确保提供给你的步骤在 macOS 上可直接使用，然后给出两条路径（运行示例仓库 vs. 集成到你现有的 `FoodMagic` 项目）的具体命令清单。随后会请你选择偏好的安装方式。

---

<details>
            <summary>Searched web "Astral uv install macOS brew or script site:astral.sh uv install" • **1** results</summary>
        
| Title | URL |
|-------|-----|
| Web Search Results | undefined |

</details>

---

<details>
            <summary>Searched web "Claude Code CLI install macOS anthropic" • **1** results</summary>
        
| Title | URL |
|-------|-----|
| Web Search Results | undefined |

</details>

---

<details>
            <summary>Searched web "ElevenLabs MCP server install" • **1** results</summary>
        
| Title | URL |
|-------|-----|
| Web Search Results | undefined |

</details>

---

<details>
            <summary>Searched web "Firecrawl MCP server install" • **1** results</summary>
        
| Title | URL |
|-------|-----|
| Web Search Results | undefined |

</details>

---

<details>
            <summary>Searched web "site:github.com disler/claude-code-hooks-mastery README install" • **1** results</summary>
        
| Title | URL |
|-------|-----|
| Web Search Results | undefined |

</details>

---

<think><details><summary>Thought Process</summary>**Considering installation options**

The user wants to install the repository 'hooks mastery' but isn’t necessarily looking to integrate it into FoodMagic. I think we should offer two choices: run the demo repo or integrate it with FoodMagic. It’s important to use absolute paths as per the user’s note, but I’m not sure where to clone 'claude-code-hooks-mastery.' Maybe we could suggest the home directory or a workspace? I’ll ask the user if they prefer running the demo alone or integrating, proposing a default path like '/Users/<USER>/lvpd/study'.
**Proposing installation commands**

The developer instructions mention we can propose commands to run, but the user may need to approve them. I’ll need to suggest commands for installing uv and claude, cloning the repo, and setting the environment. Before making tool calls, I must provide status updates. I noticed the previous assistant managed this well with a note in Chinese, which worked as a status update. For my response, I’ll outline installation steps in Chinese and offer two paths: A) 运行示例仓库; B) 集成到 FoodMagic, with detailed steps for macOS darwin 24.6.0.
**Providing installation commands**

I’ll provide commands to install uv and claude CLI via brew or a script. For Node.js installation, I’ll suggest using nvm, but first, I should verify using 'node -v' and 'npm -v' since the user likely has Node on their Mac. Non-interactive flags like '-y' are crucial for commands to avoid prompts. After running 'install.sh', I’ll instruct adding $HOME/.cargo/bin to PATH, and suggest exporting the updated path in /.zshrc since the user’s environment is zsh. Lastly, I need to mention the required 'ANTHROPIC_API_KEY' environment variable.
**Setting up environment for uv**

I need to provide instructions for exporting variables either in .zshrc or using a .env file for the project. I might suggest creating a virtual environment with 'uv venv', as 'uv' typically requires 'uv pip sync'. The repo likely uses Python and might run files in '.claude/hooks/'. While 'uv run' creates an ephemeral environment if requirements or pyproject files are present, I'll set up 'uv venv' and use 'uv pip install -r' as a precaution. However, there's a chance there may not be a 'requirements.txt' file, given the repo's nature. So, I’ll rely on just using 'uv run'.
**Preparing installation options and status updates**

I need to provide status updates before any tool calls. So, I'll start with a brief update: "已核对 uv 与 Claude Code CLI 官方安装方式，整理 macOS 安装 2 方案与一键命令." After that, I’ll call the interactive feedback tool with predefined options for the user.

The options will include:
- A: 运行示例仓库（在 /Users/<USER>/lvpd/study 克隆并启动）
- B: 集成到 FoodMagic（同步 .claude 到 /Users/<USER>/lvpd/study/FoodMagic）
- 只给我命令，不代执行
- 我已安装好，直接配置 Hook
- 其它

I’ll also remind the user about providing the API key and mention the Brew installation steps if it's missing. Finally, I'll prepare a reference list with markdown link anchors.</details></think>

---

我已核对 uv 与 Claude Code CLI 的官方安装方式，整理出 macOS 上两种路径（运行示例仓库 vs 集成到你现有 `FoodMagic`）的最小可行命令。下面给出一步到位的指令清单与可选项，稍后我会请你选择路线并可代为执行。### 两种安装路径（macOS，zsh）

- 一般只需按“方案 A”跑通示例仓库；若要直接在你的项目里用，则选“方案 B”。

### 方案 A：运行示例仓库

1) 安装 Astral uv（2 选 1）
- Homebrew（推荐）:
```bash
brew update && brew install uv jq
uv --version
```
- 官方脚本:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
echo 'export PATH="$HOME/.local/bin:$HOME/.cargo/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
uv --version
```

2) 安装 Claude Code CLI
```bash
npm i -g @anthropic-ai/claude-code
claude --version
```

3) 配置 Anthropic Key（替换成你的真实 key）
```bash
echo 'export ANTHROPIC_API_KEY="<你的key>"' >> ~/.zshrc
source ~/.zshrc
```

4) 克隆并初始化仓库
```bash
cd /Users/<USER>/lvpd/study
git clone https://github.com/disler/claude-code-hooks-mastery.git
cd claude-code-hooks-mastery
[ -f .env.sample ] && cp .env.sample .env
[ -f .mcp.json.sample ] && cp .mcp.json.sample .mcp.json
```

5) 启用最小 Hook（编辑 `.claude/settings.json`，加入片段）
```json
"UserPromptSubmit": [
  {
    "hooks": [
      {
        "type": "command",
        "command": "uv run .claude/hooks/user_prompt_submit.py --log-only"
      }
    ]
  }
]
```

6) 启动与验证
```bash
claude
# 在终端里随便问一个问题
[ -f logs/user_prompt_submit.json ] && cat logs/user_prompt_submit.json | jq '.'
```

### 方案 B：集成到你的 `FoodMagic` 项目

1) 准备依赖（同上：安装 uv、Claude Code CLI、设置 `ANTHROPIC_API_KEY`）

2) 同步 `.claude/` 到项目根
```bash
cd /Users/<USER>/lvpd/study
git clone https://github.com/disler/claude-code-hooks-mastery.git
rsync -av --exclude ".git" claude-code-hooks-mastery/.claude/ /Users/<USER>/lvpd/study/FoodMagic/.claude/
```

3) 在 `FoodMagic` 根配置并启动
- 编辑 `/Users/<USER>/lvpd/study/FoodMagic/.claude/settings.json`，按上面的 Hook 片段启用
```bash
cd /Users/<USER>/lvpd/study/FoodMagic
claude
```
- 执行任意命令后，查看项目根的 `logs/` 输出

### 参考文档
- 项目仓库与说明: [`disler/claude-code-hooks-mastery`](https://github.com/disler/claude-code-hooks-mastery)
- Astral uv 安装文档: [`https://docs.astral.sh`](https://docs.astral.sh)
- Claude Code 文档: [`https://docs.anthropic.com`](https://docs.anthropic.com)

- 我可以按你选择的方案，直接在你的 Mac 上自动执行第 1–4 步（并保留全部终端输出记录）。

- 变体：若你还没有 Homebrew，也可仅用官方脚本安装 uv；`jq` 仅用于查看 JSON 日志，非必需。

- 如果 `npm i -g` 权限受限，建议先用 nvm 安装 Node 后再执行。

- 如果 `.claude/settings.json` 已存在，按需合并上述 Hook 片段即可。

- 如果需要 ElevenLabs/Firecrawl 等 MCP，可再补充 `.mcp.json` 与对应 API Key。

- 你只需提供 `ANTHROPIC_API_KEY`；其余我都可以代为处理。

- 简要总结
  - 提供了示例仓库跑通（A）与在 `FoodMagic` 集成（B）两套命令。
  - 给出 uv 与 Claude Code CLI 的安装与验证方式。
  - 给出最小 Hook 片段与日志验证方法。

[需要你确认下一步]

- 请选择安装路径并是否授权我代为执行命令（可选）。



---

