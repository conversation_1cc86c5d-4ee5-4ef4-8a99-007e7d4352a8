#!/bin/bash

# 智能自动存档脚本 - 支持读取Claude会话总结

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "$0")/../.." && pwd)"
cd "$PROJECT_ROOT"

# 检查是否是git仓库
if [ ! -d .git ]; then
    echo "警告: 当前目录不是Git仓库，跳过自动存档"
    exit 0
fi

# 检查是否有未提交的变更
if git diff-index --quiet HEAD -- 2>/dev/null && [ -z "$(git ls-files --others --exclude-standard)" ]; then
    echo "没有检测到文件变更，跳过自动存档"
    exit 0
fi

# 获取会话ID
SESSION_ID="${CLAUDE_SESSION_ID:-$(date +%s | tail -c 6)}"

# 获取操作摘要
SUMMARY=""
MODIFIED_COUNT=$(git diff --name-only 2>/dev/null | wc -l | xargs)
ADDED_COUNT=$(git ls-files --others --exclude-standard 2>/dev/null | wc -l | xargs)
DELETED_COUNT=$(git diff --name-only --diff-filter=D 2>/dev/null | wc -l | xargs)

CHANGES=()
[ "$MODIFIED_COUNT" -gt 0 ] && CHANGES+=("${MODIFIED_COUNT}个修改")
[ "$ADDED_COUNT" -gt 0 ] && CHANGES+=("${ADDED_COUNT}个新增")
[ "$DELETED_COUNT" -gt 0 ] && CHANGES+=("${DELETED_COUNT}个删除")

if [ ${#CHANGES[@]} -gt 0 ]; then
    SUMMARY=$(IFS=", "; echo "${CHANGES[*]}")
else
    SUMMARY="文件变更"
fi

# 获取内容概述
CONTENT_SUMMARY=""

# 检查是否存在Claude生成的会话总结文件
SUMMARY_FILE="$PROJECT_ROOT/.claude/.session-summary"
if [ -f "$SUMMARY_FILE" ]; then
    # 读取Claude生成的总结
    CONTENT_SUMMARY=$(cat "$SUMMARY_FILE" | head -1)
    echo "使用Claude会话总结: $CONTENT_SUMMARY"
    # 使用后删除总结文件
    rm -f "$SUMMARY_FILE"
else
    # 使用自动分析
    echo "使用自动分析生成概述..."
    source "$PROJECT_ROOT/.claude/hooks/auto-checkpoint.sh"
    # 这里会继承原脚本的CONTENT_SUMMARY变量
fi

# 如果还是没有概述，使用默认值
[ -z "$CONTENT_SUMMARY" ] && CONTENT_SUMMARY="代码更新"

# 添加所有变更
echo "正在准备自动存档..."
git add -A 2>/dev/null

# 检查是否有内容需要提交
if git diff --cached --quiet 2>/dev/null; then
    echo "没有需要提交的变更"
    exit 0
fi

# 构建提交消息
COMMIT_MSG="checkpoint: 自动存档 [会话:${SESSION_ID}, ${SUMMARY}, ${CONTENT_SUMMARY}]"

# 执行提交
echo "正在创建存档: $COMMIT_MSG"
git commit -m "$COMMIT_MSG" --no-verify 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 自动存档成功"
    echo "------------------------"
    git log -1 --oneline
    echo "------------------------"
else
    echo "⚠️ 自动存档失败"
    exit 1
fi