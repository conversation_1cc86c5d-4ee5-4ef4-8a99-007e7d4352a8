#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.8"
# dependencies = [
#     "openai",
#     "anthropic",
#     "python-dotenv",
# ]
# ///

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path
from typing import Optional

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

def get_git_diff() -> str:
    """获取当前 Git 变更的详细信息"""
    try:
        # 获取文件变更列表
        files_result = subprocess.run(
            ["git", "diff", "--name-status"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=5
        )
        
        # 获取详细的代码变更（限制输出大小）
        diff_result = subprocess.run(
            ["git", "diff", "--no-color"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=5
        )
        
        # 获取暂存的变更
        staged_result = subprocess.run(
            ["git", "diff", "--cached", "--no-color"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=5
        )
        
        # 获取未跟踪的新文件
        untracked_result = subprocess.run(
            ["git", "ls-files", "--others", "--exclude-standard"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=5
        )
        
        # 组合所有信息
        diff_info = []
        
        if files_result.stdout:
            diff_info.append("## File changes:")
            diff_info.append(files_result.stdout)
        
        if untracked_result.stdout:
            diff_info.append("\n## New files:")
            diff_info.append(untracked_result.stdout)
        
        # 限制详细 diff 的长度
        if diff_result.stdout:
            diff_content = diff_result.stdout[:8000]  # 限制在 8K 字符
            if len(diff_result.stdout) > 8000:
                diff_content += "\n... (truncated)"
            diff_info.append("\n## Code changes:")
            diff_info.append(diff_content)
        
        if staged_result.stdout and staged_result.stdout != diff_result.stdout:
            staged_content = staged_result.stdout[:4000]  # 限制在 4K 字符
            if len(staged_result.stdout) > 4000:
                staged_content += "\n... (truncated)"
            diff_info.append("\n## Staged changes:")
            diff_info.append(staged_content)
        
        return "\n".join(diff_info) if diff_info else "Unable to get Git changes"
        
    except Exception as e:
        return f"Failed to get Git info: {str(e)}"


def read_session_transcript(transcript_path: str, max_lines: int = 100) -> str:
    """读取会话转录的最后部分"""
    try:
        if not os.path.exists(transcript_path):
            return ""
        
        lines = []
        with open(transcript_path, 'r', encoding='utf-8', errors='replace') as f:
            for line in f:
                line = line.strip()
                if line:
                    try:
                        data = json.loads(line)
                        # 提取用户消息和助手响应
                        if data.get('type') == 'user_message':
                            content = data.get('content', '')
                            if content:
                                lines.append(f"User: {content[:200]}")  # 限制长度
                        elif data.get('type') == 'assistant_message':
                            content = data.get('content', '')
                            if content:
                                lines.append(f"Assistant: {content[:300]}")  # 限制长度
                    except json.JSONDecodeError:
                        continue
        
        # 返回最后的对话内容
        return "\n".join(lines[-max_lines:]) if lines else ""
        
    except Exception as e:
        return f"Failed to read session transcript: {str(e)}"


def generate_summary_with_openai(git_diff: str, session_context: str) -> Optional[str]:
    """使用 OpenAI API 生成提交摘要"""
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        return None
    
    try:
        from openai import OpenAI
        
        client = OpenAI(api_key=api_key)
        
        # 确保所有字符串都是 ASCII 安全的
        # 移除 git_diff 和 session_context 中的非 ASCII 字符
        safe_git_diff = git_diff[:4000].encode('ascii', errors='replace').decode('ascii')
        safe_session_context = (session_context[-1000:] if session_context else 'No session context')
        safe_session_context = safe_session_context.encode('ascii', errors='replace').decode('ascii')
        
        # 构建提示词
        prompt = f"""Based on the following Git changes and session context, generate a detailed commit summary in Chinese.

Requirements:
1. Summarize the main functionality or content of this modification
2. List key improvements or new features (if any)
3. Clearly state if it involves testing, documentation, configuration, etc.
4. Use professional technical terms
5. Keep it concise but informative (under 200 Chinese characters)
6. Return ONLY the summary text, no quotes or formatting

Git changes (first 4000 chars):
{safe_git_diff}

Session context (last 1000 chars):
{safe_session_context}

Generate the commit summary in Chinese:"""

        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a professional software developer. Generate clear, concise Git commit summaries in Chinese."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=400,
            temperature=0.3
        )
        
        result = response.choices[0].message.content.strip()
        # 清理输出，移除可能的引号
        result = result.strip('"').strip("'").strip()
        # 取第一行
        result = result.split('\n')[0].strip()
        
        return result
        
    except Exception as e:
        print(f"Debug: OpenAI API error: {e}", file=sys.stderr)
        return None


def generate_summary_with_anthropic(git_diff: str, session_context: str) -> Optional[str]:
    """使用 Anthropic API 生成提交摘要"""
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key:
        return None
    
    try:
        from anthropic import Anthropic
        
        client = Anthropic(api_key=api_key)
        
        # 确保所有字符串都是 ASCII 安全的
        # 移除 git_diff 和 session_context 中的非 ASCII 字符
        safe_git_diff = git_diff[:4000].encode('ascii', errors='replace').decode('ascii')
        safe_session_context = (session_context[-1000:] if session_context else 'No session context')
        safe_session_context = safe_session_context.encode('ascii', errors='replace').decode('ascii')
        
        # 构建提示词
        prompt = f"""Based on the following Git changes and session context, generate a detailed commit summary in Chinese.

Requirements:
1. Summarize the main functionality or content of this modification
2. List key improvements or new features (if any)
3. Clearly state if it involves testing, documentation, configuration, etc.
4. Use professional technical terms
5. Keep it concise but informative (under 200 Chinese characters)
6. Return ONLY the summary text, no quotes or formatting

Git changes (first 4000 chars):
{safe_git_diff}

Session context (last 1000 chars):
{safe_session_context}

Generate the commit summary in Chinese:"""

        response = client.messages.create(
            model="claude-3-haiku-20240307",
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=400,
            temperature=0.3
        )
        
        result = response.content[0].text.strip()
        # 清理输出，移除可能的引号
        result = result.strip('"').strip("'").strip()
        # 取第一行
        result = result.split('\n')[0].strip()
        
        return result
        
    except Exception as e:
        print(f"Debug: Anthropic API error: {e}", file=sys.stderr)
        return None


def generate_fallback_summary(git_diff: str) -> str:
    """生成简单的后备摘要"""
    lines = git_diff.split('\n')
    
    # 统计文件类型
    modified_files = []
    new_files = []
    
    for line in lines:
        if line.startswith('M\t'):
            modified_files.append(line[2:])
        elif line.startswith('A\t'):
            new_files.append(line[2:])
    
    # 识别主要修改类型
    summary_parts = []
    
    # 检查特定类型的文件
    all_files = modified_files + new_files
    
    if any('test' in f.lower() or 'spec' in f.lower() for f in all_files):
        summary_parts.append("测试更新")
    
    if any(f.endswith('.md') for f in all_files):
        summary_parts.append("文档更新")
    
    if any('hook' in f.lower() for f in all_files):
        summary_parts.append("Hook配置更新")
    
    if any(f.endswith(('.java', '.kt')) for f in all_files):
        summary_parts.append("Java/Kotlin代码更新")
    
    if any(f.endswith(('.ts', '.tsx', '.js', '.jsx')) for f in all_files):
        summary_parts.append("前端代码更新")
    
    if any(f.endswith('.py') for f in all_files):
        summary_parts.append("Python脚本更新")
    
    if not summary_parts:
        summary_parts.append("代码更新")
    
    return " | ".join(summary_parts[:3])  # 最多3个描述


def main():
    # 设置 UTF-8 环境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    parser = argparse.ArgumentParser(description='生成智能的 Git 提交摘要')
    parser.add_argument('--transcript', help='会话转录文件路径')
    parser.add_argument('--session-id', help='会话 ID')
    parser.add_argument('--output', help='输出文件路径', 
                       default='.claude/.session-summary')
    args = parser.parse_args()
    
    # 获取 Git 变更信息
    git_diff = get_git_diff()
    
    # 读取会话上下文
    session_context = ""
    if args.transcript:
        session_context = read_session_transcript(args.transcript)
    
    # 尝试使用 LLM 生成摘要
    summary = None
    
    # 优先使用 OpenAI
    if os.getenv('OPENAI_API_KEY'):
        summary = generate_summary_with_openai(git_diff, session_context)
    
    # 其次使用 Anthropic
    if not summary and os.getenv('ANTHROPIC_API_KEY'):
        summary = generate_summary_with_anthropic(git_diff, session_context)
    
    # 后备方案
    if not summary:
        summary = generate_fallback_summary(git_diff)
    
    # 确保输出目录存在
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 写入摘要文件（确保使用 UTF-8）
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    # 打印摘要（确保编码正确）
    try:
        print(f"摘要已生成: {summary}")
    except UnicodeEncodeError:
        print(f"摘要已生成: {summary.encode('utf-8', errors='replace').decode('utf-8')}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())