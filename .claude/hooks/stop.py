#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "python-dotenv",
# ]
# ///

import argparse
import json
import os
import sys
import random
import subprocess
import platform
import shutil
from pathlib import Path
from datetime import datetime

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # dotenv is optional


def get_completion_messages():
    """Return list of friendly completion messages."""
    return [
        "Work complete!",
        "All done!",
        "Task finished!",
        "Job complete!",
        "Ready for next task!"
    ]


def get_tts_script_path():
    """
    Determine which TTS script to use based on available APIs/OS.
    Priority: macOS 'say' > ElevenLabs > pyttsx3
    """
    # Get current script directory and construct utils/tts path
    script_dir = Path(__file__).parent
    tts_dir = script_dir / "utils" / "tts"

    # Prefer macOS native 'say' if available
    try:
        if platform.system() == "Darwin":
            say_path = shutil.which("say")
            if say_path:
                return str(say_path)
    except Exception:
        pass  # ignore detection errors and fallback

    # Check for ElevenLabs API key (next priority)
    if os.getenv('ELEVENLABS_API_KEY'):
        elevenlabs_script = tts_dir / "elevenlabs_tts.py"
        if elevenlabs_script.exists():
            return str(elevenlabs_script)

    # Fall back to pyttsx3 (no API key required)
    pyttsx3_script = tts_dir / "pyttsx3_tts.py"
    if pyttsx3_script.exists():
        return str(pyttsx3_script)

    return None


def get_llm_completion_message():
    """
    Generate completion message using local predefined messages by default.

    Returns:
        str: A random completion message from predefined list
    """
    # Default to local messages to avoid network latency
    messages = get_completion_messages()
    return random.choice(messages)

def announce_completion():
    """Announce completion using the fastest available TTS method."""
    try:
        # Get completion message (local random message by default)
        completion_message = get_llm_completion_message()

        # Prefer macOS native 'say' if available to minimize latency
        try:
            if platform.system() == "Darwin":
                say_path = shutil.which("say")
                if say_path:
                    subprocess.run([say_path, completion_message], capture_output=True, timeout=10)
                    return
        except Exception:
            # If detection or invocation fails, fallback to script-based TTS
            pass

        # Fallback to script-based TTS
        tts_script = get_tts_script_path()
        if not tts_script:
            return  # No TTS available

        # If get_tts_script_path returned 'say' path, call it directly
        if os.path.basename(tts_script) == "say" or tts_script.endswith("/say"):
            subprocess.run([tts_script, completion_message], capture_output=True, timeout=10)
        else:
            subprocess.run(["uv", "run", tts_script, completion_message], capture_output=True, timeout=10)

    except (subprocess.TimeoutExpired, subprocess.SubprocessError, FileNotFoundError):
        # Fail silently if TTS encounters issues
        pass
    except Exception:
        # Fail silently for any other errors
        pass


def main():
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser()
        parser.add_argument('--chat', action='store_true', help='Copy transcript to chat.json')
        args = parser.parse_args()
        
        # Read JSON input from stdin
        input_data = json.load(sys.stdin)

        # Extract required fields
        session_id = input_data.get("session_id", "")
        stop_hook_active = input_data.get("stop_hook_active", False)

        # Ensure log directory exists (in project root, not in .claude)
        project_root = os.getcwd()
        # If we're running from .claude/hooks, go up two levels
        if os.path.basename(os.path.dirname(__file__)) == "hooks":
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        
        log_dir = os.path.join(project_root, "logs")
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, "stop.json")

        # Append to JSONL log with simple rotation at ~10MB
        try:
            jsonl_file = os.path.join(log_dir, "stop.jsonl")

            # Check rotation condition on the default JSONL file
            rotate = False
            if os.path.exists(jsonl_file):
                try:
                    if os.path.getsize(jsonl_file) >= 10 * 1024 * 1024:  # 10MB
                        rotate = True
                except OSError:
                    pass

            target_file = jsonl_file
            if rotate:
                date_str = datetime.now().strftime("%Y-%m-%d")
                target_file = os.path.join(log_dir, f"stop-{date_str}.jsonl")

            with open(target_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(input_data, ensure_ascii=False) + "\n")
        except Exception:
            # Fail silently on logging issues
            pass

        # Handle --chat switch
        if args.chat and 'transcript_path' in input_data:
            transcript_path = input_data['transcript_path']
            if os.path.exists(transcript_path):
                # Read .jsonl file and convert to JSON array
                chat_data = []
                try:
                    with open(transcript_path, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                try:
                                    chat_data.append(json.loads(line))
                                except json.JSONDecodeError:
                                    pass  # Skip invalid lines
                    
                    # Write to logs/chat.json
                    chat_file = os.path.join(log_dir, 'chat.json')
                    with open(chat_file, 'w', encoding='utf-8') as f:
                        json.dump(chat_data, f, indent=2, ensure_ascii=False)
                except Exception:
                    pass  # Fail silently

        # Announce completion via TTS
        announce_completion()
        
        # Generate intelligent commit summary using LLM
        try:
            summary_script = os.path.join(os.path.dirname(__file__), 'utils', 'llm', 'generate_commit_summary.py')
            if os.path.exists(summary_script):
                # Generate summary with session context
                summary_args = ['uv', 'run', summary_script]
                
                # Add transcript path if available
                if 'transcript_path' in input_data:
                    summary_args.extend(['--transcript', input_data['transcript_path']])
                
                # Add session ID
                summary_args.extend(['--session-id', session_id])
                
                # Add explicit output path (relative to project root)
                summary_output = os.path.join(project_root, '.claude', '.session-summary')
                summary_args.extend(['--output', summary_output])
                
                # Run the summary generation script
                if input_data.get('test_mode'):
                    # In test mode, keep blocking behavior for debug output consistency
                    result = subprocess.run(summary_args,
                                  capture_output=True,
                                  text=True,
                                  timeout=15)
                    if result.returncode != 0:
                        print(f"摘要生成失败: {result.stderr}", file=sys.stderr)
                    elif result.stdout:
                        print(f"摘要生成输出: {result.stdout}", file=sys.stderr)
                else:
                    # In normal mode, launch in background to avoid blocking
                    try:
                        with open(os.devnull, 'w') as devnull:
                            subprocess.Popen(
                                summary_args,
                                stdout=devnull,
                                stderr=devnull,
                                start_new_session=True
                            )
                    except Exception as e:
                        # Only surface errors in test mode
                        if input_data.get('test_mode'):
                            print(f"摘要生成后台启动失败: {e}", file=sys.stderr)
        except Exception as e:
            if input_data.get('test_mode'):
                print(f"摘要生成异常: {e}", file=sys.stderr)
        
        # Run Git auto-checkpoint if available (skip in test mode)
        if not input_data.get('test_mode'):
            try:
                checkpoint_script = os.path.join(os.path.dirname(__file__), 'auto-checkpoint-smart.sh')
                if os.path.exists(checkpoint_script):
                    # Set session ID as environment variable
                    env = os.environ.copy()
                    env['CLAUDE_SESSION_ID'] = session_id
                    
                    # Run the checkpoint script in background (non-blocking)
                    try:
                        with open(os.devnull, 'w') as devnull:
                            subprocess.Popen(
                                ['bash', checkpoint_script],
                                stdout=devnull,
                                stderr=devnull,
                                env=env,
                                start_new_session=True
                            )
                    except Exception:
                        # Fail silently (only surface in test mode elsewhere if needed)
                        pass
            except Exception:
                pass  # Fail silently if Git checkpoint fails

        sys.exit(0)

    except json.JSONDecodeError:
        # Handle JSON decode errors gracefully
        sys.exit(0)
    except Exception:
        # Handle any other errors gracefully
        sys.exit(0)


if __name__ == "__main__":
    main()
