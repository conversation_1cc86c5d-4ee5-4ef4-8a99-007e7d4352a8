#!/bin/bash

# 自动存档脚本 - 在Claude Code会话结束时自动提交Git变更

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "$0")/../.." && pwd)"
cd "$PROJECT_ROOT"

# 检查是否是git仓库
if [ ! -d .git ]; then
    echo "警告: 当前目录不是Git仓库，跳过自动存档"
    exit 0
fi

# 检查是否有未提交的变更
if git diff-index --quiet HEAD -- 2>/dev/null && [ -z "$(git ls-files --others --exclude-standard)" ]; then
    echo "没有检测到文件变更，跳过自动存档"
    exit 0
fi

# 获取会话ID（从环境变量或生成一个简短ID）
SESSION_ID="${CLAUDE_SESSION_ID:-$(date +%s | tail -c 6)}"

# 获取操作摘要（分析最近的变更）
SUMMARY=""
MODIFIED_COUNT=$(git diff --name-only 2>/dev/null | wc -l | xargs)
ADDED_COUNT=$(git ls-files --others --exclude-standard 2>/dev/null | wc -l | xargs)
DELETED_COUNT=$(git diff --name-only --diff-filter=D 2>/dev/null | wc -l | xargs)

# 构建摘要信息
CHANGES=()
[ "$MODIFIED_COUNT" -gt 0 ] && CHANGES+=("${MODIFIED_COUNT}个修改")
[ "$ADDED_COUNT" -gt 0 ] && CHANGES+=("${ADDED_COUNT}个新增")
[ "$DELETED_COUNT" -gt 0 ] && CHANGES+=("${DELETED_COUNT}个删除")

if [ ${#CHANGES[@]} -gt 0 ]; then
    SUMMARY=$(IFS=", "; echo "${CHANGES[*]}")
else
    SUMMARY="文件变更"
fi

# 获取本次提交内容概述（分析具体的文件变更）
CONTENT_SUMMARY=""
MODIFIED_FILES=$(git diff --name-only 2>/dev/null)
NEW_FILES=$(git ls-files --others --exclude-standard 2>/dev/null)

# 尝试从git diff中提取更多上下文信息（限制输出避免过长）
DIFF_CONTEXT=""
if command -v git &> /dev/null; then
    # 获取实际代码变更的简短摘要（仅函数/类名）
    DIFF_FUNCTIONS=$(git diff --no-color 2>/dev/null | grep -E "^@@.*@@" | head -3 | sed 's/^.*@@ //' | cut -d' ' -f1 | tr '\n' ' ')
    
    # 检查是否有新增的重要功能
    NEW_FUNCTIONS=$(git diff --no-color 2>/dev/null | grep -E "^\+.*function |^\+.*class |^\+.*def |^\+.*public.*\(" | head -3 | sed 's/^+//' | awk '{print $2}' | tr '\n' ' ')
fi

# 统计文件类型
JAVA_COUNT=0
TS_JS_COUNT=0
PY_COUNT=0
CONFIG_COUNT=0
DOC_COUNT=0
SCRIPT_COUNT=0
OTHER_COUNT=0

# 统计目录变更
CLAUDE_CHANGES=0
DOCS_CHANGES=0
APP_CHANGES=0
WEB_CHANGES=0

# 处理修改的文件和新文件
ALL_FILES="$MODIFIED_FILES $NEW_FILES"

for file in $ALL_FILES; do
    # 获取文件扩展名
    ext="${file##*.}"
    # 获取顶级目录
    dir=$(echo "$file" | cut -d'/' -f1)
    
    # 分类统计文件类型
    case "$ext" in
        java|kt) JAVA_COUNT=$((JAVA_COUNT + 1)) ;;
        ts|tsx|js|jsx) TS_JS_COUNT=$((TS_JS_COUNT + 1)) ;;
        py) PY_COUNT=$((PY_COUNT + 1)) ;;
        yml|yaml|json) CONFIG_COUNT=$((CONFIG_COUNT + 1)) ;;
        md) DOC_COUNT=$((DOC_COUNT + 1)) ;;
        sh|bash) SCRIPT_COUNT=$((SCRIPT_COUNT + 1)) ;;
        *) OTHER_COUNT=$((OTHER_COUNT + 1)) ;;
    esac
    
    # 目录统计
    case "$dir" in
        .claude) CLAUDE_CHANGES=$((CLAUDE_CHANGES + 1)) ;;
        docs) DOCS_CHANGES=$((DOCS_CHANGES + 1)) ;;
        food-magic-app) APP_CHANGES=$((APP_CHANGES + 1)) ;;
        web-bundles) WEB_CHANGES=$((WEB_CHANGES + 1)) ;;
    esac
done

# 构建内容概述
CONTENT_PARTS=()

# 找出主要变更目录
MAX_DIR_COUNT=0
MAX_DIR_NAME=""
if [ $CLAUDE_CHANGES -gt $MAX_DIR_COUNT ]; then
    MAX_DIR_COUNT=$CLAUDE_CHANGES
    MAX_DIR_NAME="Claude配置"
fi
if [ $DOCS_CHANGES -gt $MAX_DIR_COUNT ]; then
    MAX_DIR_COUNT=$DOCS_CHANGES
    MAX_DIR_NAME="文档更新"
fi
if [ $APP_CHANGES -gt $MAX_DIR_COUNT ]; then
    MAX_DIR_COUNT=$APP_CHANGES
    MAX_DIR_NAME="应用代码"
fi
if [ $WEB_CHANGES -gt $MAX_DIR_COUNT ]; then
    MAX_DIR_COUNT=$WEB_CHANGES
    MAX_DIR_NAME="Web包"
fi

[ -n "$MAX_DIR_NAME" ] && CONTENT_PARTS+=("$MAX_DIR_NAME")

# 找出主要文件类型
MAX_TYPE_COUNT=0
MAX_TYPE_NAME=""
if [ $JAVA_COUNT -gt $MAX_TYPE_COUNT ]; then
    MAX_TYPE_COUNT=$JAVA_COUNT
    MAX_TYPE_NAME="Java/Kotlin"
fi
if [ $TS_JS_COUNT -gt $MAX_TYPE_COUNT ]; then
    MAX_TYPE_COUNT=$TS_JS_COUNT
    MAX_TYPE_NAME="TypeScript/JavaScript"
fi
if [ $PY_COUNT -gt $MAX_TYPE_COUNT ]; then
    MAX_TYPE_COUNT=$PY_COUNT
    MAX_TYPE_NAME="Python"
fi
if [ $CONFIG_COUNT -gt $MAX_TYPE_COUNT ]; then
    MAX_TYPE_COUNT=$CONFIG_COUNT
    MAX_TYPE_NAME="配置"
fi
if [ $DOC_COUNT -gt $MAX_TYPE_COUNT ]; then
    MAX_TYPE_COUNT=$DOC_COUNT
    MAX_TYPE_NAME="文档"
fi
if [ $SCRIPT_COUNT -gt $MAX_TYPE_COUNT ]; then
    MAX_TYPE_COUNT=$SCRIPT_COUNT
    MAX_TYPE_NAME="脚本"
fi

[ -n "$MAX_TYPE_NAME" ] && [ "$MAX_TYPE_NAME" != "其他" ] && CONTENT_PARTS+=("$MAX_TYPE_NAME")

# 特殊文件检测和智能识别
if echo "$ALL_FILES" | grep -q "\.claude"; then
    # 如果还没有添加Claude配置
    if [[ ! " ${CONTENT_PARTS[@]} " =~ "Claude配置" ]]; then
        CONTENT_PARTS+=("hooks配置")
    fi
fi

# 更智能的内容识别
if echo "$ALL_FILES" | grep -qi "test\|spec\|\.test\.\|\.spec\."; then
    CONTENT_PARTS+=("测试")
fi

if echo "$ALL_FILES" | grep -qi "docker\|dockerfile\|compose"; then
    CONTENT_PARTS+=("Docker配置")
fi

if echo "$ALL_FILES" | grep -qi "readme\|doc\|\.md$"; then
    [ ! " ${CONTENT_PARTS[@]} " =~ "文档" ] && CONTENT_PARTS+=("文档")
fi

if echo "$ALL_FILES" | grep -qi "package\.json\|pom\.xml\|build\.gradle\|requirements\.txt"; then
    CONTENT_PARTS+=("依赖更新")
fi

if echo "$ALL_FILES" | grep -qi "\.env\|config\|settings\|\.yml\|\.yaml"; then
    [ ! " ${CONTENT_PARTS[@]} " =~ "配置" ] && CONTENT_PARTS+=("配置")
fi

if echo "$ALL_FILES" | grep -qi "auth\|login\|security\|permission"; then
    CONTENT_PARTS+=("认证/安全")
fi

if echo "$ALL_FILES" | grep -qi "api\|controller\|route\|endpoint"; then
    CONTENT_PARTS+=("API")
fi

if echo "$ALL_FILES" | grep -qi "component\|view\|page\|ui\|style\|css"; then
    CONTENT_PARTS+=("UI组件")
fi

if echo "$ALL_FILES" | grep -qi "model\|entity\|schema\|migration"; then
    CONTENT_PARTS+=("数据模型")
fi

if echo "$ALL_FILES" | grep -qi "service\|util\|helper\|lib"; then
    CONTENT_PARTS+=("服务/工具")
fi

# 组合内容概述
if [ ${#CONTENT_PARTS[@]} -gt 0 ]; then
    # 限制最多3个描述
    CONTENT_PARTS=("${CONTENT_PARTS[@]:0:3}")
    CONTENT_SUMMARY=$(IFS=" & "; echo "${CONTENT_PARTS[*]}")
else
    CONTENT_SUMMARY="代码更新"
fi

# 添加所有变更（包括新文件）
echo "正在准备自动存档..."

# 添加所有跟踪的文件变更
git add -A 2>/dev/null

# 检查是否有内容需要提交
if git diff --cached --quiet 2>/dev/null; then
    echo "没有需要提交的变更"
    exit 0
fi

# 构建提交消息
COMMIT_MSG="checkpoint: 自动存档 [会话:${SESSION_ID}, ${SUMMARY}, ${CONTENT_SUMMARY}]"

# 执行提交
echo "正在创建存档: $COMMIT_MSG"
git commit -m "$COMMIT_MSG" --no-verify 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 自动存档成功"
    
    # 显示提交信息
    echo "------------------------"
    git log -1 --oneline
    echo "------------------------"
else
    echo "⚠️ 自动存档失败"
    exit 1
fi