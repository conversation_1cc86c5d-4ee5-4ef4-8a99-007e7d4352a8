#!/bin/bash

# 增强版自动存档脚本 - 支持Claude AI总结
# 使用方式：
# 1. 普通模式：bash auto-checkpoint-with-summary.sh
# 2. 带AI总结：CLAUDE_SUMMARY="实现了用户认证功能" bash auto-checkpoint-with-summary.sh

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "$0")/../.." && pwd)"
cd "$PROJECT_ROOT"

# 检查是否是git仓库
if [ ! -d .git ]; then
    echo "警告: 当前目录不是Git仓库，跳过自动存档"
    exit 0
fi

# 检查是否有未提交的变更
if git diff-index --quiet HEAD -- 2>/dev/null && [ -z "$(git ls-files --others --exclude-standard)" ]; then
    echo "没有检测到文件变更，跳过自动存档"
    exit 0
fi

# 获取会话ID（从环境变量或生成一个简短ID）
SESSION_ID="${CLAUDE_SESSION_ID:-$(date +%s | tail -c 6)}"

# 获取操作摘要（分析最近的变更）
SUMMARY=""
MODIFIED_COUNT=$(git diff --name-only 2>/dev/null | wc -l | xargs)
ADDED_COUNT=$(git ls-files --others --exclude-standard 2>/dev/null | wc -l | xargs)
DELETED_COUNT=$(git diff --name-only --diff-filter=D 2>/dev/null | wc -l | xargs)

# 构建摘要信息
CHANGES=()
[ "$MODIFIED_COUNT" -gt 0 ] && CHANGES+=("${MODIFIED_COUNT}个修改")
[ "$ADDED_COUNT" -gt 0 ] && CHANGES+=("${ADDED_COUNT}个新增")
[ "$DELETED_COUNT" -gt 0 ] && CHANGES+=("${DELETED_COUNT}个删除")

if [ ${#CHANGES[@]} -gt 0 ]; then
    SUMMARY=$(IFS=", "; echo "${CHANGES[*]}")
else
    SUMMARY="文件变更"
fi

# 获取内容概述
CONTENT_SUMMARY=""

# 优先使用Claude提供的总结（如果存在）
if [ -n "$CLAUDE_SUMMARY" ]; then
    CONTENT_SUMMARY="$CLAUDE_SUMMARY"
    echo "使用Claude AI总结: $CONTENT_SUMMARY"
else
    # 使用脚本自动分析
    MODIFIED_FILES=$(git diff --name-only 2>/dev/null)
    NEW_FILES=$(git ls-files --others --exclude-standard 2>/dev/null)
    
    # 这里使用简化版的分析逻辑
    ALL_FILES="$MODIFIED_FILES $NEW_FILES"
    CONTENT_PARTS=()
    
    # 快速识别主要变更类型
    if echo "$ALL_FILES" | grep -q "\.claude"; then
        CONTENT_PARTS+=("Claude配置")
    fi
    if echo "$ALL_FILES" | grep -qi "test\|spec"; then
        CONTENT_PARTS+=("测试")
    fi
    if echo "$ALL_FILES" | grep -qi "auth\|security"; then
        CONTENT_PARTS+=("认证/安全")
    fi
    if echo "$ALL_FILES" | grep -qi "api\|controller"; then
        CONTENT_PARTS+=("API")
    fi
    if echo "$ALL_FILES" | grep -qi "\.md$\|doc"; then
        CONTENT_PARTS+=("文档")
    fi
    
    if [ ${#CONTENT_PARTS[@]} -gt 0 ]; then
        CONTENT_SUMMARY=$(IFS=" & "; echo "${CONTENT_PARTS[*]:0:3}")
    else
        CONTENT_SUMMARY="代码更新"
    fi
fi

# 添加所有变更
echo "正在准备自动存档..."
git add -A 2>/dev/null

# 检查是否有内容需要提交
if git diff --cached --quiet 2>/dev/null; then
    echo "没有需要提交的变更"
    exit 0
fi

# 构建提交消息
COMMIT_MSG="checkpoint: 自动存档 [会话:${SESSION_ID}, ${SUMMARY}, ${CONTENT_SUMMARY}]"

# 执行提交
echo "正在创建存档: $COMMIT_MSG"
git commit -m "$COMMIT_MSG" --no-verify 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ 自动存档成功"
    echo "------------------------"
    git log -1 --oneline
    echo "------------------------"
else
    echo "⚠️ 自动存档失败"
    exit 1
fi