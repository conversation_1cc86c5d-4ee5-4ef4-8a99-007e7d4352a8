Run these 7 sub agent tasks simultaneously in parallel:

1. crypto-coin-analyzer: Analyze DOGE with full technical and sentiment analysis
2. crypto-market-agent: Get current market data for all major cryptocurrencies
3. llm-ai-agents-and-eng-research: Find latest AI agent frameworks and LLM engineering best practices
4. meta-agent: Create a 'security-vulnerability-scanner' agent that scans codebases for OWASP top 10 vulnerabilities
5. meta-agent: Create a 'performance-optimizer' agent that identifies and fixes performance bottlenecks in code
6. meta-agent: Create a 'smart-doc-generator' agent that auto-generates comprehensive documentation from code
7. meta-agent: Create a 'test-coverage-analyzer' agent that analyzes test coverage and suggests missing test cases