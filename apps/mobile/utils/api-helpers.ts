import { useAuthStore } from '../stores/auth.store';

export const DEFAULT_TIMEOUT = 10000;
export const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8080';

/**
 * 创建带超时的fetch包装器
 */
export async function fetchWithTimeout(
  url: string, 
  options: RequestInit = {}, 
  timeout: number = DEFAULT_TIMEOUT
): Promise<Response> {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    
    // 提供更详细的错误信息
    if ((error as Error).name === 'AbortError') {
      throw new Error('请求超时');
    }
    
    // 检查网络连接
    if ((error as Error).message.includes('Network request failed') || 
        (error as Error).message.includes('Failed to fetch')) {
      throw new Error('网络连接失败，请检查您的网络设置');
    }
    
    throw error;
  }
}

/**
 * 构建请求头，包含认证信息
 */
export function buildHeaders(includeAuth: boolean = false): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = useAuthStore.getState().token;
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }
  
  return headers;
}

/**
 * 自定义错误类，提供更好的错误信息
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}