import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { IngredientInput } from './IngredientInput';

describe('IngredientInput', () => {
  const mockOnIngredientsChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with empty ingredients list', () => {
    const { getByPlaceholderText } = render(
      <IngredientInput
        ingredients={[]}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    expect(getByPlaceholderText('输入食材名称...')).toBeTruthy();
  });

  it('should display existing ingredients as tags', () => {
    const ingredients = ['番茄', '鸡蛋', '洋葱'];
    const { getByText } = render(
      <IngredientInput
        ingredients={ingredients}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    ingredients.forEach((ingredient) => {
      expect(getByText(ingredient)).toBeTruthy();
    });
  });

  it('should add ingredient when Add button is pressed', () => {
    const { getByPlaceholderText, getByText } = render(
      <IngredientInput
        ingredients={[]}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    const input = getByPlaceholderText('输入食材名称...');
    const addButton = getByText('添加');

    fireEvent.changeText(input, '番茄');
    fireEvent.press(addButton);

    expect(mockOnIngredientsChange).toHaveBeenCalledWith(['番茄']);
  });

  it('should add ingredient when Enter key is pressed', () => {
    const { getByPlaceholderText } = render(
      <IngredientInput
        ingredients={[]}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    const input = getByPlaceholderText('输入食材名称...');
    
    fireEvent.changeText(input, '鸡蛋');
    fireEvent(input, 'onSubmitEditing');

    expect(mockOnIngredientsChange).toHaveBeenCalledWith(['鸡蛋']);
  });

  it('should not add duplicate ingredients', () => {
    const { getByPlaceholderText, getByText } = render(
      <IngredientInput
        ingredients={['番茄']}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    const input = getByPlaceholderText('输入食材名称...');
    const addButton = getByText('添加');

    fireEvent.changeText(input, '番茄');
    fireEvent.press(addButton);

    expect(mockOnIngredientsChange).not.toHaveBeenCalled();
  });

  it('should not add empty ingredients', () => {
    const { getByPlaceholderText, getByText } = render(
      <IngredientInput
        ingredients={[]}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    const input = getByPlaceholderText('输入食材名称...');
    const addButton = getByText('添加');

    fireEvent.changeText(input, '  ');
    fireEvent.press(addButton);

    expect(mockOnIngredientsChange).not.toHaveBeenCalled();
  });

  it('should remove ingredient when delete button is pressed', () => {
    const ingredients = ['番茄', '鸡蛋'];
    const { getAllByTestId } = render(
      <IngredientInput
        ingredients={ingredients}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    const deleteButtons = getAllByTestId('delete-ingredient');
    fireEvent.press(deleteButtons[0]);

    expect(mockOnIngredientsChange).toHaveBeenCalledWith(['鸡蛋']);
  });

  it('should respect maxIngredients limit', () => {
    const maxIngredients = 2;
    const ingredients = ['番茄', '鸡蛋'];
    const { getByPlaceholderText, getByText } = render(
      <IngredientInput
        ingredients={ingredients}
        onIngredientsChange={mockOnIngredientsChange}
        maxIngredients={maxIngredients}
      />
    );

    const input = getByPlaceholderText('输入食材名称...');
    const addButton = getByText('添加');

    expect(input.props.editable).toBe(false);
    expect(addButton.props.disabled).toBe(true);
    expect(getByText(`已达到最大食材数量限制 (${maxIngredients})`)).toBeTruthy();
  });

  it('should trim whitespace from input', () => {
    const { getByPlaceholderText, getByText } = render(
      <IngredientInput
        ingredients={[]}
        onIngredientsChange={mockOnIngredientsChange}
      />
    );

    const input = getByPlaceholderText('输入食材名称...');
    const addButton = getByText('添加');

    fireEvent.changeText(input, '  番茄  ');
    fireEvent.press(addButton);

    expect(mockOnIngredientsChange).toHaveBeenCalledWith(['番茄']);
  });
});