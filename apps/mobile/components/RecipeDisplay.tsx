import React, { useState } from 'react';
import { AccessibilityInfo } from 'react-native';
import {
  YStack,
  XStack,
  Text,
  Card,
  H2,
  H3,
  Separator,
  AnimatePresence,
  Button,
} from 'tamagui';
import {
  Clock,
  Users,
  Flame,
  ChefHat,
  CheckCircle,
  Circle,
} from '@tamagui/lucide-icons';
import { RecipeDto } from '@foodmagic/shared-types';

interface RecipeDisplayProps {
  recipe: RecipeDto;
}

const DifficultyBadge = ({ difficulty }: { difficulty: string }) => {
  const difficultyConfig = {
    EASY: { label: '简单', color: '$green9', icon: '🌱' },
    MEDIUM: { label: '中等', color: '$orange9', icon: '🔥' },
    HARD: { label: '困难', color: '$red9', icon: '🌶️' },
  };

  const config = difficultyConfig[difficulty as keyof typeof difficultyConfig] || difficultyConfig.EASY;

  return (
    <XStack
      backgroundColor="$backgroundHover"
      paddingHorizontal="$3"
      paddingVertical="$2"
      borderRadius="$4"
      alignItems="center"
      space="$1"
      accessibilityRole="text"
      accessibilityLabel={`难度：${config.label}`}
    >
      <Text fontSize="$3">{config.icon}</Text>
      <Text fontSize="$3" color={config.color} fontWeight="600">
        {config.label}
      </Text>
    </XStack>
  );
};

const MetadataItem = ({ icon: Icon, label, value, accessibilityLabel }: any) => (
  <XStack
    alignItems="center"
    space="$2"
    accessibilityRole="text"
    accessibilityLabel={accessibilityLabel}
  >
    <Icon size={20} color="$orange10" />
    <Text fontSize="$3" color="$gray11">
      {label}:
    </Text>
    <Text fontSize="$3" fontWeight="600">
      {value}
    </Text>
  </XStack>
);

export default function RecipeDisplay({ recipe }: RecipeDisplayProps) {
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  const toggleStep = (stepIndex: number) => {
    const newCompleted = new Set(completedSteps);
    if (newCompleted.has(stepIndex)) {
      newCompleted.delete(stepIndex);
      AccessibilityInfo.announceForAccessibility(`步骤 ${stepIndex + 1} 标记为未完成`);
    } else {
      newCompleted.add(stepIndex);
      AccessibilityInfo.announceForAccessibility(`步骤 ${stepIndex + 1} 已完成`);
    }
    setCompletedSteps(newCompleted);
  };

  return (
    <YStack space="$4" animation="quick" enterStyle={{ opacity: 0, y: 20 }}>
      {/* 标题和描述 */}
      <Card
        padded
        bordered
        animation="quick"
        backgroundColor="$backgroundHover"
        borderColor="$borderColor"
        borderRadius="$6"
        accessibilityRole="header"
        elevation={2}
      >
        <YStack space="$3">
          <H2
            fontSize="$8"
            fontWeight="bold"
            color="$orange11"
            textAlign="center"
            accessibilityRole="heading"
            accessibilityLevel={2}
          >
            {recipe.title}
          </H2>
          {recipe.description && (
            <Text
              fontSize="$4"
              color="$gray11"
              textAlign="center"
              lineHeight="$5"
              accessibilityRole="text"
            >
              {recipe.description}
            </Text>
          )}
        </YStack>
      </Card>

      {/* 元数据 */}
      <Card
        padded
        bordered
        backgroundColor="$background"
        borderColor="$borderColor"
        borderRadius="$4"
      >
        <XStack flexWrap="wrap" gap="$3" justifyContent="space-between">
          <DifficultyBadge difficulty={recipe.difficulty} />
          <MetadataItem
            icon={Clock}
            label="烹饪时间"
            value={`${recipe.cookingTimeMinutes}分钟`}
            accessibilityLabel={`烹饪时间：${recipe.cookingTimeMinutes}分钟`}
          />
          <MetadataItem
            icon={Users}
            label="份量"
            value={`${recipe.servings}人份`}
            accessibilityLabel={`份量：${recipe.servings}人份`}
          />
          {recipe.caloriesPerServing && (
            <MetadataItem
              icon={Flame}
              label="热量"
              value={`${recipe.caloriesPerServing}千卡/份`}
              accessibilityLabel={`每份热量：${recipe.caloriesPerServing}千卡`}
            />
          )}
        </XStack>
      </Card>

      {/* 食材列表 */}
      <Card
        padded
        bordered
        backgroundColor="$background"
        borderColor="$borderColor"
        borderRadius="$4"
      >
        <YStack space="$3">
          <XStack alignItems="center" space="$2">
            <ChefHat size={24} color="$orange10" />
            <H3
              fontSize="$6"
              fontWeight="600"
              accessibilityRole="heading"
              accessibilityLevel={3}
            >
              所需食材
            </H3>
          </XStack>
          <Separator />
          <YStack space="$2">
            {recipe.ingredients.map((ingredient, index) => (
              <XStack
                key={index}
                backgroundColor="$backgroundHover"
                padding="$3"
                borderRadius="$3"
                justifyContent="space-between"
                alignItems="center"
                animation="quick"
                hoverStyle={{ scale: 1.02 }}
                pressStyle={{ scale: 0.98 }}
                accessibilityRole="text"
                accessibilityLabel={`${ingredient.name}：${ingredient.quantity}`}
              >
                <Text fontSize="$4" color="$gray12">
                  {ingredient.name}
                </Text>
                <Text fontSize="$4" fontWeight="600" color="$orange10">
                  {ingredient.quantity}
                </Text>
              </XStack>
            ))}
          </YStack>
        </YStack>
      </Card>

      {/* 烹饪步骤 */}
      <Card
        padded
        bordered
        backgroundColor="$background"
        borderColor="$borderColor"
        borderRadius="$4"
      >
        <YStack space="$3">
          <H3
            fontSize="$6"
            fontWeight="600"
            accessibilityRole="heading"
            accessibilityLevel={3}
          >
            烹饪步骤
          </H3>
          <Separator />
          <YStack space="$3">
            {recipe.instructions.map((instruction, index) => {
              const isCompleted = completedSteps.has(index);
              const timerInfo = recipe.timers?.find(t => t.step === index + 1);
              
              return (
                <AnimatePresence key={index}>
                  <Card
                    padded
                    bordered={!isCompleted}
                    backgroundColor={isCompleted ? '$green2' : '$backgroundHover'}
                    borderColor={isCompleted ? '$green6' : '$borderColor'}
                    borderRadius="$4"
                    animation="quick"
                    enterStyle={{ opacity: 0, x: -20 }}
                    exitStyle={{ opacity: 0, x: 20 }}
                    pressStyle={{ scale: 0.98 }}
                    onPress={() => toggleStep(index)}
                    accessibilityRole="button"
                    accessibilityLabel={`步骤 ${index + 1}：${instruction.substring(0, 50)}${
                      instruction.length > 50 ? '...' : ''
                    }。${isCompleted ? '已完成' : '未完成'}。点击切换完成状态`}
                  >
                    <XStack space="$3" alignItems="flex-start">
                      <Button
                        size="$3"
                        circular
                        chromeless
                        icon={isCompleted ? CheckCircle : Circle}
                        color={isCompleted ? '$green10' : '$gray10'}
                        onPress={() => toggleStep(index)}
                        accessibilityLabel={`标记步骤 ${index + 1} 为${
                          isCompleted ? '未完成' : '已完成'
                        }`}
                      />
                      <YStack flex={1} space="$2">
                        <Text
                          fontSize="$3"
                          fontWeight="600"
                          color={isCompleted ? '$green11' : '$orange10'}
                        >
                          步骤 {index + 1}
                        </Text>
                        <Text
                          fontSize="$4"
                          color={isCompleted ? '$gray10' : '$gray12'}
                          lineHeight="$5"
                          textDecorationLine={isCompleted ? 'line-through' : 'none'}
                        >
                          {instruction}
                        </Text>
                        {timerInfo && (
                          <XStack alignItems="center" space="$1" marginTop="$2">
                            <Clock size={16} color="$blue10" />
                            <Text fontSize="$3" color="$blue10">
                              计时：{Math.floor(timerInfo.seconds / 60)}分钟
                              {timerInfo.seconds % 60 > 0 && `${timerInfo.seconds % 60}秒`}
                            </Text>
                          </XStack>
                        )}
                      </YStack>
                    </XStack>
                  </Card>
                </AnimatePresence>
              );
            })}
          </YStack>
        </YStack>
      </Card>

      {/* 标签 */}
      {recipe.tags && recipe.tags.length > 0 && (
        <Card
          padded
          bordered
          backgroundColor="$backgroundHover"
          borderColor="$borderColor"
          borderRadius="$4"
        >
          <XStack flexWrap="wrap" gap="$2">
            {recipe.tags.map((tag, index) => (
              <XStack
                key={index}
                backgroundColor="$orange3"
                paddingHorizontal="$3"
                paddingVertical="$2"
                borderRadius="$10"
                accessibilityRole="text"
                accessibilityLabel={`标签：${tag}`}
              >
                <Text fontSize="$2" color="$orange11" fontWeight="500">
                  #{tag}
                </Text>
              </XStack>
            ))}
          </XStack>
        </Card>
      )}
    </YStack>
  );
}