import React, { useState, useEffect } from 'react';
import { AccessibilityInfo } from 'react-native';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  YStack,
  XStack,
  Text,
  Card,
  H2,
  H3,
  Separator,
  AnimatePresence,
  Button,
  Sheet,
} from 'tamagui';
import {
  Clock,
  Users,
  Flame,
  ChefHat,
  CheckCircle,
  Circle,
  Heart,
} from '@tamagui/lucide-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  interpolate,
} from 'react-native-reanimated';
import { RecipeDto } from '@foodmagic/shared-types';
import { useAuthStore } from '../stores/auth.store';
import { useRecipeStore } from '../stores/recipe.store';

interface RecipeDisplayProps {
  recipe: RecipeDto;
}

const DifficultyBadge = ({ difficulty }: { difficulty: string }) => {
  const difficultyConfig = {
    EASY: { label: '简单', color: '$green9', icon: '🌱' },
    MEDIUM: { label: '中等', color: '$orange9', icon: '🔥' },
    HARD: { label: '困难', color: '$red9', icon: '🌶️' },
  };

  const config = difficultyConfig[difficulty as keyof typeof difficultyConfig] || difficultyConfig.EASY;

  return (
    <XStack
      backgroundColor="$backgroundHover"
      paddingHorizontal="$3"
      paddingVertical="$2"
      borderRadius="$4"
      alignItems="center"
      space="$1"
      accessibilityRole="text"
      accessibilityLabel={`难度：${config.label}`}
    >
      <Text fontSize="$3">{config.icon}</Text>
      <Text fontSize="$3" color={config.color} fontWeight="600">
        {config.label}
      </Text>
    </XStack>
  );
};

const MetadataItem = ({ icon: Icon, label, value, accessibilityLabel }: any) => (
  <XStack
    alignItems="center"
    space="$2"
    accessibilityRole="text"
    accessibilityLabel={accessibilityLabel}
  >
    <Icon size={20} color="$orange10" />
    <Text fontSize="$3" color="$gray11">
      {label}:
    </Text>
    <Text fontSize="$3" fontWeight="600">
      {value}
    </Text>
  </XStack>
);

const AnimatedButton = Animated.createAnimatedComponent(Button);

const FavoriteButton = ({ recipeId, isSaved, onToggle }: {
  recipeId: string;
  isSaved: boolean;
  onToggle: () => void;
}) => {
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
    ],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withSpring(1.2, { damping: 2, stiffness: 150 }),
      withSpring(1, { damping: 2, stiffness: 150 })
    );
    
    if (!isSaved) {
      rotation.value = withSequence(
        withSpring(-10, { damping: 2, stiffness: 150 }),
        withSpring(10, { damping: 2, stiffness: 150 }),
        withSpring(0, { damping: 2, stiffness: 150 })
      );
    }
    
    onToggle();
  };

  return (
    <AnimatedButton
      style={animatedStyle}
      size="$5"
      circular
      chromeless
      icon={Heart}
      color={isSaved ? '$red10' : '$gray8'}
      backgroundColor={isSaved ? '$red2' : 'transparent'}
      onPress={handlePress}
      accessibilityRole="button"
      accessibilityLabel={isSaved ? '取消收藏' : '收藏食谱'}
      accessibilityState={{ selected: isSaved }}
    />
  );
};

export default function RecipeDisplay({ recipe }: RecipeDisplayProps) {
  const router = useRouter();
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [showAuthModal, setShowAuthModal] = useState(false);
  const { isAuthenticated, user } = useAuthStore();
  const { toggleFavorite, isFavorite, checkFavoriteStatus } = useRecipeStore();
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    if (isAuthenticated && recipe.id) {
      checkFavoriteStatus(recipe.id).then((status) => {
        setIsSaved(status);
      });
    }
  }, [isAuthenticated, recipe.id]);

  const handleFavoriteToggle = async () => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    if (!recipe?.id) {
      console.error('Recipe ID is missing');
      return;
    }

    try {
      const newStatus = await toggleFavorite(recipe.id);
      setIsSaved(newStatus);
      
      // 触觉反馈
      await Haptics.impactAsync(
        newStatus ? Haptics.ImpactFeedbackStyle.Medium : Haptics.ImpactFeedbackStyle.Light
      );
      
      // 无障碍通知
      AccessibilityInfo.announceForAccessibility(
        newStatus ? '食谱已收藏' : '已取消收藏'
      );
    } catch (error) {
      console.error('收藏操作失败:', error);
      // 恢复到原状态
      setIsSaved(!isSaved);
      // 通知用户操作失败
      AccessibilityInfo.announceForAccessibility('收藏操作失败，请稍后重试');
    }
  };

  const toggleStep = (stepIndex: number) => {
    const newCompleted = new Set(completedSteps);
    if (newCompleted.has(stepIndex)) {
      newCompleted.delete(stepIndex);
      AccessibilityInfo.announceForAccessibility(`步骤 ${stepIndex + 1} 标记为未完成`);
    } else {
      newCompleted.add(stepIndex);
      AccessibilityInfo.announceForAccessibility(`步骤 ${stepIndex + 1} 已完成`);
    }
    setCompletedSteps(newCompleted);
  };

  return (
    <YStack space="$4" animation="quick" enterStyle={{ opacity: 0, y: 20 }}>
      {/* 标题和描述 */}
      <Card
        padded
        bordered
        animation="quick"
        backgroundColor="$backgroundHover"
        borderColor="$borderColor"
        borderRadius="$6"
        accessibilityRole="header"
        elevation={2}
      >
        <YStack space="$3">
          <XStack justifyContent="space-between" alignItems="flex-start">
            <YStack flex={1} paddingRight="$3">
              <H2
                fontSize="$8"
                fontWeight="bold"
                color="$orange11"
                textAlign="center"
                accessibilityRole="heading"
                accessibilityLevel={2}
              >
                {recipe.title}
              </H2>
            </YStack>
            <FavoriteButton
              recipeId={recipe.id}
              isSaved={isSaved}
              onToggle={handleFavoriteToggle}
            />
          </XStack>
          {recipe.description && (
            <Text
              fontSize="$4"
              color="$gray11"
              textAlign="center"
              lineHeight="$5"
              accessibilityRole="text"
            >
              {recipe.description}
            </Text>
          )}
        </YStack>
      </Card>

      {/* 元数据 */}
      <Card
        padded
        bordered
        backgroundColor="$background"
        borderColor="$borderColor"
        borderRadius="$4"
      >
        <XStack flexWrap="wrap" gap="$3" justifyContent="space-between">
          <DifficultyBadge difficulty={recipe.difficulty} />
          <MetadataItem
            icon={Clock}
            label="烹饪时间"
            value={`${recipe.cookingTimeMinutes}分钟`}
            accessibilityLabel={`烹饪时间：${recipe.cookingTimeMinutes}分钟`}
          />
          <MetadataItem
            icon={Users}
            label="份量"
            value={`${recipe.servings}人份`}
            accessibilityLabel={`份量：${recipe.servings}人份`}
          />
          {recipe.caloriesPerServing && (
            <MetadataItem
              icon={Flame}
              label="热量"
              value={`${recipe.caloriesPerServing}千卡/份`}
              accessibilityLabel={`每份热量：${recipe.caloriesPerServing}千卡`}
            />
          )}
        </XStack>
      </Card>

      {/* 食材列表 */}
      <Card
        padded
        bordered
        backgroundColor="$background"
        borderColor="$borderColor"
        borderRadius="$4"
      >
        <YStack space="$3">
          <XStack alignItems="center" space="$2">
            <ChefHat size={24} color="$orange10" />
            <H3
              fontSize="$6"
              fontWeight="600"
              accessibilityRole="heading"
              accessibilityLevel={3}
            >
              所需食材
            </H3>
          </XStack>
          <Separator />
          <YStack space="$2">
            {recipe.ingredients.map((ingredient, index) => (
              <XStack
                key={index}
                backgroundColor="$backgroundHover"
                padding="$3"
                borderRadius="$3"
                justifyContent="space-between"
                alignItems="center"
                animation="quick"
                hoverStyle={{ scale: 1.02 }}
                pressStyle={{ scale: 0.98 }}
                accessibilityRole="text"
                accessibilityLabel={`${ingredient.name}：${ingredient.quantity}`}
              >
                <Text fontSize="$4" color="$gray12">
                  {ingredient.name}
                </Text>
                <Text fontSize="$4" fontWeight="600" color="$orange10">
                  {ingredient.quantity}
                </Text>
              </XStack>
            ))}
          </YStack>
        </YStack>
      </Card>

      {/* 烹饪步骤 */}
      <Card
        padded
        bordered
        backgroundColor="$background"
        borderColor="$borderColor"
        borderRadius="$4"
      >
        <YStack space="$3">
          <H3
            fontSize="$6"
            fontWeight="600"
            accessibilityRole="heading"
            accessibilityLevel={3}
          >
            烹饪步骤
          </H3>
          <Separator />
          <YStack space="$3">
            {recipe.instructions.map((instruction, index) => {
              const isCompleted = completedSteps.has(index);
              const timerInfo = recipe.timers?.find(t => t.step === index + 1);
              
              return (
                <AnimatePresence key={index}>
                  <Card
                    padded
                    bordered={!isCompleted}
                    backgroundColor={isCompleted ? '$green2' : '$backgroundHover'}
                    borderColor={isCompleted ? '$green6' : '$borderColor'}
                    borderRadius="$4"
                    animation="quick"
                    enterStyle={{ opacity: 0, x: -20 }}
                    exitStyle={{ opacity: 0, x: 20 }}
                    pressStyle={{ scale: 0.98 }}
                    onPress={() => toggleStep(index)}
                    accessibilityRole="button"
                    accessibilityLabel={`步骤 ${index + 1}：${instruction.substring(0, 50)}${
                      instruction.length > 50 ? '...' : ''
                    }。${isCompleted ? '已完成' : '未完成'}。点击切换完成状态`}
                  >
                    <XStack space="$3" alignItems="flex-start">
                      <Button
                        size="$3"
                        circular
                        chromeless
                        icon={isCompleted ? CheckCircle : Circle}
                        color={isCompleted ? '$green10' : '$gray10'}
                        onPress={() => toggleStep(index)}
                        accessibilityLabel={`标记步骤 ${index + 1} 为${
                          isCompleted ? '未完成' : '已完成'
                        }`}
                      />
                      <YStack flex={1} space="$2">
                        <Text
                          fontSize="$3"
                          fontWeight="600"
                          color={isCompleted ? '$green11' : '$orange10'}
                        >
                          步骤 {index + 1}
                        </Text>
                        <Text
                          fontSize="$4"
                          color={isCompleted ? '$gray10' : '$gray12'}
                          lineHeight="$5"
                          textDecorationLine={isCompleted ? 'line-through' : 'none'}
                        >
                          {instruction}
                        </Text>
                        {timerInfo && (
                          <XStack alignItems="center" space="$1" marginTop="$2">
                            <Clock size={16} color="$blue10" />
                            <Text fontSize="$3" color="$blue10">
                              计时：{Math.floor(timerInfo.seconds / 60)}分钟
                              {timerInfo.seconds % 60 > 0 && `${timerInfo.seconds % 60}秒`}
                            </Text>
                          </XStack>
                        )}
                      </YStack>
                    </XStack>
                  </Card>
                </AnimatePresence>
              );
            })}
          </YStack>
        </YStack>
      </Card>

      {/* 标签 */}
      {recipe.tags && recipe.tags.length > 0 && (
        <Card
          padded
          bordered
          backgroundColor="$backgroundHover"
          borderColor="$borderColor"
          borderRadius="$4"
        >
          <XStack flexWrap="wrap" gap="$2">
            {recipe.tags.map((tag, index) => (
              <XStack
                key={index}
                backgroundColor="$orange3"
                paddingHorizontal="$3"
                paddingVertical="$2"
                borderRadius="$10"
                accessibilityRole="text"
                accessibilityLabel={`标签：${tag}`}
              >
                <Text fontSize="$2" color="$orange11" fontWeight="500">
                  #{tag}
                </Text>
              </XStack>
            ))}
          </XStack>
        </Card>
      )}

      {/* 登录引导Modal */}
      <Sheet
        modal
        open={showAuthModal}
        onOpenChange={setShowAuthModal}
        snapPoints={[35]}
        dismissOnSnapToBottom
        animation="medium"
      >
        <Sheet.Overlay animation="lazy" enterStyle={{ opacity: 0 }} exitStyle={{ opacity: 0 }} />
        <Sheet.Frame backgroundColor="$background" borderTopLeftRadius="$6" borderTopRightRadius="$6">
          <Sheet.Handle backgroundColor="$gray8" />
          <YStack padding="$4" space="$4">
            <YStack space="$2" alignItems="center">
              <Heart size={48} color="$orange10" />
              <H3 fontSize="$6" fontWeight="600" textAlign="center">
                登录后即可收藏您喜欢的食谱
              </H3>
              <Text fontSize="$4" color="$gray11" textAlign="center">
                创建账户，收藏美味食谱，随时随地查看
              </Text>
            </YStack>
            <YStack space="$3">
              <Button
                size="$4"
                backgroundColor="$orange10"
                color="white"
                onPress={() => {
                  setShowAuthModal(false);
                  router.push('/(auth)/login');
                }}
                accessibilityRole="button"
                accessibilityLabel="前往登录"
              >
                登录
              </Button>
              <Button
                size="$4"
                chromeless
                color="$orange10"
                onPress={() => {
                  setShowAuthModal(false);
                  router.push('/(auth)/register');
                }}
                accessibilityRole="button"
                accessibilityLabel="前往注册"
              >
                还没有账户？立即注册
              </Button>
            </YStack>
          </YStack>
        </Sheet.Frame>
      </Sheet>
    </YStack>
  );
}