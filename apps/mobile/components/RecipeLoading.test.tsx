import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import { AccessibilityInfo } from 'react-native';
import RecipeLoading from './RecipeLoading';

jest.mock('react-native/Libraries/AccessibilityInfo/AccessibilityInfo', () => ({
  announceForAccessibility: jest.fn(),
}));

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

describe('RecipeLoading', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('应该渲染加载组件', () => {
    const { getByText, getByLabelText } = render(<RecipeLoading />);
    
    expect(getByText('正在为您准备美味...')).toBeTruthy();
    expect(getByLabelText('正在加载食谱')).toBeTruthy();
    expect(getByText('小贴士：好的食谱需要精心准备，请耐心等待')).toBeTruthy();
  });

  it('应该显示厨师帽动画', () => {
    const { getByLabelText } = render(<RecipeLoading />);
    
    expect(getByLabelText('厨师帽动画')).toBeTruthy();
  });

  it('应该在挂载时宣布加载状态', () => {
    render(<RecipeLoading />);
    
    expect(AccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('正在生成食谱，请稍候');
  });

  it('应该循环显示不同的加载消息', async () => {
    const { getByText, rerender } = render(<RecipeLoading />);
    
    // 初始消息
    expect(getByText('正在为您准备美味...')).toBeTruthy();
    
    // 前进到下一个消息
    jest.advanceTimersByTime(2000);
    rerender(<RecipeLoading />);
    
    await waitFor(() => {
      expect(getByText('挑选最佳食谱中...')).toBeTruthy();
    });
    
    // 再前进到下一个消息
    jest.advanceTimersByTime(2000);
    rerender(<RecipeLoading />);
    
    await waitFor(() => {
      expect(getByText('搭配完美调味料...')).toBeTruthy();
    });
  });

  it('应该显示骨架屏', () => {
    const { getAllByTestId, container } = render(<RecipeLoading />);
    
    // 检查是否有Card组件（骨架屏）
    const cards = container.findAllByType('Card');
    expect(cards.length).toBeGreaterThan(0);
  });

  it('加载消息应该循环回到开始', async () => {
    const { getByText, rerender } = render(<RecipeLoading />);
    
    // 循环通过所有消息
    for (let i = 0; i < 5; i++) {
      jest.advanceTimersByTime(2000);
      rerender(<RecipeLoading />);
    }
    
    // 应该回到第一个消息
    await waitFor(() => {
      expect(getByText('正在为您准备美味...')).toBeTruthy();
    });
  });

  it('应该清理定时器', () => {
    const { unmount } = render(<RecipeLoading />);
    
    const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
    
    unmount();
    
    expect(clearIntervalSpy).toHaveBeenCalled();
    
    clearIntervalSpy.mockRestore();
  });

  it('应该有正确的无障碍角色', () => {
    const { getByLabelText } = render(<RecipeLoading />);
    
    const progressBar = getByLabelText('正在加载食谱');
    expect(progressBar.props.accessibilityRole).toBe('progressbar');
  });
});