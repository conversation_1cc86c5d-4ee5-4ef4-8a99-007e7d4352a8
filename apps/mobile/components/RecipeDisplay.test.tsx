import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { AccessibilityInfo } from 'react-native';
import RecipeDisplay from './RecipeDisplay';
import { RecipeDto } from '@foodmagic/shared-types';

jest.mock('react-native/Libraries/AccessibilityInfo/AccessibilityInfo', () => ({
  announceForAccessibility: jest.fn(),
}));

jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'Light',
    Medium: 'Medium',
    Heavy: 'Heavy',
  },
}));

jest.mock('../stores/auth.store', () => ({
  useAuthStore: jest.fn(() => ({
    isAuthenticated: false,
    user: null,
  })),
}));

jest.mock('../stores/recipe.store', () => ({
  useRecipeStore: jest.fn(() => ({
    toggleFavorite: jest.fn(),
    isFavorite: jest.fn(() => false),
    checkFavoriteStatus: jest.fn(() => Promise.resolve(false)),
  })),
}));

describe('RecipeDisplay', () => {
  const mockRecipe: RecipeDto = {
    id: '1',
    title: '番茄炒蛋',
    description: '经典的家常菜，简单又美味',
    imageUrl: null,
    cookingTimeMinutes: 15,
    difficulty: 'EASY',
    servings: 2,
    caloriesPerServing: 200,
    tags: ['家常菜', '快手菜'],
    ingredients: [
      { name: '鸡蛋', quantity: '3个' },
      { name: '番茄', quantity: '2个' },
      { name: '盐', quantity: '适量' },
    ],
    instructions: [
      '将鸡蛋打散，加少许盐调味',
      '番茄切块备用',
      '热锅放油，先炒鸡蛋，炒熟后盛出',
      '锅中再放少许油，下番茄块炒软',
      '加入炒好的鸡蛋，调味后快速翻炒均匀即可',
    ],
    timers: [
      { step: 3, seconds: 120 },
      { step: 4, seconds: 180 },
    ],
    createdAt: '2025-01-14T10:00:00Z',
  };

  it('应该正确渲染食谱标题和描述', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    expect(getByText('番茄炒蛋')).toBeTruthy();
    expect(getByText('经典的家常菜，简单又美味')).toBeTruthy();
  });

  it('应该显示难度标签', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    expect(getByText('简单')).toBeTruthy();
  });

  it('应该显示元数据信息', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    expect(getByText('15分钟')).toBeTruthy();
    expect(getByText('2人份')).toBeTruthy();
    expect(getByText('200千卡/份')).toBeTruthy();
  });

  it('应该渲染所有食材', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    mockRecipe.ingredients.forEach((ingredient) => {
      expect(getByText(ingredient.name)).toBeTruthy();
      expect(getByText(ingredient.quantity)).toBeTruthy();
    });
  });

  it('应该渲染所有烹饪步骤', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    mockRecipe.instructions.forEach((instruction, index) => {
      expect(getByText(`步骤 ${index + 1}`)).toBeTruthy();
      expect(getByText(instruction)).toBeTruthy();
    });
  });

  it('应该显示计时信息', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    expect(getByText('计时：2分钟')).toBeTruthy();
    expect(getByText('计时：3分钟')).toBeTruthy();
  });

  it('应该渲染标签', () => {
    const { getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    expect(getByText('#家常菜')).toBeTruthy();
    expect(getByText('#快手菜')).toBeTruthy();
  });

  it('点击步骤应该切换完成状态', async () => {
    const { getByText, queryByTestId } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    const firstStep = getByText('步骤 1').parent?.parent?.parent;
    
    if (firstStep) {
      fireEvent.press(firstStep);
      
      await waitFor(() => {
        expect(AccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('步骤 1 已完成');
      });
      
      fireEvent.press(firstStep);
      
      await waitFor(() => {
        expect(AccessibilityInfo.announceForAccessibility).toHaveBeenCalledWith('步骤 1 标记为未完成');
      });
    }
  });

  it('应该处理没有描述的食谱', () => {
    const recipeWithoutDescription = {
      ...mockRecipe,
      description: '',
    };
    
    const { queryByText } = render(<RecipeDisplay recipe={recipeWithoutDescription} />);
    
    expect(queryByText('经典的家常菜，简单又美味')).toBeFalsy();
    expect(queryByText('番茄炒蛋')).toBeTruthy();
  });

  it('应该处理没有热量信息的食谱', () => {
    const recipeWithoutCalories = {
      ...mockRecipe,
      caloriesPerServing: null,
    };
    
    const { queryByText } = render(<RecipeDisplay recipe={recipeWithoutCalories} />);
    
    expect(queryByText('200千卡/份')).toBeFalsy();
    expect(queryByText('15分钟')).toBeTruthy();
  });

  it('应该处理没有标签的食谱', () => {
    const recipeWithoutTags = {
      ...mockRecipe,
      tags: null,
    };
    
    const { queryByText } = render(<RecipeDisplay recipe={recipeWithoutTags} />);
    
    expect(queryByText('#家常菜')).toBeFalsy();
    expect(queryByText('#快手菜')).toBeFalsy();
  });

  it('应该处理没有计时器的食谱', () => {
    const recipeWithoutTimers = {
      ...mockRecipe,
      timers: null,
    };
    
    const { queryByText } = render(<RecipeDisplay recipe={recipeWithoutTimers} />);
    
    expect(queryByText('计时：2分钟')).toBeFalsy();
    expect(queryByText('计时：3分钟')).toBeFalsy();
  });

  it('应该正确处理不同难度级别', () => {
    const mediumRecipe = { ...mockRecipe, difficulty: 'MEDIUM' as const };
    const hardRecipe = { ...mockRecipe, difficulty: 'HARD' as const };
    
    const { rerender, getByText } = render(<RecipeDisplay recipe={mediumRecipe} />);
    expect(getByText('中等')).toBeTruthy();
    
    rerender(<RecipeDisplay recipe={hardRecipe} />);
    expect(getByText('困难')).toBeTruthy();
  });

  it('未登录时点击收藏应该显示登录引导', () => {
    const { getByLabelText, getByText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    // 点击收藏按钮
    const favoriteButton = getByLabelText('收藏食谱');
    fireEvent.press(favoriteButton);
    
    // 应该显示登录引导模态框
    expect(getByText('登录后即可收藏您喜欢的食谱')).toBeTruthy();
  });

  it('已登录时点击收藏应该调用toggleFavorite', async () => {
    const mockToggleFavorite = jest.fn().mockResolvedValue(true);
    const useRecipeStore = require('../stores/recipe.store').useRecipeStore;
    const useAuthStore = require('../stores/auth.store').useAuthStore;
    
    useAuthStore.mockReturnValue({
      isAuthenticated: true,
      user: { id: '1', email: '<EMAIL>' },
    });
    
    useRecipeStore.mockReturnValue({
      toggleFavorite: mockToggleFavorite,
      isFavorite: jest.fn(() => false),
      checkFavoriteStatus: jest.fn(() => Promise.resolve(false)),
    });
    
    const { getByLabelText } = render(<RecipeDisplay recipe={mockRecipe} />);
    
    const favoriteButton = getByLabelText('收藏食谱');
    fireEvent.press(favoriteButton);
    
    await waitFor(() => {
      expect(mockToggleFavorite).toHaveBeenCalledWith('1');
    });
  });
});