import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Input,
  XStack,
  YStack,
  Button,
  Text,
  AnimatePresence,
  styled,
} from 'tamagui';
import { X } from '@tamagui/lucide-icons';
import { TextInput } from 'react-native';
import { IngredientAutocomplete } from './IngredientAutocomplete';
import { IngredientService } from '../services/ingredientService';
import { IngredientSuggestionDto } from '@foodmagic/shared-types';

interface IngredientInputWithAutocompleteProps {
  ingredients: string[];
  onIngredientsChange: (ingredients: string[]) => void;
  placeholder?: string;
  maxIngredients?: number;
}

const Tag = styled(XStack, {
  backgroundColor: '$blue5',
  paddingHorizontal: '$3',
  paddingVertical: '$2',
  borderRadius: '$4',
  alignItems: 'center',
  gap: '$2',
  animation: 'quick',
  scale: 1,
  opacity: 1,
  pressStyle: {
    scale: 0.95,
  },
  enterStyle: {
    scale: 0.8,
    opacity: 0,
  },
  exitStyle: {
    scale: 0.8,
    opacity: 0,
  },
});

export const IngredientInputWithAutocomplete: React.FC<IngredientInputWithAutocompleteProps> = ({
  ingredients,
  onIngredientsChange,
  placeholder = '输入食材名称...',
  maxIngredients = 20,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<IngredientSuggestionDto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout>();
  const inputRef = useRef<TextInput>(null);

  // 搜索食材建议（带防抖）
  const searchIngredients = useCallback(async (query: string) => {
    if (query.length < 1) {
      setSuggestions([]);
      setShowAutocomplete(false);
      return;
    }

    setIsLoading(true);
    setShowAutocomplete(true);

    try {
      const results = await IngredientService.searchIngredients(query);
      setSuggestions(results);
    } catch (error) {
      console.error('Failed to search ingredients:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 输入变化时触发搜索（防抖300ms）
  useEffect(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (inputValue.trim()) {
      debounceTimerRef.current = setTimeout(() => {
        searchIngredients(inputValue);
      }, 300);
    } else {
      setSuggestions([]);
      setShowAutocomplete(false);
    }

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [inputValue, searchIngredients]);

  // 添加食材（支持自定义）
  const handleAddIngredient = useCallback((ingredient: string) => {
    const trimmedIngredient = ingredient.trim();
    
    if (
      trimmedIngredient &&
      !ingredients.includes(trimmedIngredient) &&
      ingredients.length < maxIngredients
    ) {
      onIngredientsChange([...ingredients, trimmedIngredient]);
      setInputValue('');
      setSuggestions([]);
      setShowAutocomplete(false);

      // 检查是否为自定义食材（不在建议列表中）
      const isCustom = !suggestions.some(s => s.name === trimmedIngredient);
      if (isCustom && trimmedIngredient !== '') {
        // 记录自定义食材（不阻塞用户流程）
        IngredientService.recordCustomIngredient(trimmedIngredient).catch(() => {
          // 静默处理错误
        });
      }
    }
  }, [ingredients, onIngredientsChange, maxIngredients, suggestions]);

  // 从建议列表选择食材
  const handleSelectSuggestion = useCallback((ingredientName: string) => {
    handleAddIngredient(ingredientName);
    inputRef.current?.focus();
  }, [handleAddIngredient]);

  // 处理键盘事件
  const handleKeyPress = useCallback((e: any) => {
    if (e.nativeEvent.key === 'Enter') {
      e.preventDefault();
      // 如果有建议且输入匹配第一个建议，选择它；否则添加自定义食材
      if (suggestions.length > 0 && suggestions[0].name.toLowerCase() === inputValue.toLowerCase()) {
        handleAddIngredient(suggestions[0].name);
      } else if (inputValue.trim()) {
        handleAddIngredient(inputValue);
      }
    }
  }, [suggestions, inputValue, handleAddIngredient]);

  // 删除食材
  const handleRemoveIngredient = useCallback((index: number) => {
    const newIngredients = ingredients.filter((_, i) => i !== index);
    onIngredientsChange(newIngredients);
  }, [ingredients, onIngredientsChange]);

  // 添加按钮处理
  const handleAddButtonPress = useCallback(() => {
    if (inputValue.trim()) {
      handleAddIngredient(inputValue);
    }
  }, [inputValue, handleAddIngredient]);

  return (
    <YStack gap="$2" width="100%">
      <YStack gap="$3" width="100%">
        {ingredients.length > 0 && (
          <XStack flexWrap="wrap" gap="$2">
            <AnimatePresence>
              {ingredients.map((ingredient, index) => (
                <Tag key={`${ingredient}-${index}`}>
                  <Text fontSize="$3" color="$blue11">
                    {ingredient}
                  </Text>
                  <Button
                    size="$1"
                    circular
                    icon={X}
                    chromeless
                    onPress={() => handleRemoveIngredient(index)}
                    hoverStyle={{ backgroundColor: '$blue6' }}
                    testID="delete-ingredient"
                  />
                </Tag>
              ))}
            </AnimatePresence>
          </XStack>
        )}

        <XStack gap="$2" alignItems="center">
          <Input
            ref={inputRef}
            flex={1}
            value={inputValue}
            onChangeText={setInputValue}
            placeholder={placeholder}
            onSubmitEditing={handleAddButtonPress}
            onKeyPress={handleKeyPress}
            returnKeyType="done"
            disabled={ingredients.length >= maxIngredients}
            editable={ingredients.length < maxIngredients}
          />
          <Button
            onPress={handleAddButtonPress}
            disabled={!inputValue.trim() || ingredients.length >= maxIngredients}
            theme="blue"
          >
            添加
          </Button>
        </XStack>

        {ingredients.length >= maxIngredients && (
          <Text fontSize="$2" color="$orange10">
            已达到最大食材数量限制 ({maxIngredients})
          </Text>
        )}
      </YStack>
      
      <IngredientAutocomplete
        suggestions={suggestions}
        isLoading={isLoading}
        onSelect={handleSelectSuggestion}
        visible={showAutocomplete}
        maxHeight={200}
      />
    </YStack>
  );
};