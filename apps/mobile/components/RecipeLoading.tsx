import React, { useEffect } from 'react';
import { AccessibilityInfo } from 'react-native';
import { YStack, XStack, Text, Card, H2 } from 'tamagui';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
  Easing,
  interpolate,
  withDelay,
} from 'react-native-reanimated';

const AnimatedCard = Animated.createAnimatedComponent(Card);

const LoadingDot = ({ delay = 0 }: { delay?: number }) => {
  const scale = useSharedValue(0);

  useEffect(() => {
    scale.value = withDelay(
      delay,
      withRepeat(
        withSequence(
          withTiming(1, { duration: 600, easing: Easing.ease }),
          withTiming(0.3, { duration: 600, easing: Easing.ease })
        ),
        -1
      )
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: interpolate(scale.value, [0.3, 1], [0.3, 1]),
  }));

  return (
    <Animated.View style={animatedStyle}>
      <YStack
        width={12}
        height={12}
        borderRadius={6}
        backgroundColor="$orange10"
      />
    </Animated.View>
  );
};

const ChefHat = () => {
  const rotation = useSharedValue(0);
  const translateY = useSharedValue(0);

  useEffect(() => {
    rotation.value = withRepeat(
      withSequence(
        withTiming(10, { duration: 1000, easing: Easing.ease }),
        withTiming(-10, { duration: 1000, easing: Easing.ease })
      ),
      -1
    );
    
    translateY.value = withRepeat(
      withSequence(
        withTiming(-10, { duration: 1500, easing: Easing.ease }),
        withTiming(0, { duration: 1500, easing: Easing.ease })
      ),
      -1
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${rotation.value}deg` },
      { translateY: translateY.value },
    ],
  }));

  return (
    <Animated.View style={animatedStyle}>
      <Text fontSize={80} accessibilityRole="image" accessibilityLabel="厨师帽动画">
        👨‍🍳
      </Text>
    </Animated.View>
  );
};

const PulsingCard = ({ children, delay = 0 }: any) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0.3);

  useEffect(() => {
    scale.value = withDelay(
      delay,
      withRepeat(
        withSequence(
          withTiming(1.02, { duration: 1000, easing: Easing.ease }),
          withTiming(1, { duration: 1000, easing: Easing.ease })
        ),
        -1
      )
    );
    
    opacity.value = withDelay(
      delay,
      withRepeat(
        withSequence(
          withTiming(1, { duration: 1000, easing: Easing.ease }),
          withTiming(0.3, { duration: 1000, easing: Easing.ease })
        ),
        -1
      )
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <AnimatedCard
      style={animatedStyle}
      backgroundColor="$orange3"
      borderRadius="$4"
      height={20}
      marginVertical="$2"
    >
      {children}
    </AnimatedCard>
  );
};

const loadingMessages = [
  '正在为您准备美味...',
  '挑选最佳食谱中...',
  '搭配完美调味料...',
  '计算烹饪时间...',
  '优化烹饪步骤...',
];

export default function RecipeLoading() {
  const [messageIndex, setMessageIndex] = React.useState(0);

  useEffect(() => {
    AccessibilityInfo.announceForAccessibility('正在生成食谱，请稍候');
    
    const interval = setInterval(() => {
      setMessageIndex((prev) => (prev + 1) % loadingMessages.length);
    }, 2000);

    return () => {
      clearInterval(interval);
      // 清理动画共享值
      'worklet';
    };
  }, []);

  return (
    <YStack
      flex={1}
      backgroundColor="$background"
      justifyContent="center"
      alignItems="center"
      padding="$4"
      accessibilityRole="progressbar"
      accessibilityLabel="正在加载食谱"
    >
      <YStack space="$6" alignItems="center" width="100%" maxWidth={400}>
        {/* 动画厨师帽 */}
        <ChefHat />

        {/* 加载提示文字 */}
        <YStack space="$2" alignItems="center">
          <H2
            fontSize="$7"
            fontWeight="600"
            color="$orange11"
            textAlign="center"
            animation="quick"
            enterStyle={{ opacity: 0, y: -10 }}
          >
            {loadingMessages[messageIndex]}
          </H2>
          
          {/* 加载点动画 */}
          <XStack space="$2" justifyContent="center">
            <LoadingDot delay={0} />
            <LoadingDot delay={200} />
            <LoadingDot delay={400} />
          </XStack>
        </YStack>

        {/* 骨架屏 */}
        <YStack width="100%" space="$3" marginTop="$4">
          <Card
            padded
            bordered
            backgroundColor="$backgroundHover"
            borderColor="$borderColor"
            borderRadius="$4"
            opacity={0.6}
          >
            <YStack space="$3">
              <PulsingCard delay={0} />
              <PulsingCard delay={100} />
              <PulsingCard delay={200} />
            </YStack>
          </Card>

          <Card
            padded
            bordered
            backgroundColor="$backgroundHover"
            borderColor="$borderColor"
            borderRadius="$4"
            opacity={0.4}
          >
            <YStack space="$2">
              <PulsingCard delay={300} />
              <PulsingCard delay={400} />
            </YStack>
          </Card>
        </YStack>

        {/* 温馨提示 */}
        <Text
          fontSize="$3"
          color="$gray10"
          textAlign="center"
          marginTop="$4"
          animation="quick"
          enterStyle={{ opacity: 0 }}
          opacity={0.8}
        >
          小贴士：好的食谱需要精心准备，请耐心等待
        </Text>
      </YStack>
    </YStack>
  );
}