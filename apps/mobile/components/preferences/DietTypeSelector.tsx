import React from 'react';
import {
  YStack,
  XStack,
  RadioGroup,
  Label,
  Text,
  Tooltip,
} from 'tamagui';
import { Info } from '@tamagui/lucide-icons';
import { DietType } from '@food-magic/shared-types';

interface DietTypeSelectorProps {
  value: DietType;
  onValueChange: (value: DietType) => void;
  disabled?: boolean;
}

const dietTypeInfo: Record<DietType, { label: string; description: string }> = {
  NONE: {
    label: '无特殊饮食',
    description: '没有特定的饮食限制',
  },
  LOW_CARB: {
    label: '低碳水',
    description: '减少碳水化合物摄入，适合控制血糖和减重',
  },
  HIGH_PROTEIN: {
    label: '高蛋白',
    description: '增加蛋白质摄入，适合健身增肌',
  },
  KETO: {
    label: '生酮饮食',
    description: '极低碳水、高脂肪的饮食方式',
  },
  PALEO: {
    label: '原始饮食',
    description: '避免加工食品，回归原始饮食方式',
  },
  MEDITERRANEAN: {
    label: '地中海饮食',
    description: '以橄榄油、鱼类、蔬果为主的健康饮食',
  },
  VEGAN: {
    label: '纯素食',
    description: '完全植物性饮食，不含任何动物产品',
  },
};

export function DietTypeSelector({
  value,
  onValueChange,
  disabled = false,
}: DietTypeSelectorProps) {
  return (
    <YStack space="$3">
      <Text fontSize="$4" fontWeight="600" color="$gray12">
        饮食目标
      </Text>
      <Text fontSize="$2" color="$gray10" marginBottom="$2">
        选择您的饮食目标（单选）
      </Text>
      
      <RadioGroup
        value={value}
        onValueChange={onValueChange}
        disabled={disabled}
        accessibilityLabel="选择饮食目标"
      >
        <YStack space="$2">
          {(Object.keys(dietTypeInfo) as DietType[]).map((dietType) => {
            const info = dietTypeInfo[dietType];
            return (
              <Label
                key={dietType}
                htmlFor={`diet-${dietType}`}
                disabled={disabled}
              >
                <XStack
                  space="$3"
                  alignItems="center"
                  paddingVertical="$2"
                  paddingHorizontal="$3"
                  borderRadius="$3"
                  backgroundColor={value === dietType ? '$orange2' : '$background'}
                  hoverStyle={{
                    backgroundColor: value === dietType ? '$orange3' : '$gray2',
                  }}
                  pressStyle={{
                    backgroundColor: value === dietType ? '$orange3' : '$gray3',
                  }}
                  borderWidth={1}
                  borderColor={value === dietType ? '$orange8' : '$borderColor'}
                >
                  <RadioGroup.Item
                    value={dietType}
                    id={`diet-${dietType}`}
                    size="$3"
                  >
                    <RadioGroup.Indicator />
                  </RadioGroup.Item>
                  
                  <YStack flex={1}>
                    <Text
                      fontSize="$3"
                      fontWeight={value === dietType ? '600' : '400'}
                      color={value === dietType ? '$orange10' : '$gray12'}
                    >
                      {info.label}
                    </Text>
                  </YStack>
                  
                  <Tooltip placement="left">
                    <Tooltip.Trigger>
                      <Info size={16} color="$gray8" />
                    </Tooltip.Trigger>
                    <Tooltip.Content
                      enterStyle={{ opacity: 0, scale: 0.9 }}
                      exitStyle={{ opacity: 0, scale: 0.9 }}
                      animation="quick"
                    >
                      <Tooltip.Arrow />
                      <Text fontSize="$2" maxWidth={200}>
                        {info.description}
                      </Text>
                    </Tooltip.Content>
                  </Tooltip>
                </XStack>
              </Label>
            );
          })}
        </YStack>
      </RadioGroup>
    </YStack>
  );
}