import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { DietTypeSelector } from './DietTypeSelector';
import { DietType } from '@food-magic/shared-types';

describe('DietTypeSelector', () => {
  const mockOnValueChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all diet type options', () => {
    const { getByText } = render(
      <DietTypeSelector
        value="NONE"
        onValueChange={mockOnValueChange}
      />
    );

    expect(getByText('饮食目标')).toBeTruthy();
    expect(getByText('选择您的饮食目标（单选）')).toBeTruthy();
    expect(getByText('无特殊饮食')).toBeTruthy();
    expect(getByText('低碳水')).toBeTruthy();
    expect(getByText('高蛋白')).toBeTruthy();
    expect(getByText('生酮饮食')).toBeTruthy();
    expect(getByText('原始饮食')).toBeTruthy();
    expect(getByText('地中海饮食')).toBeTruthy();
    expect(getByText('纯素食')).toBeTruthy();
  });

  it('should highlight the selected diet type', () => {
    const { getByLabelText } = render(
      <DietTypeSelector
        value="LOW_CARB"
        onValueChange={mockOnValueChange}
      />
    );

    const lowCarbOption = getByLabelText('diet-LOW_CARB');
    expect(lowCarbOption.props.value).toBe('LOW_CARB');
  });

  it('should call onValueChange when a diet type is selected', () => {
    const { getByLabelText } = render(
      <DietTypeSelector
        value="NONE"
        onValueChange={mockOnValueChange}
      />
    );

    const ketoOption = getByLabelText('diet-KETO');
    fireEvent.press(ketoOption);

    expect(mockOnValueChange).toHaveBeenCalledWith('KETO');
  });

  it('should disable interaction when disabled prop is true', () => {
    const { getByLabelText } = render(
      <DietTypeSelector
        value="NONE"
        onValueChange={mockOnValueChange}
        disabled={true}
      />
    );

    const highProteinOption = getByLabelText('diet-HIGH_PROTEIN');
    fireEvent.press(highProteinOption);

    expect(mockOnValueChange).not.toHaveBeenCalled();
  });

  it('should render tooltips with diet descriptions', () => {
    const { getAllByTestId } = render(
      <DietTypeSelector
        value="NONE"
        onValueChange={mockOnValueChange}
      />
    );

    // Check that tooltip triggers are rendered (Info icons)
    const tooltipTriggers = getAllByTestId(/tooltip-trigger/i);
    expect(tooltipTriggers.length).toBeGreaterThan(0);
  });
});