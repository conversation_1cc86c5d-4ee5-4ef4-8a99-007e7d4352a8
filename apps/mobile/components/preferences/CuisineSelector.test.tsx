import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { CuisineSelector } from './CuisineSelector';
import { CuisineType } from '@food-magic/shared-types';

describe('CuisineSelector', () => {
  const mockOnValueChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all cuisine type options', () => {
    const { getByText } = render(
      <CuisineSelector
        value={[]}
        onValueChange={mockOnValueChange}
      />
    );

    expect(getByText('菜系偏好')).toBeTruthy();
    expect(getByText('选择您喜欢的菜系（可多选）')).toBeTruthy();
    expect(getByText('意大利菜')).toBeTruthy();
    expect(getByText('墨西哥菜')).toBeTruthy();
    expect(getByText('川菜')).toBeTruthy();
    expect(getByText('日本料理')).toBeTruthy();
  });

  it('should display select/deselect all button', () => {
    const { getByText } = render(
      <CuisineSelector
        value={[]}
        onValueChange={mockOnValueChange}
      />
    );

    expect(getByText('全选')).toBeTruthy();
  });

  it('should toggle select all / deselect all', () => {
    const { getByText, rerender } = render(
      <CuisineSelector
        value={[]}
        onValueChange={mockOnValueChange}
      />
    );

    const selectAllButton = getByText('全选');
    fireEvent.press(selectAllButton);

    // Should call with all cuisine types
    expect(mockOnValueChange).toHaveBeenCalledWith(
      expect.arrayContaining(['ITALIAN', 'MEXICAN', 'CHINESE'])
    );

    // Rerender with all selected
    const allCuisines = Object.keys({
      ITALIAN: '意大利菜',
      MEXICAN: '墨西哥菜',
      CHINESE: '川菜',
      JAPANESE: '日本料理',
      INDIAN: '印度菜',
      THAI: '泰国菜',
      FRENCH: '法国菜',
      SPANISH: '西班牙菜',
      GREEK: '希腊菜',
      AMERICAN: '美式料理',
      KOREAN: '韩国料理',
      VIETNAMESE: '越南菜',
      MIDDLE_EASTERN: '中东菜',
      AFRICAN: '非洲菜',
      CARIBBEAN: '加勒比海菜',
    }) as CuisineType[];

    rerender(
      <CuisineSelector
        value={allCuisines}
        onValueChange={mockOnValueChange}
      />
    );

    expect(getByText('取消全选')).toBeTruthy();
  });

  it('should add cuisine when unchecked cuisine is clicked', () => {
    const { getByText } = render(
      <CuisineSelector
        value={['ITALIAN']}
        onValueChange={mockOnValueChange}
      />
    );

    const mexicanOption = getByText('墨西哥菜');
    fireEvent.press(mexicanOption);

    expect(mockOnValueChange).toHaveBeenCalledWith(['ITALIAN', 'MEXICAN']);
  });

  it('should remove cuisine when checked cuisine is clicked', () => {
    const { getByText } = render(
      <CuisineSelector
        value={['ITALIAN', 'MEXICAN']}
        onValueChange={mockOnValueChange}
      />
    );

    const italianOption = getByText('意大利菜');
    fireEvent.press(italianOption);

    expect(mockOnValueChange).toHaveBeenCalledWith(['MEXICAN']);
  });

  it('should display selected count', () => {
    const { getByText } = render(
      <CuisineSelector
        value={['ITALIAN', 'MEXICAN', 'CHINESE']}
        onValueChange={mockOnValueChange}
      />
    );

    expect(getByText('已选择 3 种菜系')).toBeTruthy();
  });

  it('should not display count when no cuisines selected', () => {
    const { queryByText } = render(
      <CuisineSelector
        value={[]}
        onValueChange={mockOnValueChange}
      />
    );

    expect(queryByText(/已选择.*种菜系/)).toBeFalsy();
  });

  it('should disable interaction when disabled prop is true', () => {
    const { getByText } = render(
      <CuisineSelector
        value={[]}
        onValueChange={mockOnValueChange}
        disabled={true}
      />
    );

    const italianOption = getByText('意大利菜');
    fireEvent.press(italianOption);

    expect(mockOnValueChange).not.toHaveBeenCalled();
  });
});