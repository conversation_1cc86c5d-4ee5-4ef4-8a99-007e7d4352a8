import React from 'react';
import {
  YStack,
  XStack,
  Checkbox,
  Label,
  Text,
  Button,
  ScrollView,
} from 'tamagui';
import { CuisineType } from '@food-magic/shared-types';

interface CuisineSelectorProps {
  value: CuisineType[];
  onValueChange: (value: CuisineType[]) => void;
  disabled?: boolean;
}

const cuisineTypeInfo: Record<CuisineType, string> = {
  ITALIAN: '意大利菜',
  MEXICAN: '墨西哥菜',
  CHINESE: '川菜',
  JAPANESE: '日本料理',
  INDIAN: '印度菜',
  THAI: '泰国菜',
  FRENCH: '法国菜',
  SPANISH: '西班牙菜',
  GREEK: '希腊菜',
  AMERICAN: '美式料理',
  KOREAN: '韩国料理',
  VIETNAMESE: '越南菜',
  MIDDLE_EASTERN: '中东菜',
  AFRICAN: '非洲菜',
  CARIBBEAN: '加勒比海菜',
};

export function C<PERSON>sineSelector({
  value,
  onValueChange,
  disabled = false,
}: CuisineSelectorProps) {
  const handleToggle = (cuisine: CuisineType) => {
    const newValue = value.includes(cuisine)
      ? value.filter(c => c !== cuisine)
      : [...value, cuisine];
    onValueChange(newValue);
  };

  const handleSelectAll = () => {
    if (value.length === Object.keys(cuisineTypeInfo).length) {
      onValueChange([]);
    } else {
      onValueChange(Object.keys(cuisineTypeInfo) as CuisineType[]);
    }
  };

  const isAllSelected = value.length === Object.keys(cuisineTypeInfo).length;

  return (
    <YStack space="$3">
      <XStack justifyContent="space-between" alignItems="center">
        <YStack>
          <Text fontSize="$4" fontWeight="600" color="$gray12">
            菜系偏好
          </Text>
          <Text fontSize="$2" color="$gray10">
            选择您喜欢的菜系（可多选）
          </Text>
        </YStack>
        <Button
          size="$2"
          chromeless
          onPress={handleSelectAll}
          disabled={disabled}
          color="$orange10"
        >
          {isAllSelected ? '取消全选' : '全选'}
        </Button>
      </XStack>

      <ScrollView maxHeight={400}>
        <YStack space="$2">
          {(Object.keys(cuisineTypeInfo) as CuisineType[]).map((cuisine) => {
            const isChecked = value.includes(cuisine);
            return (
              <Label
                key={cuisine}
                htmlFor={`cuisine-${cuisine}`}
                disabled={disabled}
              >
                <XStack
                  space="$3"
                  alignItems="center"
                  paddingVertical="$2"
                  paddingHorizontal="$3"
                  borderRadius="$3"
                  backgroundColor={isChecked ? '$orange2' : '$background'}
                  hoverStyle={{
                    backgroundColor: isChecked ? '$orange3' : '$gray2',
                  }}
                  pressStyle={{
                    backgroundColor: isChecked ? '$orange3' : '$gray3',
                  }}
                  borderWidth={1}
                  borderColor={isChecked ? '$orange8' : '$borderColor'}
                  onPress={() => handleToggle(cuisine)}
                >
                  <Checkbox
                    id={`cuisine-${cuisine}`}
                    checked={isChecked}
                    onCheckedChange={() => handleToggle(cuisine)}
                    disabled={disabled}
                    size="$3"
                  >
                    <Checkbox.Indicator>
                      <Text fontSize="$2">✓</Text>
                    </Checkbox.Indicator>
                  </Checkbox>
                  
                  <Text
                    fontSize="$3"
                    fontWeight={isChecked ? '600' : '400'}
                    color={isChecked ? '$orange10' : '$gray12'}
                    flex={1}
                  >
                    {cuisineTypeInfo[cuisine]}
                  </Text>
                </XStack>
              </Label>
            );
          })}
        </YStack>
      </ScrollView>
      
      {value.length > 0 && (
        <Text fontSize="$2" color="$gray10">
          已选择 {value.length} 种菜系
        </Text>
      )}
    </YStack>
  );
}