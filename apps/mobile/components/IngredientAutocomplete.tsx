import React, { useCallback, useRef, useEffect } from 'react';
import {
  YStack,
  XStack,
  Text,
  ScrollView,
  AnimatePresence,
  styled,
  Button,
} from 'tamagui';
import { IngredientSuggestionDto } from '@foodmagic/shared-types';

interface IngredientAutocompleteProps {
  suggestions: IngredientSuggestionDto[];
  isLoading: boolean;
  onSelect: (ingredient: string) => void;
  visible: boolean;
  maxHeight?: number;
}

const SuggestionItem = styled(Button, {
  width: '100%',
  justifyContent: 'flex-start',
  paddingHorizontal: '$3',
  paddingVertical: '$3',
  backgroundColor: 'transparent',
  borderRadius: 0,
  pressStyle: {
    backgroundColor: '$gray3',
  },
  hoverStyle: {
    backgroundColor: '$gray3',
  },
});

const CategoryBadge = styled(Text, {
  fontSize: '$1',
  color: '$gray10',
  backgroundColor: '$gray3',
  paddingHorizontal: '$2',
  paddingVertical: '$1',
  borderRadius: '$2',
});

export const IngredientAutocomplete: React.FC<IngredientAutocompleteProps> = ({
  suggestions,
  isLoading,
  onSelect,
  visible,
  maxHeight = 200,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (visible && suggestions.length > 0) {
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    }
  }, [visible, suggestions]);

  const handleSelect = useCallback(
    (suggestion: IngredientSuggestionDto) => {
      onSelect(suggestion.name);
    },
    [onSelect]
  );

  if (!visible || (!isLoading && suggestions.length === 0)) {
    return null;
  }

  return (
    <AnimatePresence>
      <YStack
        animation="quick"
        enterStyle={{
          opacity: 0,
          scale: 0.95,
          y: -10,
        }}
        exitStyle={{
          opacity: 0,
          scale: 0.95,
          y: -10,
        }}
        backgroundColor="$background"
        borderWidth={1}
        borderColor="$borderColor"
        borderRadius="$4"
        overflow="hidden"
        elevation={4}
        shadowColor="$shadowColor"
        shadowOffset={{ width: 0, height: 2 }}
        shadowOpacity={0.1}
        shadowRadius={4}
        maxHeight={maxHeight}
      >
        {isLoading ? (
          <YStack padding="$3" alignItems="center">
            <Text fontSize="$3" color="$gray10">
              搜索中...
            </Text>
          </YStack>
        ) : (
          <ScrollView
            ref={scrollViewRef}
            showsVerticalScrollIndicator={true}
            nestedScrollEnabled={true}
          >
            <YStack>
              {suggestions.map((suggestion) => (
                <SuggestionItem
                  key={suggestion.id}
                  onPress={() => handleSelect(suggestion)}
                >
                  <XStack
                    width="100%"
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <Text fontSize="$3" color="$color">
                      {suggestion.name}
                    </Text>
                    {suggestion.category && (
                      <CategoryBadge>{suggestion.category}</CategoryBadge>
                    )}
                  </XStack>
                </SuggestionItem>
              ))}
            </YStack>
          </ScrollView>
        )}
      </YStack>
    </AnimatePresence>
  );
};