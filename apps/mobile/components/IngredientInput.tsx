import React, { useState, useCallback, useRef } from 'react';
import {
  View,
  Input,
  XStack,
  YStack,
  Button,
  Text,
  AnimatePresence,
  styled,
  getTokens,
} from 'tamagui';
import { X } from '@tamagui/lucide-icons';
import { TextInput } from 'react-native';

interface IngredientInputProps {
  ingredients: string[];
  onIngredientsChange: (ingredients: string[]) => void;
  placeholder?: string;
  maxIngredients?: number;
}

const Tag = styled(XStack, {
  backgroundColor: '$blue5',
  paddingHorizontal: '$3',
  paddingVertical: '$2',
  borderRadius: '$4',
  alignItems: 'center',
  gap: '$2',
  animation: 'quick',
  scale: 1,
  opacity: 1,
  pressStyle: {
    scale: 0.95,
  },
  enterStyle: {
    scale: 0.8,
    opacity: 0,
  },
  exitStyle: {
    scale: 0.8,
    opacity: 0,
  },
});

export const IngredientInput: React.FC<IngredientInputProps> = ({
  ingredients,
  onIngredientsChange,
  placeholder = '输入食材名称...',
  maxIngredients = 20,
}) => {
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<TextInput>(null);

  const handleAddIngredient = useCallback(() => {
    const trimmedValue = inputValue.trim();
    
    if (
      trimmedValue &&
      !ingredients.includes(trimmedValue) &&
      ingredients.length < maxIngredients
    ) {
      onIngredientsChange([...ingredients, trimmedValue]);
      setInputValue('');
    }
  }, [inputValue, ingredients, onIngredientsChange, maxIngredients]);

  const handleRemoveIngredient = useCallback(
    (index: number) => {
      const newIngredients = ingredients.filter((_, i) => i !== index);
      onIngredientsChange(newIngredients);
    },
    [ingredients, onIngredientsChange]
  );

  const handleKeyPress = useCallback(
    (e: any) => {
      if (e.nativeEvent.key === 'Enter') {
        e.preventDefault();
        handleAddIngredient();
      }
    },
    [handleAddIngredient]
  );

  return (
    <YStack gap="$3" width="100%">
      {ingredients.length > 0 && (
        <XStack flexWrap="wrap" gap="$2">
          <AnimatePresence>
            {ingredients.map((ingredient, index) => (
              <Tag key={`${ingredient}-${index}`}>
                <Text fontSize="$3" color="$blue11">
                  {ingredient}
                </Text>
                <Button
                  size="$1"
                  circular
                  icon={X}
                  chromeless
                  onPress={() => handleRemoveIngredient(index)}
                  hoverStyle={{ backgroundColor: '$blue6' }}
                  testID="delete-ingredient"
                />
              </Tag>
            ))}
          </AnimatePresence>
        </XStack>
      )}

      <XStack gap="$2" alignItems="center">
        <Input
          ref={inputRef}
          flex={1}
          value={inputValue}
          onChangeText={setInputValue}
          placeholder={placeholder}
          onSubmitEditing={handleAddIngredient}
          onKeyPress={handleKeyPress}
          returnKeyType="done"
          disabled={ingredients.length >= maxIngredients}
        />
        <Button
          onPress={handleAddIngredient}
          disabled={!inputValue.trim() || ingredients.length >= maxIngredients}
          theme="blue"
        >
          添加
        </Button>
      </XStack>

      {ingredients.length >= maxIngredients && (
        <Text fontSize="$2" color="$orange10">
          已达到最大食材数量限制 ({maxIngredients})
        </Text>
      )}
    </YStack>
  );
};