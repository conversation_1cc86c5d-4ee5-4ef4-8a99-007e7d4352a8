import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { IngredientAutocomplete } from './IngredientAutocomplete';
import { IngredientSuggestionDto } from '@foodmagic/shared-types';

describe('IngredientAutocomplete', () => {
  const mockOnSelect = jest.fn();
  
  const mockSuggestions: IngredientSuggestionDto[] = [
    { id: '1', name: '番茄', category: '蔬菜' },
    { id: '2', name: '番茄酱', category: '调料' },
    { id: '3', name: '土豆', category: '蔬菜' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not render when visible is false', () => {
    const { queryByText } = render(
      <IngredientAutocomplete
        suggestions={mockSuggestions}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={false}
      />
    );

    expect(queryByText('番茄')).toBeNull();
  });

  it('should render suggestions when visible is true', () => {
    const { getByText } = render(
      <IngredientAutocomplete
        suggestions={mockSuggestions}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={true}
      />
    );

    mockSuggestions.forEach((suggestion) => {
      expect(getByText(suggestion.name)).toBeTruthy();
      if (suggestion.category) {
        expect(getByText(suggestion.category)).toBeTruthy();
      }
    });
  });

  it('should show loading state when isLoading is true', () => {
    const { getByText } = render(
      <IngredientAutocomplete
        suggestions={[]}
        isLoading={true}
        onSelect={mockOnSelect}
        visible={true}
      />
    );

    expect(getByText('搜索中...')).toBeTruthy();
  });

  it('should call onSelect when suggestion is selected', () => {
    const { getByText } = render(
      <IngredientAutocomplete
        suggestions={mockSuggestions}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={true}
      />
    );

    fireEvent.press(getByText('番茄'));
    expect(mockOnSelect).toHaveBeenCalledWith('番茄');
  });

  it('should not render when suggestions are empty and not loading', () => {
    const { queryByText } = render(
      <IngredientAutocomplete
        suggestions={[]}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={true}
      />
    );

    expect(queryByText('搜索中...')).toBeNull();
  });

  it('should render with custom maxHeight', () => {
    const { getByTestId } = render(
      <IngredientAutocomplete
        suggestions={mockSuggestions}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={true}
        maxHeight={300}
      />
    );

    const container = getByTestId('autocomplete-container');
    expect(container.props.style.maxHeight).toBe(300);
  });

  it('should handle selection of suggestions with categories', () => {
    const { getByText } = render(
      <IngredientAutocomplete
        suggestions={mockSuggestions}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={true}
      />
    );

    const suggestionWithCategory = mockSuggestions.find(s => s.category);
    if (suggestionWithCategory) {
      fireEvent.press(getByText(suggestionWithCategory.name));
      expect(mockOnSelect).toHaveBeenCalledWith(suggestionWithCategory.name);
    }
  });

  it('should scroll to top when new suggestions appear', async () => {
    const scrollToMock = jest.fn();
    jest.spyOn(React, 'useRef').mockReturnValueOnce({
      current: { scrollTo: scrollToMock },
    });

    const { rerender } = render(
      <IngredientAutocomplete
        suggestions={[]}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={false}
      />
    );

    rerender(
      <IngredientAutocomplete
        suggestions={mockSuggestions}
        isLoading={false}
        onSelect={mockOnSelect}
        visible={true}
      />
    );

    await waitFor(() => {
      expect(scrollToMock).toHaveBeenCalledWith({ y: 0, animated: false });
    });
  });
});