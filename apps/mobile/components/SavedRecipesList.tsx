import React from 'react';
import {
  YStack,
  XStack,
  Card,
  Text,
  H3,
  Button,
} from 'tamagui';
import {
  Clock,
  Users,
  Heart,
  ChevronRight,
} from '@tamagui/lucide-icons';
import Animated, {
  FadeInDown,
  Layout,
} from 'react-native-reanimated';
import { RecipeDto } from '@foodmagic/shared-types';
import { useRecipeStore } from '../stores/recipe.store';
import * as Haptics from 'expo-haptics';

interface SavedRecipesListProps {
  recipes: RecipeDto[];
  onRecipePress: (recipeId: string) => void;
  showFavoriteButton?: boolean;
}

const AnimatedCard = Animated.createAnimatedComponent(Card);

const DifficultyIndicator = ({ difficulty }: { difficulty: string }) => {
  const config = {
    EASY: { label: '简单', color: '$green9', emoji: '🌱' },
    MEDIUM: { label: '中等', color: '$orange9', emoji: '🔥' },
    HARD: { label: '困难', color: '$red9', emoji: '🌶️' },
  };

  const { label, color, emoji } = config[difficulty as keyof typeof config] || config.EASY;

  return (
    <XStack alignItems="center" space="$1">
      <Text fontSize="$2">{emoji}</Text>
      <Text fontSize="$2" color={color} fontWeight="600">
        {label}
      </Text>
    </XStack>
  );
};

const RecipeCard = ({ 
  recipe, 
  index, 
  onPress, 
  showFavoriteButton 
}: { 
  recipe: RecipeDto; 
  index: number; 
  onPress: () => void;
  showFavoriteButton?: boolean;
}) => {
  const { toggleFavorite, isFavorite, isFavoriting } = useRecipeStore();
  const isSaved = isFavorite(recipe.id);
  const [isProcessing, setIsProcessing] = React.useState(false);

  const handleFavoriteToggle = async (e: any) => {
    e.stopPropagation();
    
    if (isProcessing || isFavoriting) {
      return; // 防止重复点击
    }
    
    setIsProcessing(true);
    try {
      await toggleFavorite(recipe.id);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('收藏操作失败:', error);
      // 操作失败时也要给用户触觉反馈
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AnimatedCard
      entering={FadeInDown.delay(index * 50).springify()}
      layout={Layout.springify()}
      padded
      bordered
      pressStyle={{ scale: 0.98 }}
      hoverStyle={{ scale: 1.02 }}
      animation="quick"
      backgroundColor="$background"
      borderColor="$borderColor"
      borderRadius="$4"
      onPress={onPress}
      elevation={2}
      accessibilityRole="button"
      accessibilityLabel={`食谱：${recipe.title}`}
    >
      <XStack justifyContent="space-between" alignItems="flex-start">
        <YStack flex={1} space="$2">
          <H3 fontSize="$5" fontWeight="600" color="$orange11" numberOfLines={2}>
            {recipe.title}
          </H3>
          
          {recipe.description && (
            <Text fontSize="$3" color="$gray11" numberOfLines={2}>
              {recipe.description}
            </Text>
          )}

          <XStack space="$3" flexWrap="wrap" alignItems="center">
            <DifficultyIndicator difficulty={recipe.difficulty} />
            
            <XStack alignItems="center" space="$1">
              <Clock size={14} color="$gray10" />
              <Text fontSize="$2" color="$gray11">
                {recipe.cookingTimeMinutes}分钟
              </Text>
            </XStack>

            <XStack alignItems="center" space="$1">
              <Users size={14} color="$gray10" />
              <Text fontSize="$2" color="$gray11">
                {recipe.servings}人份
              </Text>
            </XStack>
          </XStack>

          {recipe.tags && recipe.tags.length > 0 && (
            <XStack flexWrap="wrap" gap="$1" marginTop="$1">
              {recipe.tags.slice(0, 3).map((tag, idx) => (
                <XStack
                  key={idx}
                  backgroundColor="$orange3"
                  paddingHorizontal="$2"
                  paddingVertical="$1"
                  borderRadius="$2"
                >
                  <Text fontSize="$1" color="$orange11">
                    #{tag}
                  </Text>
                </XStack>
              ))}
              {recipe.tags.length > 3 && (
                <Text fontSize="$1" color="$gray10">
                  +{recipe.tags.length - 3}
                </Text>
              )}
            </XStack>
          )}
        </YStack>

        <XStack alignItems="center" space="$2">
          {showFavoriteButton && (
            <Button
              size="$3"
              circular
              chromeless
              icon={Heart}
              color={isSaved ? '$red10' : '$gray8'}
              backgroundColor={isSaved ? '$red2' : 'transparent'}
              onPress={handleFavoriteToggle}
              disabled={isProcessing}
              opacity={isProcessing ? 0.5 : 1}
              accessibilityRole="button"
              accessibilityLabel={isSaved ? '取消收藏' : '收藏'}
            />
          )}
          <ChevronRight size={20} color="$gray10" />
        </XStack>
      </XStack>
    </AnimatedCard>
  );
};

export default function SavedRecipesList({ 
  recipes, 
  onRecipePress, 
  showFavoriteButton = false 
}: SavedRecipesListProps) {
  return (
    <YStack space="$3">
      {recipes.map((recipe, index) => (
        <RecipeCard
          key={recipe.id}
          recipe={recipe}
          index={index}
          onPress={() => onRecipePress(recipe.id)}
          showFavoriteButton={showFavoriteButton}
        />
      ))}
    </YStack>
  );
}