import { RecipeService, RecipeServiceError } from './recipe.service';
import { useAuthStore } from '../stores/auth.store';

// Mock fetch
global.fetch = jest.fn();

// Mock auth store
jest.mock('../stores/auth.store');

describe('RecipeService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('收藏相关API', () => {
    beforeEach(() => {
      (useAuthStore.getState as jest.Mock).mockReturnValue({
        token: 'test-token',
      });
    });

    describe('checkFavoriteStatus', () => {
      it('应该返回true当食谱已收藏（200）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          status: 200,
        });

        const result = await RecipeService.checkFavoriteStatus('recipe-1');
        
        expect(result).toBe(true);
        expect(fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/v1/saved-recipes/recipe-1'),
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Authorization': 'Bearer test-token',
            }),
          })
        );
      });

      it('应该返回false当食谱未收藏（404）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: false,
          status: 404,
        });

        const result = await RecipeService.checkFavoriteStatus('recipe-1');
        
        expect(result).toBe(false);
      });

      it('应该返回false当用户未登录（401）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: false,
          status: 401,
        });

        const result = await RecipeService.checkFavoriteStatus('recipe-1');
        
        expect(result).toBe(false);
      });

      it('应该抛出错误当recipeId为空', async () => {
        await expect(RecipeService.checkFavoriteStatus('')).rejects.toThrow('食谱ID不能为空');
      });
    });

    describe('favoriteRecipe', () => {
      it('应该成功收藏食谱（204）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          status: 204,
        });

        await RecipeService.favoriteRecipe('recipe-1');
        
        expect(fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/v1/saved-recipes/recipe-1'),
          expect.objectContaining({
            method: 'PUT',
            headers: expect.objectContaining({
              'Authorization': 'Bearer test-token',
            }),
          })
        );
      });

      it('应该抛出错误当用户未登录（401）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: false,
          status: 401,
        });

        await expect(RecipeService.favoriteRecipe('recipe-1')).rejects.toThrow('请先登录后再收藏食谱');
      });

      it('应该抛出错误当recipeId为空', async () => {
        await expect(RecipeService.favoriteRecipe('')).rejects.toThrow('食谱ID不能为空');
      });

      it('应该处理超时错误', async () => {
        const abortError = new Error('AbortError');
        abortError.name = 'AbortError';
        (fetch as jest.Mock).mockRejectedValue(abortError);

        await expect(RecipeService.favoriteRecipe('recipe-1')).rejects.toThrow('收藏食谱请求超时');
      });
    });

    describe('unfavoriteRecipe', () => {
      it('应该成功取消收藏（204）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          status: 204,
        });

        await RecipeService.unfavoriteRecipe('recipe-1');
        
        expect(fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/v1/saved-recipes/recipe-1'),
          expect.objectContaining({
            method: 'DELETE',
            headers: expect.objectContaining({
              'Authorization': 'Bearer test-token',
            }),
          })
        );
      });

      it('应该抛出错误当用户未登录（401）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: false,
          status: 401,
        });

        await expect(RecipeService.unfavoriteRecipe('recipe-1')).rejects.toThrow('请先登录');
      });

      it('应该抛出错误当recipeId为空', async () => {
        await expect(RecipeService.unfavoriteRecipe('')).rejects.toThrow('食谱ID不能为空');
      });
    });

    describe('getUserFavorites', () => {
      it('应该返回收藏的食谱列表', async () => {
        const mockRecipes = [
          { id: '1', title: '食谱1' },
          { id: '2', title: '食谱2' },
        ];

        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          status: 200,
          json: async () => mockRecipes,
        });

        const result = await RecipeService.getUserFavorites();
        
        expect(result).toEqual(mockRecipes);
        expect(fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/v1/saved-recipes'),
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Authorization': 'Bearer test-token',
            }),
          })
        );
      });

      it('应该抛出错误当用户未登录（401）', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: false,
          status: 401,
        });

        await expect(RecipeService.getUserFavorites()).rejects.toThrow('请先登录后再查看收藏');
      });

      it('应该抛出错误当返回数据不是数组', async () => {
        (fetch as jest.Mock).mockResolvedValue({
          ok: true,
          status: 200,
          json: async () => ({ notAnArray: true }),
        });

        await expect(RecipeService.getUserFavorites()).rejects.toThrow('服务器返回了无效的数据格式');
      });

      it('应该处理超时错误', async () => {
        const abortError = new Error('AbortError');
        abortError.name = 'AbortError';
        (fetch as jest.Mock).mockRejectedValue(abortError);

        await expect(RecipeService.getUserFavorites()).rejects.toThrow('获取收藏列表请求超时');
      });
    });
  });

  describe('认证头处理', () => {
    it('应该在有token时添加Authorization header', async () => {
      (useAuthStore.getState as jest.Mock).mockReturnValue({
        token: 'test-token',
      });

      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200,
      });

      await RecipeService.checkFavoriteStatus('recipe-1');

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json',
          }),
        })
      );
    });

    it('应该在没有token时不添加Authorization header', async () => {
      (useAuthStore.getState as jest.Mock).mockReturnValue({
        token: null,
      });

      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200,
      });

      await RecipeService.checkFavoriteStatus('recipe-1');

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            'Authorization': expect.any(String),
          }),
        })
      );
    });
  });
});