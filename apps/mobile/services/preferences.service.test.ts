import { PreferencesService } from './preferences.service';
import { UserPreferencesDto } from '@food-magic/shared-types';

// Mock the api-helpers module
jest.mock('../utils/api-helpers', () => ({
  fetchWithTimeout: jest.fn(),
  buildHeaders: jest.fn(() => ({ 'Content-Type': 'application/json' })),
  ApiError: class ApiError extends Error {
    constructor(message: string, public statusCode?: number, public details?: any) {
      super(message);
      this.name = 'ApiError';
    }
  },
  API_BASE_URL: 'http://localhost:8080',
}));

const { fetchWithTimeout, buildHeaders } = require('../utils/api-helpers');

describe('PreferencesService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserPreferences', () => {
    it('should fetch user preferences successfully', async () => {
      const mockPreferences: UserPreferencesDto = {
        diet: 'LOW_CARB',
        preferredCuisines: ['ITALIAN', 'MEXICAN'],
        spiceLevel: 'MEDIUM',
      };

      fetchWithTimeout.mockResolvedValue({
        ok: true,
        json: async () => mockPreferences,
      });

      const result = await PreferencesService.getUserPreferences();

      expect(fetchWithTimeout).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/users/me/preferences',
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        }
      );
      expect(result).toEqual(mockPreferences);
    });

    it('should return empty preferences on 404', async () => {
      fetchWithTimeout.mockResolvedValue({
        ok: false,
        status: 404,
      });

      const result = await PreferencesService.getUserPreferences();

      expect(result).toEqual({});
    });

    it('should throw error on 401 unauthorized', async () => {
      fetchWithTimeout.mockResolvedValue({
        ok: false,
        status: 401,
      });

      await expect(PreferencesService.getUserPreferences()).rejects.toThrow(
        '未授权，请先登录'
      );
    });

    it('should handle server error with message', async () => {
      fetchWithTimeout.mockResolvedValue({
        ok: false,
        status: 500,
        json: async () => ({ message: '服务器内部错误' }),
      });

      await expect(PreferencesService.getUserPreferences()).rejects.toThrow(
        '服务器内部错误'
      );
    });

    it('should handle network error', async () => {
      fetchWithTimeout.mockRejectedValue(new Error('Network request failed'));

      await expect(PreferencesService.getUserPreferences()).rejects.toThrow(
        'Network request failed'
      );
    });
  });

  describe('updateUserPreferences', () => {
    it('should update user preferences successfully', async () => {
      const inputPreferences: UserPreferencesDto = {
        diet: 'KETO',
        preferredCuisines: ['CHINESE'],
        maxCookingTime: 30,
      };

      const updatedPreferences: UserPreferencesDto = {
        ...inputPreferences,
        locale: 'zh-CN',
      };

      fetchWithTimeout.mockResolvedValue({
        ok: true,
        json: async () => updatedPreferences,
      });

      const result = await PreferencesService.updateUserPreferences(inputPreferences);

      expect(fetchWithTimeout).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/users/me/preferences',
        {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(inputPreferences),
        }
      );
      expect(result).toEqual(updatedPreferences);
    });

    it('should validate preferences before sending', async () => {
      const invalidPreferences: UserPreferencesDto = {
        preferredCuisines: new Array(25).fill('ITALIAN'), // Too many cuisines
      };

      await expect(
        PreferencesService.updateUserPreferences(invalidPreferences)
      ).rejects.toThrow('偏好设置验证失败，请检查输入');

      expect(fetchWithTimeout).not.toHaveBeenCalled();
    });

    it('should throw error on 401 unauthorized', async () => {
      const preferences: UserPreferencesDto = {
        diet: 'VEGAN',
      };

      fetchWithTimeout.mockResolvedValue({
        ok: false,
        status: 401,
      });

      await expect(
        PreferencesService.updateUserPreferences(preferences)
      ).rejects.toThrow('未授权，请先登录');
    });

    it('should throw error on 400 bad request', async () => {
      const preferences: UserPreferencesDto = {
        diet: 'PALEO',
      };

      fetchWithTimeout.mockResolvedValue({
        ok: false,
        status: 400,
      });

      await expect(
        PreferencesService.updateUserPreferences(preferences)
      ).rejects.toThrow('偏好设置格式错误');
    });

    it('should handle server error with custom message', async () => {
      const preferences: UserPreferencesDto = {
        diet: 'MEDITERRANEAN',
      };

      fetchWithTimeout.mockResolvedValue({
        ok: false,
        status: 500,
        json: async () => ({ message: '数据库连接失败' }),
      });

      await expect(
        PreferencesService.updateUserPreferences(preferences)
      ).rejects.toThrow('数据库连接失败');
    });

    it('should handle network error', async () => {
      const preferences: UserPreferencesDto = {
        diet: 'HIGH_PROTEIN',
      };

      fetchWithTimeout.mockRejectedValue(new Error('Request timeout'));

      await expect(
        PreferencesService.updateUserPreferences(preferences)
      ).rejects.toThrow('Request timeout');
    });

    it('should handle unknown error', async () => {
      const preferences: UserPreferencesDto = {
        diet: 'LOW_CARB',
      };

      fetchWithTimeout.mockRejectedValue('Unknown error');

      await expect(
        PreferencesService.updateUserPreferences(preferences)
      ).rejects.toThrow('更新用户偏好时发生未知错误');
    });
  });
});