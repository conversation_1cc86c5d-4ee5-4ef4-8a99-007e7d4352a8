import { RecipeDto, UserPreferencesDto } from '@foodmagic/shared-types';
import { useAuthStore } from '../stores/auth.store';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8080';
const DEFAULT_TIMEOUT = 10000; // 10秒超时（食谱生成可能需要更长时间）

// 创建带超时的fetch包装器
async function fetchWithTimeout(url: string, options: RequestInit = {}, timeout: number = DEFAULT_TIMEOUT): Promise<Response> {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    
    // 提供更详细的错误信息
    if ((error as Error).name === 'AbortError') {
      throw new Error('请求超时');
    }
    
    // 检查网络连接
    if ((error as Error).message.includes('Network request failed') || 
        (error as Error).message.includes('Failed to fetch')) {
      throw new Error('网络连接失败，请检查您的网络设置');
    }
    
    throw error;
  }
}

// 自定义错误类，提供更好的错误信息
export class RecipeServiceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'RecipeServiceError';
  }
}

export class RecipeService {
  // 获取认证token（从store或localStorage）
  private static getAuthToken(): string | null {
    return useAuthStore.getState().token;
  }

  // 构建请求头
  private static buildHeaders(includeAuth: boolean = false): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    if (includeAuth) {
      const token = this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }
    
    return headers;
  }

  static async generateRecipe(
    ingredients: string[], 
    preferences?: UserPreferencesDto
  ): Promise<RecipeDto> {
    if (!ingredients || ingredients.length === 0) {
      throw new RecipeServiceError('至少需要一个食材来生成食谱');
    }

    const request = {
      ingredients,
      preferences: preferences || {
        dietaryRestrictions: [],
        spiceLevel: 'MEDIUM',
        locale: 'zh-CN',
        unitSystem: 'METRIC',
        reduceMotion: false,
      },
    };

    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/recipes/generate`,
        {
          method: 'POST',
          headers: this.buildHeaders(),
          body: JSON.stringify(request),
        },
        20000 // 食谱生成给20秒超时
      );

      if (!response.ok) {
        let errorMessage = `生成食谱失败`;
        
        // 尝试解析错误响应
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch {
          // 忽略JSON解析错误
        }
        
        throw new RecipeServiceError(errorMessage, response.status);
      }

      const data: RecipeDto = await response.json();
      
      // 验证响应数据
      if (!data || !data.id || !data.title) {
        throw new RecipeServiceError('服务器返回了无效的食谱数据');
      }
      
      return data;
    } catch (error) {
      if (error instanceof RecipeServiceError) {
        throw error;
      }
      
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('生成食谱请求超时，请稍后重试');
      }
      
      throw new RecipeServiceError(
        '生成食谱时发生错误',
        undefined,
        error
      );
    }
  }

  static async saveRecipe(recipeId: string): Promise<void> {
    if (!recipeId) {
      throw new RecipeServiceError('食谱ID不能为空');
    }

    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/recipes/${encodeURIComponent(recipeId)}/save`,
        {
          method: 'POST',
          headers: this.buildHeaders(true), // 需要认证
        },
        5000
      );

      if (response.status === 401) {
        throw new RecipeServiceError('请先登录后再保存食谱', 401);
      }

      if (!response.ok) {
        throw new RecipeServiceError(`保存食谱失败`, response.status);
      }
    } catch (error) {
      if (error instanceof RecipeServiceError) {
        throw error;
      }
      
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('保存食谱请求超时');
      }
      
      throw new RecipeServiceError('保存食谱时发生错误', undefined, error);
    }
  }

  static async getSavedRecipes(): Promise<RecipeDto[]> {
    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/recipes/saved`,
        {
          method: 'GET',
          headers: this.buildHeaders(true), // 需要认证
        },
        8000
      );

      if (response.status === 401) {
        throw new RecipeServiceError('请先登录后再查看保存的食谱', 401);
      }

      if (!response.ok) {
        throw new RecipeServiceError(`获取保存的食谱失败`, response.status);
      }

      const data: RecipeDto[] = await response.json();
      
      // 验证返回数据是数组
      if (!Array.isArray(data)) {
        throw new RecipeServiceError('服务器返回了无效的数据格式');
      }
      
      return data;
    } catch (error) {
      if (error instanceof RecipeServiceError) {
        throw error;
      }
      
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('获取保存的食谱请求超时');
      }
      
      throw new RecipeServiceError('获取保存的食谱时发生错误', undefined, error);
    }
  }

  // 检查食谱是否已收藏
  static async checkFavoriteStatus(recipeId: string): Promise<boolean> {
    if (!recipeId) {
      throw new RecipeServiceError('食谱ID不能为空');
    }

    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/saved-recipes/${encodeURIComponent(recipeId)}`,
        {
          method: 'GET',
          headers: this.buildHeaders(true), // 需要认证
        },
        5000
      );

      if (response.status === 401) {
        // 未登录返回false
        return false;
      }

      // 200表示已收藏，404表示未收藏
      return response.status === 200;
    } catch (error) {
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('检查收藏状态请求超时');
      }
      
      // 其他错误默认返回未收藏
      console.error('检查收藏状态失败:', error);
      return false;
    }
  }

  // 收藏食谱（幂等）
  static async favoriteRecipe(recipeId: string): Promise<void> {
    if (!recipeId) {
      throw new RecipeServiceError('食谱ID不能为空');
    }

    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/saved-recipes/${encodeURIComponent(recipeId)}`,
        {
          method: 'PUT',
          headers: this.buildHeaders(true), // 需要认证
        },
        5000
      );

      if (response.status === 401) {
        throw new RecipeServiceError('请先登录后再收藏食谱', 401);
      }

      if (!response.ok && response.status !== 204) {
        throw new RecipeServiceError(`收藏食谱失败`, response.status);
      }
    } catch (error) {
      if (error instanceof RecipeServiceError) {
        throw error;
      }
      
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('收藏食谱请求超时');
      }
      
      throw new RecipeServiceError('收藏食谱时发生错误', undefined, error);
    }
  }

  // 取消收藏食谱（幂等）
  static async unfavoriteRecipe(recipeId: string): Promise<void> {
    if (!recipeId) {
      throw new RecipeServiceError('食谱ID不能为空');
    }

    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/saved-recipes/${encodeURIComponent(recipeId)}`,
        {
          method: 'DELETE',
          headers: this.buildHeaders(true), // 需要认证
        },
        5000
      );

      if (response.status === 401) {
        throw new RecipeServiceError('请先登录', 401);
      }

      if (!response.ok && response.status !== 204) {
        throw new RecipeServiceError(`取消收藏失败`, response.status);
      }
    } catch (error) {
      if (error instanceof RecipeServiceError) {
        throw error;
      }
      
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('取消收藏请求超时');
      }
      
      throw new RecipeServiceError('取消收藏时发生错误', undefined, error);
    }
  }

  // 获取用户收藏的食谱列表
  static async getUserFavorites(): Promise<RecipeDto[]> {
    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/saved-recipes`,
        {
          method: 'GET',
          headers: this.buildHeaders(true), // 需要认证
        },
        8000
      );

      if (response.status === 401) {
        throw new RecipeServiceError('请先登录后再查看收藏', 401);
      }

      if (!response.ok) {
        throw new RecipeServiceError(`获取收藏列表失败`, response.status);
      }

      const data: RecipeDto[] = await response.json();
      
      // 验证返回数据是数组
      if (!Array.isArray(data)) {
        throw new RecipeServiceError('服务器返回了无效的数据格式');
      }
      
      return data;
    } catch (error) {
      if (error instanceof RecipeServiceError) {
        throw error;
      }
      
      if ((error as Error).name === 'AbortError') {
        throw new RecipeServiceError('获取收藏列表请求超时');
      }
      
      throw new RecipeServiceError('获取收藏列表时发生错误', undefined, error);
    }
  }
}