import { IngredientSuggestionDto } from '@foodmagic/shared-types';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8080';

// 添加请求超时控制
const DEFAULT_TIMEOUT = 5000; // 5秒超时

// 创建带超时的fetch包装器
async function fetchWithTimeout(url: string, options: RequestInit = {}, timeout: number = DEFAULT_TIMEOUT): Promise<Response> {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    throw error;
  }
}

export class IngredientService {
  // 缓存搜索结果以优化性能
  private static searchCache = new Map<string, { data: IngredientSuggestionDto[], timestamp: number }>();
  private static CACHE_TTL = 60000; // 缓存1分钟

  static async searchIngredients(query: string): Promise<IngredientSuggestionDto[]> {
    try {
      if (!query || query.trim().length === 0) {
        return [];
      }

      const trimmedQuery = query.trim().toLowerCase();
      
      // 检查缓存
      const cached = this.searchCache.get(trimmedQuery);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data;
      }

      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/ingredients/search?q=${encodeURIComponent(trimmedQuery)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        },
        3000 // 搜索请求3秒超时
      );

      if (!response.ok) {
        // 不同状态码的处理
        if (response.status === 404) {
          return []; // 没有找到结果是正常情况
        }
        console.error(`Failed to fetch ingredient suggestions: ${response.status} ${response.statusText}`);
        return [];
      }

      const data: IngredientSuggestionDto[] = await response.json();
      
      // 验证返回数据格式
      if (!Array.isArray(data)) {
        console.error('Invalid response format: expected array');
        return [];
      }
      
      // 缓存结果
      this.searchCache.set(trimmedQuery, { data, timestamp: Date.now() });
      
      // 清理过期缓存
      if (this.searchCache.size > 100) {
        this.cleanExpiredCache();
      }
      
      return data;
    } catch (error) {
      if ((error as Error).name === 'AbortError') {
        console.warn('Ingredient search request timed out');
      } else {
        console.error('Error searching ingredients:', error);
      }
      return [];
    }
  }

  static async recordCustomIngredient(ingredientName: string): Promise<void> {
    try {
      if (!ingredientName || ingredientName.trim().length === 0) {
        return;
      }
      
      // 在MVP阶段，这个调用是可选的，不阻塞用户流程
      // 使用更短的超时时间，因为这不是关键操作
      await fetchWithTimeout(`${API_BASE_URL}/api/v1/ingredients/custom`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: ingredientName.trim() }),
      }, 2000); // 2秒超时
    } catch (error) {
      // 静默失败，不影响用户体验
      if ((error as Error).name === 'AbortError') {
        console.log('Record custom ingredient request timed out');
      } else {
        console.log('Failed to record custom ingredient:', error);
      }
    }
  }
  
  // 清理过期缓存
  private static cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.searchCache.entries()) {
      if (now - value.timestamp > this.CACHE_TTL) {
        this.searchCache.delete(key);
      }
    }
  }
  
  // 清空所有缓存（可用于手动刷新）
  static clearCache(): void {
    this.searchCache.clear();
  }
}