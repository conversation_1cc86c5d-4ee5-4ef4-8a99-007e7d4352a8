import { UserPreferencesDto, validateUserPreferences } from '@food-magic/shared-types';
import { 
  fetchWithTimeout, 
  buildHeaders, 
  ApiError as PreferencesServiceError,
  API_BASE_URL 
} from '../utils/api-helpers';

export { PreferencesServiceError };

export class PreferencesService {
  
  static async getUserPreferences(): Promise<UserPreferencesDto> {
    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/users/me/preferences`,
        {
          method: 'GET',
          headers: buildHeaders(true), // Include auth token
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          throw new PreferencesServiceError('未授权，请先登录', 401);
        }
        if (response.status === 404) {
          // User has no preferences set yet, return empty preferences
          return {};
        }
        
        let errorMessage = '获取用户偏好失败';
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch {
          // Ignore JSON parse error
        }
        
        throw new PreferencesServiceError(errorMessage, response.status);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof PreferencesServiceError) {
        throw error;
      }
      
      if (error instanceof Error) {
        throw new PreferencesServiceError(
          error.message || '获取用户偏好时发生错误'
        );
      }
      
      throw new PreferencesServiceError('获取用户偏好时发生未知错误');
    }
  }

  static async updateUserPreferences(
    preferences: UserPreferencesDto
  ): Promise<UserPreferencesDto> {
    // Validate preferences before sending
    if (!validateUserPreferences(preferences)) {
      throw new PreferencesServiceError('偏好设置验证失败，请检查输入');
    }

    try {
      const response = await fetchWithTimeout(
        `${API_BASE_URL}/api/v1/users/me/preferences`,
        {
          method: 'PUT',
          headers: buildHeaders(true), // Include auth token
          body: JSON.stringify(preferences),
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          throw new PreferencesServiceError('未授权，请先登录', 401);
        }
        if (response.status === 400) {
          throw new PreferencesServiceError('偏好设置格式错误', 400);
        }
        
        let errorMessage = '更新用户偏好失败';
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch {
          // Ignore JSON parse error
        }
        
        throw new PreferencesServiceError(errorMessage, response.status);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof PreferencesServiceError) {
        throw error;
      }
      
      if (error instanceof Error) {
        throw new PreferencesServiceError(
          error.message || '更新用户偏好时发生错误'
        );
      }
      
      throw new PreferencesServiceError('更新用户偏好时发生未知错误');
    }
  }
}