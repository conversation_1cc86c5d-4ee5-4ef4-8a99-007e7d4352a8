import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import {
  YStack,
  Input,
  Button,
  Text,
  H2,
  ScrollView,
} from 'tamagui';
import { Mail, Lock, User } from '@tamagui/lucide-icons';

export default function RegisterScreen() {
  const router = useRouter();
  const [displayName, setDisplayName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleRegister = async () => {
    if (!displayName || !email || !password || !confirmPassword) {
      setError('请填写所有字段');
      return;
    }

    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    if (password.length < 6) {
      setError('密码至少需要6个字符');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // TODO: 调用注册API
      // const response = await authService.register(displayName, email, password);
      // useAuthStore.getState().setAuth(response.user, response.token, response.refreshToken);
      
      // 暂时模拟注册成功
      setTimeout(() => {
        setIsLoading(false);
        router.back();
      }, 1000);
    } catch (error) {
      setIsLoading(false);
      setError('注册失败，请稍后重试');
    }
  };

  return (
    <ScrollView flex={1} backgroundColor="$background">
      <YStack flex={1} padding="$4" space="$4" justifyContent="center">
        <YStack space="$2" alignItems="center" marginBottom="$6">
          <H2 fontSize="$8" fontWeight="bold" color="$orange11">
            创建账户
          </H2>
          <Text fontSize="$4" color="$gray11">
            加入美食魔法，开启美味之旅
          </Text>
        </YStack>

        <YStack space="$3">
          <YStack space="$2">
            <Text fontSize="$3" color="$gray11" fontWeight="600">
              昵称
            </Text>
            <Input
              placeholder="请输入昵称"
              value={displayName}
              onChangeText={setDisplayName}
              autoCapitalize="none"
              icon={User}
              backgroundColor="$backgroundHover"
              borderColor="$borderColor"
              focusStyle={{
                borderColor: '$orange8',
              }}
              accessibilityLabel="昵称输入框"
            />
          </YStack>

          <YStack space="$2">
            <Text fontSize="$3" color="$gray11" fontWeight="600">
              邮箱
            </Text>
            <Input
              placeholder="请输入邮箱"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              icon={Mail}
              backgroundColor="$backgroundHover"
              borderColor="$borderColor"
              focusStyle={{
                borderColor: '$orange8',
              }}
              accessibilityLabel="邮箱输入框"
            />
          </YStack>

          <YStack space="$2">
            <Text fontSize="$3" color="$gray11" fontWeight="600">
              密码
            </Text>
            <Input
              placeholder="请输入密码（至少6位）"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              autoCapitalize="none"
              autoComplete="password-new"
              icon={Lock}
              backgroundColor="$backgroundHover"
              borderColor="$borderColor"
              focusStyle={{
                borderColor: '$orange8',
              }}
              accessibilityLabel="密码输入框"
            />
          </YStack>

          <YStack space="$2">
            <Text fontSize="$3" color="$gray11" fontWeight="600">
              确认密码
            </Text>
            <Input
              placeholder="请再次输入密码"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
              autoCapitalize="none"
              autoComplete="password-new"
              icon={Lock}
              backgroundColor="$backgroundHover"
              borderColor="$borderColor"
              focusStyle={{
                borderColor: '$orange8',
              }}
              accessibilityLabel="确认密码输入框"
            />
          </YStack>
        </YStack>

        {error && (
          <YStack
            backgroundColor="$red3"
            padding="$3"
            borderRadius="$4"
            borderWidth={1}
            borderColor="$red6"
          >
            <Text color="$red11" fontSize="$3">
              {error}
            </Text>
          </YStack>
        )}

        <YStack space="$3" marginTop="$4">
          <Button
            size="$5"
            backgroundColor="$orange10"
            color="white"
            onPress={handleRegister}
            disabled={isLoading}
            accessibilityLabel="注册按钮"
          >
            {isLoading ? '注册中...' : '注册'}
          </Button>

          <Button
            size="$4"
            chromeless
            color="$orange10"
            onPress={() => {
              router.back();
              router.push('/(auth)/login');
            }}
            accessibilityLabel="前往登录"
          >
            已有账户？立即登录
          </Button>

          <Text fontSize="$2" color="$gray10" textAlign="center" marginTop="$2">
            注册即表示您同意我们的服务条款和隐私政策
          </Text>
        </YStack>
      </YStack>
    </ScrollView>
  );
}