import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import {
  YStack,
  Input,
  Button,
  Text,
  H2,
  ScrollView,
} from 'tamagui';
import { Mail, Lock } from '@tamagui/lucide-icons';

export default function LoginScreen() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = async () => {
    if (!email || !password) {
      setError('请填写邮箱和密码');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // TODO: 调用登录API
      // const response = await authService.login(email, password);
      // useAuthStore.getState().setAuth(response.user, response.token, response.refreshToken);
      
      // 暂时模拟登录成功
      setTimeout(() => {
        setIsLoading(false);
        router.back();
      }, 1000);
    } catch (error) {
      setIsLoading(false);
      setError('登录失败，请检查邮箱和密码');
    }
  };

  return (
    <ScrollView flex={1} backgroundColor="$background">
      <YStack flex={1} padding="$4" space="$4" justifyContent="center">
        <YStack space="$2" alignItems="center" marginBottom="$6">
          <H2 fontSize="$8" fontWeight="bold" color="$orange11">
            欢迎回来
          </H2>
          <Text fontSize="$4" color="$gray11">
            登录您的美食魔法账户
          </Text>
        </YStack>

        <YStack space="$3">
          <YStack space="$2">
            <Text fontSize="$3" color="$gray11" fontWeight="600">
              邮箱
            </Text>
            <Input
              placeholder="请输入邮箱"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              icon={Mail}
              backgroundColor="$backgroundHover"
              borderColor="$borderColor"
              focusStyle={{
                borderColor: '$orange8',
              }}
              accessibilityLabel="邮箱输入框"
            />
          </YStack>

          <YStack space="$2">
            <Text fontSize="$3" color="$gray11" fontWeight="600">
              密码
            </Text>
            <Input
              placeholder="请输入密码"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              autoCapitalize="none"
              autoComplete="password"
              icon={Lock}
              backgroundColor="$backgroundHover"
              borderColor="$borderColor"
              focusStyle={{
                borderColor: '$orange8',
              }}
              accessibilityLabel="密码输入框"
            />
          </YStack>
        </YStack>

        {error && (
          <YStack
            backgroundColor="$red3"
            padding="$3"
            borderRadius="$4"
            borderWidth={1}
            borderColor="$red6"
          >
            <Text color="$red11" fontSize="$3">
              {error}
            </Text>
          </YStack>
        )}

        <YStack space="$3" marginTop="$4">
          <Button
            size="$5"
            backgroundColor="$orange10"
            color="white"
            onPress={handleLogin}
            disabled={isLoading}
            accessibilityLabel="登录按钮"
          >
            {isLoading ? '登录中...' : '登录'}
          </Button>

          <Button
            size="$4"
            chromeless
            color="$orange10"
            onPress={() => {
              router.back();
              router.push('/(auth)/register');
            }}
            accessibilityLabel="前往注册"
          >
            还没有账户？立即注册
          </Button>

          <Button
            size="$4"
            chromeless
            color="$gray10"
            onPress={() => {}}
            accessibilityLabel="忘记密码"
          >
            忘记密码？
          </Button>
        </YStack>
      </YStack>
    </ScrollView>
  );
}