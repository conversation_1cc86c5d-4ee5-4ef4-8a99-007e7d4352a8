import React from 'react';
import { Tabs } from 'expo-router';
import { Home, Heart, User } from '@tamagui/lucide-icons';
import { useTheme } from 'tamagui';

export default function TabLayout() {
  const theme = useTheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.orange10.val,
        tabBarInactiveTintColor: theme.gray10.val,
        tabBarStyle: {
          backgroundColor: theme.background.val,
          borderTopWidth: 1,
          borderTopColor: theme.borderColor.val,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerStyle: {
          backgroundColor: theme.backgroundHover.val,
        },
        headerTintColor: theme.orange11.val,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: '食材输入',
          tabBarLabel: '首页',
          tabBarIcon: ({ color, size }) => (
            <Home size={size} color={color} />
          ),
          tabBarAccessibilityLabel: '首页',
        }}
      />
      <Tabs.Screen
        name="favorites"
        options={{
          title: '我的收藏',
          tabBarLabel: '收藏',
          tabBarIcon: ({ color, size }) => (
            <Heart size={size} color={color} />
          ),
          tabBarAccessibilityLabel: '收藏夹',
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: '个人设置',
          tabBarLabel: '我的',
          tabBarIcon: ({ color, size }) => (
            <User size={size} color={color} />
          ),
          tabBarAccessibilityLabel: '个人设置',
        }}
      />
    </Tabs>
  );
}