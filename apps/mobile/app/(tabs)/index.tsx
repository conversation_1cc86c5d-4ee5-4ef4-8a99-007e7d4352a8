import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import {
  YStack,
  XStack,
  Input,
  Button,
  Text,
  H2,
  ScrollView,
  Card,
} from 'tamagui';
import { Plus, X, ChefHat } from '@tamagui/lucide-icons';
import { useRecipeStore } from '../../stores/recipe.store';

export default function HomeScreen() {
  const router = useRouter();
  const { generateRecipe, isLoading, error } = useRecipeStore();
  const [ingredients, setIngredients] = useState<string[]>([]);
  const [currentIngredient, setCurrentIngredient] = useState('');

  const handleAddIngredient = () => {
    if (currentIngredient.trim()) {
      setIngredients([...ingredients, currentIngredient.trim()]);
      setCurrentIngredient('');
    }
  };

  const handleRemoveIngredient = (index: number) => {
    setIngredients(ingredients.filter((_, i) => i !== index));
  };

  const handleGenerateRecipe = async () => {
    if (ingredients.length === 0) {
      return;
    }

    try {
      await generateRecipe(ingredients);
      // 导航到食谱页面
      router.push('/recipe');
    } catch (error) {
      console.error('生成食谱失败:', error);
    }
  };

  return (
    <ScrollView flex={1} backgroundColor="$background">
      <YStack flex={1} padding="$4" space="$4">
        <YStack space="$2" alignItems="center">
          <ChefHat size={48} color="$orange10" />
          <H2 fontSize="$8" fontWeight="bold" color="$orange11" textAlign="center">
            今天想做什么？
          </H2>
          <Text fontSize="$4" color="$gray11" textAlign="center">
            输入您的食材，让AI为您生成美味食谱
          </Text>
        </YStack>

        <Card
          padded
          bordered
          backgroundColor="$backgroundHover"
          borderColor="$borderColor"
          borderRadius="$4"
        >
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="600" color="$gray12">
              添加食材
            </Text>
            
            <XStack space="$2">
              <Input
                flex={1}
                placeholder="输入食材名称..."
                value={currentIngredient}
                onChangeText={setCurrentIngredient}
                onSubmitEditing={handleAddIngredient}
                returnKeyType="done"
                backgroundColor="$background"
                borderColor="$borderColor"
                focusStyle={{
                  borderColor: '$orange8',
                }}
                accessibilityLabel="食材输入框"
              />
              <Button
                size="$4"
                icon={Plus}
                onPress={handleAddIngredient}
                backgroundColor="$orange10"
                color="white"
                disabled={!currentIngredient.trim()}
                accessibilityLabel="添加食材"
              >
                添加
              </Button>
            </XStack>

            {ingredients.length > 0 && (
              <YStack space="$2">
                <Text fontSize="$3" color="$gray11">
                  已添加的食材：
                </Text>
                <XStack flexWrap="wrap" gap="$2">
                  {ingredients.map((ingredient, index) => (
                    <XStack
                      key={index}
                      backgroundColor="$orange3"
                      paddingHorizontal="$3"
                      paddingVertical="$2"
                      borderRadius="$10"
                      alignItems="center"
                      space="$1"
                      animation="quick"
                      enterStyle={{ opacity: 0, scale: 0.9 }}
                    >
                      <Text fontSize="$3" color="$orange11">
                        {ingredient}
                      </Text>
                      <Button
                        size="$2"
                        circular
                        chromeless
                        icon={X}
                        onPress={() => handleRemoveIngredient(index)}
                        color="$orange11"
                        accessibilityLabel={`删除${ingredient}`}
                      />
                    </XStack>
                  ))}
                </XStack>
              </YStack>
            )}
          </YStack>
        </Card>

        {error && (
          <Card
            padded
            backgroundColor="$red3"
            borderColor="$red6"
            borderWidth={1}
            borderRadius="$4"
          >
            <Text color="$red11" fontSize="$3">
              {error}
            </Text>
          </Card>
        )}

        <Button
          size="$5"
          backgroundColor="$orange10"
          color="white"
          onPress={handleGenerateRecipe}
          disabled={ingredients.length === 0 || isLoading}
          opacity={ingredients.length === 0 ? 0.5 : 1}
          icon={ChefHat}
          accessibilityLabel="生成食谱"
        >
          {isLoading ? '正在生成食谱...' : '生成食谱'}
        </Button>

        {ingredients.length === 0 && (
          <YStack space="$3" marginTop="$4">
            <Text fontSize="$4" fontWeight="600" color="$gray11">
              推荐食材组合
            </Text>
            <YStack space="$2">
              <Card
                padded
                pressStyle={{ scale: 0.98 }}
                onPress={() => setIngredients(['鸡胸肉', '西兰花', '胡萝卜'])}
                backgroundColor="$background"
                borderColor="$borderColor"
                borderRadius="$3"
              >
                <Text fontSize="$3" color="$gray12">
                  🥗 健康轻食：鸡胸肉、西兰花、胡萝卜
                </Text>
              </Card>
              <Card
                padded
                pressStyle={{ scale: 0.98 }}
                onPress={() => setIngredients(['土豆', '牛肉', '洋葱'])}
                backgroundColor="$background"
                borderColor="$borderColor"
                borderRadius="$3"
              >
                <Text fontSize="$3" color="$gray12">
                  🍖 家常美味：土豆、牛肉、洋葱
                </Text>
              </Card>
              <Card
                padded
                pressStyle={{ scale: 0.98 }}
                onPress={() => setIngredients(['豆腐', '香菇', '青菜'])}
                backgroundColor="$background"
                borderColor="$borderColor"
                borderRadius="$3"
              >
                <Text fontSize="$3" color="$gray12">
                  🥢 素食养生：豆腐、香菇、青菜
                </Text>
              </Card>
            </YStack>
          </YStack>
        )}
      </YStack>
    </ScrollView>
  );
}