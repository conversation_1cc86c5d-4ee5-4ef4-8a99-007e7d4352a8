import React from 'react';
import { useRouter } from 'expo-router';
import {
  YStack,
  XStack,
  Card,
  Text,
  H2,
  Button,
  Avatar,
  ScrollView,
  Separator,
} from 'tamagui';
import {
  User,
  Settings,
  LogOut,
  ChevronRight,
  Bell,
  Lock,
  HelpCircle,
} from '@tamagui/lucide-icons';
import { useAuthStore } from '../../stores/auth.store';

export default function ProfileScreen() {
  const router = useRouter();
  const { isAuthenticated, user, clearAuth } = useAuthStore();

  const handleLogout = () => {
    clearAuth();
    router.push('/');
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  // 未登录状态
  if (!isAuthenticated) {
    return (
      <YStack flex={1} backgroundColor="$background" padding="$4">
        <YStack flex={1} justifyContent="center" alignItems="center" space="$4">
          <User size={64} color="$gray8" />
          <H2 fontSize="$7" fontWeight="600" color="$gray11" textAlign="center">
            欢迎来到美食魔法
          </H2>
          <Text fontSize="$4" color="$gray10" textAlign="center" maxWidth={300}>
            登录后可以保存和管理您的食谱收藏
          </Text>
          <YStack space="$3" width="100%" maxWidth={300}>
            <Button
              size="$4"
              backgroundColor="$orange10"
              color="white"
              onPress={handleLogin}
              accessibilityLabel="登录"
            >
              登录
            </Button>
            <Button
              size="$4"
              chromeless
              color="$orange10"
              onPress={() => router.push('/(auth)/register')}
              accessibilityLabel="注册"
            >
              还没有账户？立即注册
            </Button>
          </YStack>
        </YStack>
      </YStack>
    );
  }

  // 已登录状态
  return (
    <ScrollView flex={1} backgroundColor="$background">
      <YStack padding="$4" space="$4">
        {/* 用户信息卡片 */}
        <Card
          padded
          bordered
          backgroundColor="$backgroundHover"
          borderColor="$borderColor"
          borderRadius="$6"
        >
          <XStack space="$3" alignItems="center">
            <Avatar circular size="$6" backgroundColor="$orange5">
              <Avatar.Image
                source={{ uri: user?.avatarUrl || undefined }}
              />
              <Avatar.Fallback backgroundColor="$orange5">
                <User size={32} color="$orange10" />
              </Avatar.Fallback>
            </Avatar>
            <YStack flex={1}>
              <Text fontSize="$5" fontWeight="bold" color="$gray12">
                {user?.displayName || '美食爱好者'}
              </Text>
              <Text fontSize="$3" color="$gray11">
                {user?.email || '未设置邮箱'}
              </Text>
              <XStack marginTop="$1">
                <Text
                  fontSize="$2"
                  color="$orange10"
                  backgroundColor="$orange3"
                  paddingHorizontal="$2"
                  paddingVertical="$1"
                  borderRadius="$2"
                >
                  {user?.subscriptionTier === 'PREMIUM' ? '高级会员' : '免费用户'}
                </Text>
              </XStack>
            </YStack>
          </XStack>
        </Card>

        {/* 设置选项 */}
        <YStack space="$2">
          <Text fontSize="$4" fontWeight="600" color="$gray11" marginBottom="$2">
            账户设置
          </Text>
          
          <Card
            padded
            bordered
            backgroundColor="$background"
            borderColor="$borderColor"
            borderRadius="$4"
          >
            <YStack>
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="个人信息"
              >
                <XStack space="$3" alignItems="center">
                  <User size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    个人信息
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
              
              <Separator />
              
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="通知设置"
              >
                <XStack space="$3" alignItems="center">
                  <Bell size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    通知设置
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
              
              <Separator />
              
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="隐私与安全"
              >
                <XStack space="$3" alignItems="center">
                  <Lock size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    隐私与安全
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
            </YStack>
          </Card>
        </YStack>

        {/* 其他选项 */}
        <YStack space="$2">
          <Text fontSize="$4" fontWeight="600" color="$gray11" marginBottom="$2">
            更多
          </Text>
          
          <Card
            padded
            bordered
            backgroundColor="$background"
            borderColor="$borderColor"
            borderRadius="$4"
          >
            <YStack>
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="帮助中心"
              >
                <XStack space="$3" alignItems="center">
                  <HelpCircle size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    帮助中心
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
              
              <Separator />
              
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="设置"
              >
                <XStack space="$3" alignItems="center">
                  <Settings size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    设置
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
            </YStack>
          </Card>
        </YStack>

        {/* 退出登录 */}
        <Button
          size="$4"
          backgroundColor="$red9"
          color="white"
          icon={LogOut}
          onPress={handleLogout}
          marginTop="$4"
          accessibilityLabel="退出登录"
        >
          退出登录
        </Button>

        {/* 版本信息 */}
        <YStack alignItems="center" marginTop="$4">
          <Text fontSize="$2" color="$gray10">
            美食魔法 v1.0.0
          </Text>
          <Text fontSize="$2" color="$gray10">
            © 2025 FoodMagic
          </Text>
        </YStack>
      </YStack>
    </ScrollView>
  );
}