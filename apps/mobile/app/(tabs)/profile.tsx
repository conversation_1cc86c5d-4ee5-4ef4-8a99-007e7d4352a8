import React, { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import {
  YStack,
  XStack,
  Card,
  Text,
  H2,
  Button,
  Avatar,
  ScrollView,
  Separator,
  Spinner,
  useToastController,
} from 'tamagui';
import {
  User,
  Settings,
  LogOut,
  ChevronRight,
  ChevronDown,
  Bell,
  Lock,
  HelpCircle,
  Utensils,
  Save,
} from '@tamagui/lucide-icons';
import { useAuthStore } from '../../stores/auth.store';
import { usePreferencesStore } from '../../stores/preferences.store';
import { DietTypeSelector } from '../../components/preferences/DietTypeSelector';
import { CuisineSelector } from '../../components/preferences/CuisineSelector';
import { PreferencesService } from '../../services/preferences.service';

export default function ProfileScreen() {
  const router = useRouter();
  const toast = useToastController();
  const { isAuthenticated, user, clearAuth } = useAuthStore();
  const [isPreferencesExpanded, setIsPreferencesExpanded] = useState(false);
  
  const {
    preferences,
    isLoading,
    error,
    hasChanges,
    setPreferences,
    updateDiet,
    updateCuisines,
    setLoading,
    setError,
    resetChanges,
  } = usePreferencesStore();

  // Load preferences when component mounts and user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadPreferences();
    }
  }, [isAuthenticated]);

  const loadPreferences = async () => {
    setLoading(true);
    setError(null);
    try {
      const userPreferences = await PreferencesService.getUserPreferences();
      setPreferences(userPreferences);
    } catch (error) {
      console.error('Failed to load preferences:', error);
      setError('无法加载偏好设置');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePreferences = async () => {
    setLoading(true);
    setError(null);
    try {
      const updatedPreferences = await PreferencesService.updateUserPreferences(preferences);
      setPreferences(updatedPreferences);
      resetChanges();
      toast.show('偏好设置已保存', {
        duration: 2000,
        preset: 'done',
      });
    } catch (error) {
      console.error('Failed to save preferences:', error);
      setError('保存失败，请重试');
      toast.show('保存失败，请重试', {
        duration: 3000,
        preset: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    clearAuth();
    router.push('/');
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  // 未登录状态
  if (!isAuthenticated) {
    return (
      <YStack flex={1} backgroundColor="$background" padding="$4">
        <YStack flex={1} justifyContent="center" alignItems="center" space="$4">
          <User size={64} color="$gray8" />
          <H2 fontSize="$7" fontWeight="600" color="$gray11" textAlign="center">
            欢迎来到美食魔法
          </H2>
          <Text fontSize="$4" color="$gray10" textAlign="center" maxWidth={300}>
            登录后可以保存和管理您的食谱收藏
          </Text>
          <YStack space="$3" width="100%" maxWidth={300}>
            <Button
              size="$4"
              backgroundColor="$orange10"
              color="white"
              onPress={handleLogin}
              accessibilityLabel="登录"
            >
              登录
            </Button>
            <Button
              size="$4"
              chromeless
              color="$orange10"
              onPress={() => router.push('/(auth)/register')}
              accessibilityLabel="注册"
            >
              还没有账户？立即注册
            </Button>
          </YStack>
        </YStack>
      </YStack>
    );
  }

  // 已登录状态
  return (
    <ScrollView flex={1} backgroundColor="$background">
      <YStack padding="$4" space="$4">
        {/* 用户信息卡片 */}
        <Card
          padded
          bordered
          backgroundColor="$backgroundHover"
          borderColor="$borderColor"
          borderRadius="$6"
        >
          <XStack space="$3" alignItems="center">
            <Avatar circular size="$6" backgroundColor="$orange5">
              <Avatar.Image
                source={{ uri: user?.avatarUrl || undefined }}
              />
              <Avatar.Fallback backgroundColor="$orange5">
                <User size={32} color="$orange10" />
              </Avatar.Fallback>
            </Avatar>
            <YStack flex={1}>
              <Text fontSize="$5" fontWeight="bold" color="$gray12">
                {user?.displayName || '美食爱好者'}
              </Text>
              <Text fontSize="$3" color="$gray11">
                {user?.email || '未设置邮箱'}
              </Text>
              <XStack marginTop="$1">
                <Text
                  fontSize="$2"
                  color="$orange10"
                  backgroundColor="$orange3"
                  paddingHorizontal="$2"
                  paddingVertical="$1"
                  borderRadius="$2"
                >
                  {user?.subscriptionTier === 'PREMIUM' ? '高级会员' : '免费用户'}
                </Text>
              </XStack>
            </YStack>
          </XStack>
        </Card>

        {/* 设置选项 */}
        <YStack space="$2">
          <Text fontSize="$4" fontWeight="600" color="$gray11" marginBottom="$2">
            账户设置
          </Text>
          
          <Card
            padded
            bordered
            backgroundColor="$background"
            borderColor="$borderColor"
            borderRadius="$4"
          >
            <YStack>
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="个人信息"
              >
                <XStack space="$3" alignItems="center">
                  <User size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    个人信息
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
              
              <Separator />
              
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="通知设置"
              >
                <XStack space="$3" alignItems="center">
                  <Bell size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    通知设置
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
              
              <Separator />
              
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="隐私与安全"
              >
                <XStack space="$3" alignItems="center">
                  <Lock size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    隐私与安全
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
            </YStack>
          </Card>
        </YStack>

        {/* 高级饮食偏好 */}
        <YStack space="$2">
          <Text fontSize="$4" fontWeight="600" color="$gray11" marginBottom="$2">
            高级饮食偏好
          </Text>
          
          <Card
            padded
            bordered
            backgroundColor="$background"
            borderColor="$borderColor"
            borderRadius="$4"
          >
            <YStack>
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => setIsPreferencesExpanded(!isPreferencesExpanded)}
                accessibilityRole="button"
                accessibilityLabel="高级饮食偏好设置"
              >
                <XStack space="$3" alignItems="center">
                  <Utensils size={20} color="$gray10" />
                  <YStack flex={1}>
                    <Text fontSize="$4" color="$gray12">
                      饮食目标与菜系偏好
                    </Text>
                    <Text fontSize="$2" color="$gray10" marginTop="$1">
                      设置您的饮食目标和喜爱的菜系
                    </Text>
                  </YStack>
                </XStack>
                {isPreferencesExpanded ? (
                  <ChevronDown size={20} color="$gray10" />
                ) : (
                  <ChevronRight size={20} color="$gray10" />
                )}
              </XStack>
              
              {isPreferencesExpanded && (
                <YStack
                  paddingTop="$3"
                  borderTopWidth={1}
                  borderTopColor="$borderColor"
                  marginTop="$3"
                  space="$4"
                >
                  {isLoading ? (
                    <YStack alignItems="center" padding="$4">
                      <Spinner size="large" color="$orange10" />
                      <Text fontSize="$3" color="$gray10" marginTop="$2">
                        加载中...
                      </Text>
                    </YStack>
                  ) : (
                    <>
                      <DietTypeSelector
                        value={preferences.diet || 'NONE'}
                        onValueChange={updateDiet}
                        disabled={isLoading}
                      />
                      
                      <Separator marginVertical="$2" />
                      
                      <CuisineSelector
                        value={preferences.preferredCuisines || []}
                        onValueChange={updateCuisines}
                        disabled={isLoading}
                      />
                      
                      {error && (
                        <Text fontSize="$3" color="$red10" textAlign="center">
                          {error}
                        </Text>
                      )}
                      
                      <Button
                        size="$4"
                        backgroundColor="$orange10"
                        color="white"
                        icon={Save}
                        onPress={handleSavePreferences}
                        disabled={!hasChanges || isLoading}
                        opacity={!hasChanges ? 0.5 : 1}
                        marginTop="$3"
                        accessibilityLabel="保存偏好设置"
                      >
                        {isLoading ? '保存中...' : '保存偏好'}
                      </Button>
                      
                      {hasChanges && (
                        <Text fontSize="$2" color="$orange10" textAlign="center">
                          您有未保存的更改
                        </Text>
                      )}
                    </>
                  )}
                </YStack>
              )}
            </YStack>
          </Card>
        </YStack>

        {/* 其他选项 */}
        <YStack space="$2">
          <Text fontSize="$4" fontWeight="600" color="$gray11" marginBottom="$2">
            更多
          </Text>
          
          <Card
            padded
            bordered
            backgroundColor="$background"
            borderColor="$borderColor"
            borderRadius="$4"
          >
            <YStack>
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="帮助中心"
              >
                <XStack space="$3" alignItems="center">
                  <HelpCircle size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    帮助中心
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
              
              <Separator />
              
              <XStack
                paddingVertical="$3"
                justifyContent="space-between"
                alignItems="center"
                pressStyle={{ opacity: 0.7 }}
                onPress={() => {}}
                accessibilityRole="button"
                accessibilityLabel="设置"
              >
                <XStack space="$3" alignItems="center">
                  <Settings size={20} color="$gray10" />
                  <Text fontSize="$4" color="$gray12">
                    设置
                  </Text>
                </XStack>
                <ChevronRight size={20} color="$gray10" />
              </XStack>
            </YStack>
          </Card>
        </YStack>

        {/* 退出登录 */}
        <Button
          size="$4"
          backgroundColor="$red9"
          color="white"
          icon={LogOut}
          onPress={handleLogout}
          marginTop="$4"
          accessibilityLabel="退出登录"
        >
          退出登录
        </Button>

        {/* 版本信息 */}
        <YStack alignItems="center" marginTop="$4">
          <Text fontSize="$2" color="$gray10">
            美食魔法 v1.0.0
          </Text>
          <Text fontSize="$2" color="$gray10">
            © 2025 FoodMagic
          </Text>
        </YStack>
      </YStack>
    </ScrollView>
  );
}