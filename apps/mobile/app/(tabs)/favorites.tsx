import React, { useEffect, useState } from 'react';
import { RefreshControl } from 'react-native';
import { useRouter } from 'expo-router';
import {
  YStack,
  ScrollView,
  H2,
  Text,
  Spinner,
} from 'tamagui';
import { Heart } from '@tamagui/lucide-icons';
import { useRecipeStore } from '../stores/recipe.store';
import { useAuthStore } from '../stores/auth.store';
import SavedRecipesList from '../components/SavedRecipesList';

export default function FavoritesScreen() {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const { 
    favoriteRecipes, 
    isLoading, 
    error, 
    loadFavorites,
    clearError,
  } = useRecipeStore();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      loadFavorites();
    }
  }, [isAuthenticated]);

  const handleRefresh = async () => {
    setRefreshing(true);
    clearError();
    await loadFavorites();
    setRefreshing(false);
  };

  const handleRecipePress = (recipeId: string) => {
    // 导航到食谱详情页
    router.push(`/recipe/${recipeId}`);
  };

  // 未登录状态
  if (!isAuthenticated) {
    return (
      <YStack flex={1} backgroundColor="$background" padding="$4">
        <YStack flex={1} justifyContent="center" alignItems="center" space="$4">
          <Heart size={64} color="$gray8" />
          <H2 fontSize="$7" fontWeight="600" color="$gray11" textAlign="center">
            登录后查看收藏
          </H2>
          <Text fontSize="$4" color="$gray10" textAlign="center" maxWidth={300}>
            登录您的账户，即可查看和管理您收藏的美味食谱
          </Text>
        </YStack>
      </YStack>
    );
  }

  // 加载中状态
  if (isLoading && !refreshing && favoriteRecipes.length === 0) {
    return (
      <YStack flex={1} backgroundColor="$background" justifyContent="center" alignItems="center">
        <Spinner size="large" color="$orange10" />
        <Text marginTop="$3" color="$gray11">
          正在加载您的收藏...
        </Text>
      </YStack>
    );
  }

  return (
    <YStack flex={1} backgroundColor="$background">
      <ScrollView
        flex={1}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#FB8C00"
            colors={['#FB8C00']}
          />
        }
      >
        <YStack padding="$4" space="$4">
          <YStack space="$2">
            <H2 fontSize="$8" fontWeight="bold" color="$orange11">
              我的收藏
            </H2>
            {favoriteRecipes.length > 0 && (
              <Text fontSize="$4" color="$gray11">
                共 {favoriteRecipes.length} 个食谱
              </Text>
            )}
          </YStack>

          {error && (
            <YStack
              backgroundColor="$red3"
              padding="$3"
              borderRadius="$4"
              borderWidth={1}
              borderColor="$red6"
            >
              <Text color="$red11" fontSize="$3">
                {error}
              </Text>
            </YStack>
          )}

          {favoriteRecipes.length === 0 && !error ? (
            <YStack
              flex={1}
              paddingTop="$10"
              alignItems="center"
              space="$4"
            >
              <Heart size={80} color="$gray5" />
              <YStack space="$2" alignItems="center">
                <Text fontSize="$6" fontWeight="600" color="$gray11">
                  还没有收藏的食谱
                </Text>
                <Text fontSize="$4" color="$gray10" textAlign="center" maxWidth={280}>
                  探索美味食谱，点击心形图标即可收藏您喜欢的食谱
                </Text>
              </YStack>
            </YStack>
          ) : (
            <SavedRecipesList
              recipes={favoriteRecipes}
              onRecipePress={handleRecipePress}
              showFavoriteButton
            />
          )}
        </YStack>
      </ScrollView>
    </YStack>
  );
}