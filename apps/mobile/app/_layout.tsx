import { Stack } from 'expo-router';
import { TamaguiProvider } from 'tamagui';
import { useFonts } from 'expo-font';
import config from '../tamagui.config';

export default function RootLayout() {
  const [loaded] = useFonts({
    Inter: require('@tamagui/font-inter/otf/Inter-Medium.otf'),
    InterBold: require('@tamagui/font-inter/otf/Inter-Bold.otf'),
  });

  if (!loaded) {
    return null;
  }

  return (
    <TamaguiProvider config={config}>
      <Stack>
        <Stack.Screen 
          name="(tabs)" 
          options={{ headerShown: false }} 
        />
        <Stack.Screen 
          name="recipe" 
          options={{ 
            title: '食谱详情',
            headerStyle: {
              backgroundColor: '#FFF3E0',
            },
            headerTintColor: '#E65100',
          }} 
        />
        <Stack.Screen 
          name="(auth)/login" 
          options={{ 
            title: '登录',
            presentation: 'modal',
          }} 
        />
        <Stack.Screen 
          name="(auth)/register" 
          options={{ 
            title: '注册',
            presentation: 'modal',
          }} 
        />
      </Stack>
    </TamaguiProvider>
  );
}