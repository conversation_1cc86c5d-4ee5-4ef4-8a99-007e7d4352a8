import React, { useEffect } from 'react';
import {
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
  AccessibilityInfo,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { YStack, XStack, Text, Button, Card } from 'tamagui';
import { ChevronLeft, RefreshCw, AlertCircle } from '@tamagui/lucide-icons';
import { useRecipeStore } from '../stores/recipe.store';
import RecipeDisplay from '../components/RecipeDisplay';
import RecipeLoading from '../components/RecipeLoading';

export default function RecipePage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const ingredients = params.ingredients ? JSON.parse(params.ingredients as string) : [];
  
  const {
    currentRecipe,
    isLoading,
    error,
    generateRecipe,
    clearError,
    refreshRecipe,
  } = useRecipeStore();

  useEffect(() => {
    if (ingredients.length > 0 && !currentRecipe) {
      generateRecipe(ingredients);
      AccessibilityInfo.announceForAccessibility('正在为您生成食谱');
    }
  }, [ingredients, currentRecipe, generateRecipe]);

  const handleBack = () => {
    router.back();
  };

  const handleRetry = () => {
    clearError();
    if (ingredients.length > 0) {
      generateRecipe(ingredients);
    }
  };

  const handleRefresh = () => {
    if (ingredients.length > 0) {
      refreshRecipe(ingredients);
    }
  };

  if (isLoading && !currentRecipe) {
    return <RecipeLoading />;
  }

  if (error) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
        <YStack flex={1} padding="$4" space="$4">
          <XStack alignItems="center" space="$2">
            <Button
              size="$3"
              circular
              icon={ChevronLeft}
              onPress={handleBack}
              accessibilityLabel="返回上一页"
              accessibilityRole="button"
            />
            <Text fontSize="$6" fontWeight="bold">食谱生成</Text>
          </XStack>

          <YStack flex={1} justifyContent="center" alignItems="center" space="$4">
            <Card
              padded
              bordered
              backgroundColor="$red2"
              borderColor="$red6"
              animation="quick"
              enterStyle={{ opacity: 0, scale: 0.9 }}
              accessibilityRole="alert"
            >
              <YStack space="$3" alignItems="center">
                <AlertCircle size={48} color="$red10" />
                <Text fontSize="$5" color="$red11" textAlign="center">
                  哎呀，出了点小问题
                </Text>
                <Text fontSize="$3" color="$gray11" textAlign="center">
                  {error || '生成食谱时遇到了问题，请稍后再试'}
                </Text>
              </YStack>
            </Card>

            <Button
              size="$4"
              theme="active"
              icon={RefreshCw}
              onPress={handleRetry}
              accessibilityLabel="重新生成食谱"
              accessibilityRole="button"
            >
              重新尝试
            </Button>
          </YStack>
        </YStack>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={handleRefresh}
            tintColor="#FF6B35"
            colors={['#FF6B35']}
            accessibilityLabel="下拉刷新食谱"
          />
        }
        contentContainerStyle={{ flexGrow: 1 }}
        accessibilityRole="scrollview"
      >
        <YStack flex={1} padding="$4" space="$4">
          <XStack alignItems="center" justifyContent="space-between">
            <XStack alignItems="center" space="$2">
              <Button
                size="$3"
                circular
                icon={ChevronLeft}
                onPress={handleBack}
                accessibilityLabel="返回上一页"
                accessibilityRole="button"
              />
              <Text fontSize="$6" fontWeight="bold">美味食谱</Text>
            </XStack>
            
            {currentRecipe && (
              <Button
                size="$3"
                circular
                icon={RefreshCw}
                onPress={handleRefresh}
                disabled={isLoading}
                opacity={isLoading ? 0.5 : 1}
                accessibilityLabel="重新生成食谱"
                accessibilityRole="button"
              />
            )}
          </XStack>

          {currentRecipe ? (
            <RecipeDisplay recipe={currentRecipe} />
          ) : (
            <YStack flex={1} justifyContent="center" alignItems="center">
              <Text fontSize="$4" color="$gray10">
                暂无食谱数据
              </Text>
            </YStack>
          )}
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}