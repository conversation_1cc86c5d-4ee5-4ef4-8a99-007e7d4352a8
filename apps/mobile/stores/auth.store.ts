import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { UserDto } from '@foodmagic/shared-types';

interface AuthState {
  isAuthenticated: boolean;
  user: UserDto | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setAuth: (user: UserDto, token: string, refreshToken: string) => void;
  clearAuth: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  checkAuthStatus: () => Promise<boolean>;
  refreshTokens: () => Promise<boolean>;
}

// SecureStore keys
const TOKEN_KEY = 'foodmagic_auth_token';
const REFRESH_TOKEN_KEY = 'foodmagic_refresh_token';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      isLoading: false,
      error: null,

      setAuth: async (user, token, refreshToken) => {
        try {
          // 存储token到安全存储
          await SecureStore.setItemAsync(TOKEN_KEY, token);
          await SecureStore.setItemAsync(REFRESH_TOKEN_KEY, refreshToken);
          
          set({
            isAuthenticated: true,
            user,
            token,
            refreshToken,
            error: null,
          });
        } catch (error) {
          console.error('Failed to save auth tokens:', error);
          set({ error: '保存认证信息失败' });
        }
      },

      clearAuth: async () => {
        try {
          // 清除安全存储中的token
          await SecureStore.deleteItemAsync(TOKEN_KEY);
          await SecureStore.deleteItemAsync(REFRESH_TOKEN_KEY);
          
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            refreshToken: null,
            error: null,
          });
        } catch (error) {
          console.error('Failed to clear auth tokens:', error);
        }
      },

      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),

      checkAuthStatus: async () => {
        try {
          const token = await SecureStore.getItemAsync(TOKEN_KEY);
          const refreshToken = await SecureStore.getItemAsync(REFRESH_TOKEN_KEY);
          
          if (!token || !refreshToken) {
            get().clearAuth();
            return false;
          }

          // TODO: 验证token是否有效
          // 可以调用后端的验证端点或检查token过期时间
          
          set({
            token,
            refreshToken,
            isAuthenticated: true,
          });
          
          return true;
        } catch (error) {
          console.error('Failed to check auth status:', error);
          return false;
        }
      },

      refreshTokens: async () => {
        try {
          const refreshToken = get().refreshToken;
          if (!refreshToken) {
            get().clearAuth();
            return false;
          }

          // TODO: 调用后端刷新token的API
          // const response = await authApi.refreshToken(refreshToken);
          // const { token: newToken, refreshToken: newRefreshToken, user } = response;
          // get().setAuth(user, newToken, newRefreshToken);
          
          return true;
        } catch (error) {
          console.error('Failed to refresh tokens:', error);
          get().clearAuth();
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
      }),
    }
  )
);