import { renderHook, act } from '@testing-library/react-hooks';
import { usePreferencesStore } from './preferences.store';
import { DEFAULT_USER_PREFERENCES } from '@food-magic/shared-types';

describe('usePreferencesStore', () => {
  beforeEach(() => {
    // Reset store to initial state before each test
    const { result } = renderHook(() => usePreferencesStore());
    act(() => {
      result.current.setPreferences(DEFAULT_USER_PREFERENCES);
      result.current.resetChanges();
      result.current.setError(null);
      result.current.setLoading(false);
    });
  });

  it('should initialize with default preferences', () => {
    const { result } = renderHook(() => usePreferencesStore());

    expect(result.current.preferences).toEqual(DEFAULT_USER_PREFERENCES);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.hasChanges).toBe(false);
  });

  it('should update diet type and mark as changed', () => {
    const { result } = renderHook(() => usePreferencesStore());

    act(() => {
      result.current.updateDiet('LOW_CARB');
    });

    expect(result.current.preferences.diet).toBe('LOW_CARB');
    expect(result.current.hasChanges).toBe(true);
  });

  it('should update cuisine preferences and mark as changed', () => {
    const { result } = renderHook(() => usePreferencesStore());

    act(() => {
      result.current.updateCuisines(['ITALIAN', 'MEXICAN']);
    });

    expect(result.current.preferences.preferredCuisines).toEqual(['ITALIAN', 'MEXICAN']);
    expect(result.current.hasChanges).toBe(true);
  });

  it('should set preferences and reset changes', () => {
    const { result } = renderHook(() => usePreferencesStore());

    const newPreferences = {
      ...DEFAULT_USER_PREFERENCES,
      diet: 'KETO' as const,
      preferredCuisines: ['CHINESE', 'JAPANESE'] as const,
    };

    act(() => {
      result.current.setPreferences(newPreferences);
    });

    expect(result.current.preferences).toEqual(newPreferences);
    expect(result.current.hasChanges).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should set loading state', () => {
    const { result } = renderHook(() => usePreferencesStore());

    act(() => {
      result.current.setLoading(true);
    });

    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.setLoading(false);
    });

    expect(result.current.isLoading).toBe(false);
  });

  it('should set error message', () => {
    const { result } = renderHook(() => usePreferencesStore());

    act(() => {
      result.current.setError('测试错误消息');
    });

    expect(result.current.error).toBe('测试错误消息');

    act(() => {
      result.current.setError(null);
    });

    expect(result.current.error).toBe(null);
  });

  it('should reset changes flag', () => {
    const { result } = renderHook(() => usePreferencesStore());

    act(() => {
      result.current.markAsChanged();
    });

    expect(result.current.hasChanges).toBe(true);

    act(() => {
      result.current.resetChanges();
    });

    expect(result.current.hasChanges).toBe(false);
  });

  it('should mark as changed', () => {
    const { result } = renderHook(() => usePreferencesStore());

    act(() => {
      result.current.resetChanges();
    });

    expect(result.current.hasChanges).toBe(false);

    act(() => {
      result.current.markAsChanged();
    });

    expect(result.current.hasChanges).toBe(true);
  });

  it('should preserve other preferences when updating diet', () => {
    const { result } = renderHook(() => usePreferencesStore());

    const initialPreferences = {
      ...DEFAULT_USER_PREFERENCES,
      preferredCuisines: ['ITALIAN'] as const,
      maxCookingTime: 45,
    };

    act(() => {
      result.current.setPreferences(initialPreferences);
    });

    act(() => {
      result.current.updateDiet('HIGH_PROTEIN');
    });

    expect(result.current.preferences.diet).toBe('HIGH_PROTEIN');
    expect(result.current.preferences.preferredCuisines).toEqual(['ITALIAN']);
    expect(result.current.preferences.maxCookingTime).toBe(45);
  });

  it('should preserve other preferences when updating cuisines', () => {
    const { result } = renderHook(() => usePreferencesStore());

    const initialPreferences = {
      ...DEFAULT_USER_PREFERENCES,
      diet: 'PALEO' as const,
      servingSize: 4,
    };

    act(() => {
      result.current.setPreferences(initialPreferences);
    });

    act(() => {
      result.current.updateCuisines(['THAI', 'VIETNAMESE']);
    });

    expect(result.current.preferences.preferredCuisines).toEqual(['THAI', 'VIETNAMESE']);
    expect(result.current.preferences.diet).toBe('PALEO');
    expect(result.current.preferences.servingSize).toBe(4);
  });
});