import { create } from 'zustand';
import { RecipeDto, UserPreferencesDto } from '@foodmagic/shared-types';
import { RecipeService, RecipeServiceError } from '../services/recipe.service';

interface RecipeState {
  // 状态
  currentRecipe: RecipeDto | null;
  savedRecipes: RecipeDto[];
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  lastGeneratedIngredients: string[];
  userPreferences: UserPreferencesDto | null;
  retryCount: number;
  maxRetries: number;
  
  // 操作
  generateRecipe: (ingredients: string[], preferences?: UserPreferencesDto) => Promise<void>;
  refreshRecipe: (ingredients: string[], preferences?: UserPreferencesDto) => Promise<void>;
  saveRecipe: (recipeId: string) => Promise<void>;
  loadSavedRecipes: () => Promise<void>;
  setCurrentRecipe: (recipe: RecipeDto | null) => void;
  setUserPreferences: (preferences: UserPreferencesDto) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  currentRecipe: null,
  savedRecipes: [],
  isLoading: false,
  isSaving: false,
  error: null,
  lastGeneratedIngredients: [],
  userPreferences: null,
  retryCount: 0,
  maxRetries: 3,
};

export const useRecipeStore = create<RecipeState>((set, get) => ({
  ...initialState,

  generateRecipe: async (ingredients: string[], preferences?: UserPreferencesDto) => {
    const { userPreferences, retryCount, maxRetries } = get();
    const finalPreferences = preferences || userPreferences || undefined;
    
    // 检查重试次数
    if (retryCount >= maxRetries) {
      set({ 
        error: '已达到最大重试次数，请稍后再试',
        isLoading: false,
        retryCount: 0,
      });
      return;
    }
    
    set({ 
      isLoading: true, 
      error: null,
      lastGeneratedIngredients: ingredients,
    });

    try {
      const recipe = await RecipeService.generateRecipe(ingredients, finalPreferences);
      set({ 
        currentRecipe: recipe, 
        isLoading: false,
        error: null,
        retryCount: 0, // 成功后重置重试计数
      });
    } catch (error) {
      let errorMessage = '生成食谱时出现问题，请稍后再试';
      
      if (error instanceof RecipeServiceError) {
        errorMessage = error.message;
        
        // 根据错误类型提供更友好的提示
        if (error.statusCode === 429) {
          errorMessage = '请求太频繁了，请稍等一会儿再试';
        } else if (error.statusCode === 503) {
          errorMessage = '服务暂时不可用，请稍后再试';
        } else if (error.message.includes('超时')) {
          errorMessage = '生成食谱需要一些时间，请耐心等待或稍后重试';
        }
      }
      
      set((state) => ({ 
        isLoading: false, 
        error: errorMessage,
        currentRecipe: null,
        retryCount: state.retryCount + 1, // 增加重试计数
      }));
      
      console.error('生成食谱失败:', error);
    }
  },

  refreshRecipe: async (ingredients: string[], preferences?: UserPreferencesDto) => {
    // 刷新会清除当前食谱，重新生成新的
    set({ currentRecipe: null });
    await get().generateRecipe(ingredients, preferences);
  },

  saveRecipe: async (recipeId: string) => {
    set({ isSaving: true, error: null });

    try {
      await RecipeService.saveRecipe(recipeId);
      
      // 更新保存的食谱列表
      const currentRecipe = get().currentRecipe;
      if (currentRecipe && currentRecipe.id === recipeId) {
        set((state) => ({
          savedRecipes: [...state.savedRecipes, currentRecipe],
          isSaving: false,
          error: null,
        }));
      } else {
        set({ isSaving: false });
      }
    } catch (error) {
      let errorMessage = '保存食谱失败';
      
      if (error instanceof RecipeServiceError) {
        errorMessage = error.message;
        
        if (error.statusCode === 401) {
          errorMessage = '请先登录后再保存食谱';
        }
      }
      
      set({ 
        isSaving: false, 
        error: errorMessage,
      });
      
      console.error('保存食谱失败:', error);
      throw error; // 重新抛出以便组件处理
    }
  },

  loadSavedRecipes: async () => {
    set({ isLoading: true, error: null });

    try {
      const recipes = await RecipeService.getSavedRecipes();
      set({ 
        savedRecipes: recipes, 
        isLoading: false,
        error: null,
      });
    } catch (error) {
      let errorMessage = '加载保存的食谱失败';
      
      if (error instanceof RecipeServiceError) {
        errorMessage = error.message;
        
        if (error.statusCode === 401) {
          errorMessage = '请先登录后再查看保存的食谱';
        }
      }
      
      set({ 
        isLoading: false, 
        error: errorMessage,
        savedRecipes: [],
      });
      
      console.error('加载保存的食谱失败:', error);
    }
  },

  setCurrentRecipe: (recipe: RecipeDto | null) => {
    set({ currentRecipe: recipe, error: null });
  },

  setUserPreferences: (preferences: UserPreferencesDto) => {
    set({ userPreferences: preferences });
  },

  clearError: () => {
    set({ error: null, retryCount: 0 });
  },

  reset: () => {
    set(initialState);
  },
}));