import { act, renderHook } from '@testing-library/react-hooks';
import { useRecipeStore } from './recipe.store';
import { RecipeService } from '../services/recipe.service';

// Mock RecipeService
jest.mock('../services/recipe.service');

describe('RecipeStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useRecipeStore.getState().reset();
    jest.clearAllMocks();
  });

  describe('收藏功能', () => {
    it('应该能够加载收藏列表', async () => {
      const mockRecipes = [
        { id: '1', title: '食谱1' },
        { id: '2', title: '食谱2' },
      ];

      (RecipeService.getUserFavorites as jest.Mock).mockResolvedValue(mockRecipes);

      const { result } = renderHook(() => useRecipeStore());

      await act(async () => {
        await result.current.loadFavorites();
      });

      expect(result.current.favoriteRecipes).toEqual(mockRecipes);
      expect(result.current.favoriteRecipeIds.has('1')).toBe(true);
      expect(result.current.favoriteRecipeIds.has('2')).toBe(true);
      expect(result.current.isLoading).toBe(false);
    });

    it('应该能够切换收藏状态（添加收藏）', async () => {
      (RecipeService.favoriteRecipe as jest.Mock).mockResolvedValue(undefined);

      const { result } = renderHook(() => useRecipeStore());

      let isFavorited: boolean;
      await act(async () => {
        isFavorited = await result.current.toggleFavorite('recipe-1');
      });

      expect(isFavorited!).toBe(true);
      expect(result.current.favoriteRecipeIds.has('recipe-1')).toBe(true);
      expect(RecipeService.favoriteRecipe).toHaveBeenCalledWith('recipe-1');
    });

    it('应该能够切换收藏状态（取消收藏）', async () => {
      (RecipeService.unfavoriteRecipe as jest.Mock).mockResolvedValue(undefined);

      const { result } = renderHook(() => useRecipeStore());

      // 先设置为已收藏状态
      act(() => {
        result.current.favoriteRecipeIds = new Set(['recipe-1']);
      });

      let isFavorited: boolean;
      await act(async () => {
        isFavorited = await result.current.toggleFavorite('recipe-1');
      });

      expect(isFavorited!).toBe(false);
      expect(result.current.favoriteRecipeIds.has('recipe-1')).toBe(false);
      expect(RecipeService.unfavoriteRecipe).toHaveBeenCalledWith('recipe-1');
    });

    it('应该能够检查收藏状态', async () => {
      (RecipeService.checkFavoriteStatus as jest.Mock).mockResolvedValue(true);

      const { result } = renderHook(() => useRecipeStore());

      let status: boolean;
      await act(async () => {
        status = await result.current.checkFavoriteStatus('recipe-1');
      });

      expect(status!).toBe(true);
      expect(result.current.favoriteRecipeIds.has('recipe-1')).toBe(true);
    });

    it('应该能够判断食谱是否已收藏', () => {
      const { result } = renderHook(() => useRecipeStore());

      act(() => {
        result.current.favoriteRecipeIds = new Set(['recipe-1', 'recipe-2']);
      });

      expect(result.current.isFavorite('recipe-1')).toBe(true);
      expect(result.current.isFavorite('recipe-3')).toBe(false);
    });

    it('收藏操作失败时应该回滚状态', async () => {
      const error = new Error('收藏失败');
      (RecipeService.favoriteRecipe as jest.Mock).mockRejectedValue(error);

      const { result } = renderHook(() => useRecipeStore());

      await act(async () => {
        try {
          await result.current.toggleFavorite('recipe-1');
        } catch (e) {
          // Expected error
        }
      });

      expect(result.current.favoriteRecipeIds.has('recipe-1')).toBe(false);
      expect(result.current.isFavoriting).toBe(false);
      expect(result.current.error).toBeTruthy();
    });
  });

  describe('食谱生成', () => {
    it('应该能够生成食谱', async () => {
      const mockRecipe = { id: 'recipe-1', title: '测试食谱' };
      (RecipeService.generateRecipe as jest.Mock).mockResolvedValue(mockRecipe);

      const { result } = renderHook(() => useRecipeStore());

      await act(async () => {
        await result.current.generateRecipe(['鸡肉', '西兰花']);
      });

      expect(result.current.currentRecipe).toEqual(mockRecipe);
      expect(result.current.lastGeneratedIngredients).toEqual(['鸡肉', '西兰花']);
      expect(result.current.isLoading).toBe(false);
    });

    it('生成食谱失败时应该设置错误信息', async () => {
      const error = new Error('生成失败');
      (RecipeService.generateRecipe as jest.Mock).mockRejectedValue(error);

      const { result } = renderHook(() => useRecipeStore());

      await act(async () => {
        await result.current.generateRecipe(['鸡肉']);
      });

      expect(result.current.currentRecipe).toBeNull();
      expect(result.current.error).toBeTruthy();
      expect(result.current.isLoading).toBe(false);
    });
  });
});