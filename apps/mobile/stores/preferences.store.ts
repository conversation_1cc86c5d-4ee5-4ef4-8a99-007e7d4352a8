import { create } from 'zustand';
import { 
  UserPreferencesDto, 
  DEFAULT_USER_PREFERENCES,
  DietType,
  CuisineType,
} from '@food-magic/shared-types';

interface PreferencesState {
  preferences: UserPreferencesDto;
  isLoading: boolean;
  error: string | null;
  hasChanges: boolean;
  
  // Actions
  setPreferences: (preferences: UserPreferencesDto) => void;
  updateDiet: (diet: DietType) => void;
  updateCuisines: (cuisines: CuisineType[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetChanges: () => void;
  markAsChanged: () => void;
}

export const usePreferencesStore = create<PreferencesState>((set) => ({
  preferences: DEFAULT_USER_PREFERENCES,
  isLoading: false,
  error: null,
  hasChanges: false,

  setPreferences: (preferences) =>
    set({
      preferences,
      hasChanges: false,
      error: null,
    }),

  updateDiet: (diet) =>
    set((state) => ({
      preferences: {
        ...state.preferences,
        diet,
      },
      hasChanges: true,
    })),

  updateCuisines: (cuisines) =>
    set((state) => ({
      preferences: {
        ...state.preferences,
        preferredCuisines: cuisines,
      },
      hasChanges: true,
    })),

  setLoading: (loading) =>
    set({ isLoading: loading }),

  setError: (error) =>
    set({ error }),

  resetChanges: () =>
    set({ hasChanges: false }),

  markAsChanged: () =>
    set({ hasChanges: true }),
}));