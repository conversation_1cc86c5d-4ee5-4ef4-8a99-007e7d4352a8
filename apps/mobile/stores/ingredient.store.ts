import { create } from 'zustand';

interface IngredientStore {
  ingredients: string[];
  isGenerateButtonEnabled: boolean;
  setIngredients: (ingredients: string[]) => void;
  addIngredient: (ingredient: string) => void;
  removeIngredient: (index: number) => void;
  clearIngredients: () => void;
}

export const useIngredientStore = create<IngredientStore>((set) => ({
  ingredients: [],
  isGenerateButtonEnabled: false,

  setIngredients: (ingredients) =>
    set({
      ingredients,
      isGenerateButtonEnabled: ingredients.length > 0,
    }),

  addIngredient: (ingredient) =>
    set((state) => {
      const trimmedIngredient = ingredient.trim();
      if (trimmedIngredient && !state.ingredients.includes(trimmedIngredient)) {
        const newIngredients = [...state.ingredients, trimmedIngredient];
        return {
          ingredients: newIngredients,
          isGenerateButtonEnabled: newIngredients.length > 0,
        };
      }
      return state;
    }),

  removeIngredient: (index) =>
    set((state) => {
      const newIngredients = state.ingredients.filter((_, i) => i !== index);
      return {
        ingredients: newIngredients,
        isGenerateButtonEnabled: newIngredients.length > 0,
      };
    }),

  clearIngredients: () =>
    set({
      ingredients: [],
      isGenerateButtonEnabled: false,
    }),
}));