package com.foodmagic.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.recipe.dto.RecipeGenerationRequestDto;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.entity.User;
import com.foodmagic.user.entity.UserPreference;
import com.foodmagic.user.repository.UserPreferenceRepository;
import com.foodmagic.user.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for recipe generation with user preferences.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@SpringBootTest
@AutoConfigureMockMvc
@Testcontainers
@Transactional
class RecipeGenerationIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16")
            .withDatabaseName("foodmagic_test")
            .withUsername("testuser")
            .withPassword("testpass");

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserPreferenceRepository userPreferenceRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private RecipeGenerationRequestDto recipeRequest;

    @BeforeEach
    void setUp() {
        // Clean up
        userPreferenceRepository.deleteAll();
        userRepository.deleteAll();

        // Create test user
        testUser = new User();
        testUser.setUsername("recipeuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPasswordHash("$2a$10$hashedpassword");
        testUser.setDisplayName("Recipe User");
        testUser.setIsActive(true);
        testUser = userRepository.save(testUser);

        // Create recipe request
        recipeRequest = new RecipeGenerationRequestDto();
        recipeRequest.setIngredients(Arrays.asList("chicken", "rice", "tomato", "onion"));
        recipeRequest.setRecipeType("MAIN_COURSE");
        recipeRequest.setMealTime("DINNER");
    }

    @Test
    @WithMockUser(username = "recipeuser")
    void testGenerateRecipeWithUserPreferences() throws Exception {
        // Arrange - Create user preferences
        UserPreference preference = new UserPreference();
        preference.setUserId(testUser.getId());
        preference.setPreferences(Map.of(
            "allergies", Arrays.asList("peanuts"),
            "isVegetarian", true,
            "diet", "LOW_CARB",
            "servingSize", 4
        ));
        userPreferenceRepository.save(preference);

        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert
        mockMvc.perform(post("/api/v1/recipes/generate")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.name").exists())
            .andExpect(jsonPath("$.servings").value(4))
            .andExpect(jsonPath("$.tags").isArray())
            .andExpect(jsonPath("$.tags[?(@ == 'vegetarian')]").exists())
            .andExpect(jsonPath("$.tags[?(@ == 'low_carb')]").exists());
    }

    @Test
    @WithMockUser(username = "recipeuser")
    void testGenerateRecipeWithoutUserPreferences() throws Exception {
        // Arrange - No preferences saved for user
        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert
        mockMvc.perform(post("/api/v1/recipes/generate")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.name").exists())
            .andExpect(jsonPath("$.servings").value(2)) // Default serving size
            .andExpect(jsonPath("$.ingredients").isArray());
    }

    @Test
    void testGenerateRecipeAsGuest() throws Exception {
        // Arrange - Guest request with preferences in body
        UserPreferencesDto guestPreferences = new UserPreferencesDto();
        guestPreferences.setAllergies(Arrays.asList("shellfish"));
        guestPreferences.setServingSize(6);
        recipeRequest.setPreferences(guestPreferences);

        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert
        mockMvc.perform(post("/api/v1/recipes/generate/guest")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").exists())
            .andExpect(jsonPath("$.servings").value(6))
            .andExpect(jsonPath("$.tags[?(@ == 'allergy-friendly')]").exists());
    }

    @Test
    @WithMockUser(username = "recipeuser")
    void testGenerateRecipeOverrideUserPreferences() throws Exception {
        // Arrange - User has preferences but request overrides them
        UserPreference preference = new UserPreference();
        preference.setUserId(testUser.getId());
        preference.setPreferences(Map.of(
            "servingSize", 2,
            "diet", "VEGAN"
        ));
        userPreferenceRepository.save(preference);

        // Override with different preferences in request
        UserPreferencesDto overridePreferences = new UserPreferencesDto();
        overridePreferences.setServingSize(8);
        overridePreferences.setDiet("KETO");
        recipeRequest.setPreferences(overridePreferences);

        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert
        mockMvc.perform(post("/api/v1/recipes/generate")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.servings").value(8)) // Override value
            .andExpect(jsonPath("$.tags[?(@ == 'keto')]").exists()); // Override diet
    }

    @Test
    void testGenerateRecipeWithInvalidIngredients() throws Exception {
        // Arrange - Empty ingredients list
        recipeRequest.setIngredients(Arrays.asList());
        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert
        mockMvc.perform(post("/api/v1/recipes/generate/guest")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(username = "recipeuser")
    void testSuggestRecipes() throws Exception {
        // Arrange
        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert
        mockMvc.perform(post("/api/v1/recipes/suggest?count=3")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$[0].id").exists())
            .andExpect(jsonPath("$[0].name").exists());
    }

    @Test
    @WithMockUser(username = "recipeuser")
    void testSuggestRecipesInvalidCount() throws Exception {
        // Arrange
        String requestBody = objectMapper.writeValueAsString(recipeRequest);

        // Act & Assert - Count too high
        mockMvc.perform(post("/api/v1/recipes/suggest?count=11")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isBadRequest());

        // Act & Assert - Negative count
        mockMvc.perform(post("/api/v1/recipes/suggest?count=-1")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isBadRequest());
    }
}