package com.foodmagic.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.entity.User;
import com.foodmagic.user.entity.UserPreference;
import com.foodmagic.user.repository.UserPreferenceRepository;
import com.foodmagic.user.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Arrays;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for user preferences functionality.
 * Uses TestContainers to run tests against a real PostgreSQL database.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@SpringBootTest
@AutoConfigureMockMvc
@Testcontainers
@Transactional
class UserPreferenceIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16")
            .withDatabaseName("foodmagic_test")
            .withUsername("testuser")
            .withPassword("testpass");

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserPreferenceRepository userPreferenceRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private UserPreferencesDto testPreferencesDto;

    @BeforeEach
    void setUp() {
        // Clean up
        userPreferenceRepository.deleteAll();
        userRepository.deleteAll();

        // Create test user
        testUser = new User();
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPasswordHash("$2a$10$hashedpassword");
        testUser.setDisplayName("Test User");
        testUser.setIsActive(true);
        testUser.setIsVerified(true);
        testUser = userRepository.save(testUser);

        // Create test preferences DTO
        testPreferencesDto = new UserPreferencesDto();
        testPreferencesDto.setAllergies(Arrays.asList("peanuts", "shellfish"));
        testPreferencesDto.setIsVegetarian(true);
        testPreferencesDto.setDiet("LOW_CARB");
        testPreferencesDto.setPreferredCuisines(Arrays.asList("ITALIAN", "MEXICAN"));
        testPreferencesDto.setUnitSystem("METRIC");
        testPreferencesDto.setLocale("zh-CN");
        testPreferencesDto.setMaxCookingTime(30);
        testPreferencesDto.setServingSize(4);
        testPreferencesDto.setSpiceLevel("MILD");
    }

    @Test
    @WithMockUser(username = "testuser")
    void testCreateUserPreferences() throws Exception {
        // Arrange
        String requestBody = objectMapper.writeValueAsString(testPreferencesDto);

        // Act & Assert - Create preferences
        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.allergies[0]").value("peanuts"))
            .andExpect(jsonPath("$.allergies[1]").value("shellfish"))
            .andExpect(jsonPath("$.isVegetarian").value(true))
            .andExpect(jsonPath("$.diet").value("LOW_CARB"));

        // Verify in database
        assertTrue(userPreferenceRepository.existsByUserId(testUser.getId()));
        UserPreference savedPreference = userPreferenceRepository.findByUserId(testUser.getId()).orElse(null);
        assertNotNull(savedPreference);
        assertNotNull(savedPreference.getPreferences());
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetUserPreferences_ExistingPreferences() throws Exception {
        // Arrange - Create preferences first
        String requestBody = objectMapper.writeValueAsString(testPreferencesDto);
        
        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk());

        // Act & Assert - Get preferences
        mockMvc.perform(get("/api/v1/users/me/preferences")
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.allergies[0]").value("peanuts"))
            .andExpect(jsonPath("$.isVegetarian").value(true))
            .andExpect(jsonPath("$.diet").value("LOW_CARB"))
            .andExpect(jsonPath("$.unitSystem").value("METRIC"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetUserPreferences_NoPreferences_ReturnsDefaults() throws Exception {
        // Act & Assert - Get preferences without creating them first
        mockMvc.perform(get("/api/v1/users/me/preferences")
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.allergies").isArray())
            .andExpect(jsonPath("$.allergies").isEmpty())
            .andExpect(jsonPath("$.isVegetarian").value(false))
            .andExpect(jsonPath("$.diet").value("NONE"))
            .andExpect(jsonPath("$.unitSystem").value("METRIC"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testUpdateUserPreferences() throws Exception {
        // Arrange - Create initial preferences
        String initialRequestBody = objectMapper.writeValueAsString(testPreferencesDto);
        
        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(initialRequestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk());

        // Update preferences
        testPreferencesDto.setIsVegetarian(false);
        testPreferencesDto.setDiet("KETO");
        testPreferencesDto.setSpiceLevel("HOT");
        String updatedRequestBody = objectMapper.writeValueAsString(testPreferencesDto);

        // Act & Assert - Update preferences
        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(updatedRequestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.isVegetarian").value(false))
            .andExpect(jsonPath("$.diet").value("KETO"))
            .andExpect(jsonPath("$.spiceLevel").value("HOT"));

        // Verify in database
        UserPreference updatedPreference = userPreferenceRepository.findByUserId(testUser.getId()).orElse(null);
        assertNotNull(updatedPreference);
        assertEquals("KETO", updatedPreference.getPreferences().get("diet"));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testDeleteUserPreferences() throws Exception {
        // Arrange - Create preferences first
        String requestBody = objectMapper.writeValueAsString(testPreferencesDto);
        
        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isOk());

        assertTrue(userPreferenceRepository.existsByUserId(testUser.getId()));

        // Act - Delete preferences
        mockMvc.perform(delete("/api/v1/users/me/preferences")
                .with(csrf())
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isNoContent());

        // Assert - Verify deletion
        assertFalse(userPreferenceRepository.existsByUserId(testUser.getId()));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testInvalidPreferences_TooManyAllergies() throws Exception {
        // Arrange - Create preferences with too many allergies
        testPreferencesDto.setAllergies(Arrays.asList(new String[51])); // 51 allergies
        String requestBody = objectMapper.writeValueAsString(testPreferencesDto);

        // Act & Assert
        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .principal(() -> testUser.getId().toString()))
            .andExpect(status().isBadRequest());

        // Verify nothing was saved
        assertFalse(userPreferenceRepository.existsByUserId(testUser.getId()));
    }

    @Test
    void testUnauthorizedAccess() throws Exception {
        // Act & Assert - Try to access without authentication
        mockMvc.perform(get("/api/v1/users/me/preferences"))
            .andExpect(status().isUnauthorized());

        mockMvc.perform(put("/api/v1/users/me/preferences")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
            .andExpect(status().isUnauthorized());

        mockMvc.perform(delete("/api/v1/users/me/preferences")
                .with(csrf()))
            .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "admin", roles = "ADMIN")
    void testAdminCanAccessUserPreferences() throws Exception {
        // Arrange - Create preferences for test user
        UserPreference preference = new UserPreference();
        preference.setUserId(testUser.getId());
        preference.setPreferences(java.util.Map.of(
            "allergies", Arrays.asList("peanuts"),
            "isVegetarian", true
        ));
        userPreferenceRepository.save(preference);

        // Act & Assert - Admin accesses user preferences
        mockMvc.perform(get("/api/v1/users/{userId}/preferences", testUser.getId()))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.allergies[0]").value("peanuts"))
            .andExpect(jsonPath("$.isVegetarian").value(true));
    }

    @Test
    @WithMockUser(username = "regularuser")
    void testNonAdminCannotAccessOtherUserPreferences() throws Exception {
        // Act & Assert - Non-admin tries to access other user's preferences
        mockMvc.perform(get("/api/v1/users/{userId}/preferences", testUser.getId()))
            .andExpect(status().isForbidden());
    }
}