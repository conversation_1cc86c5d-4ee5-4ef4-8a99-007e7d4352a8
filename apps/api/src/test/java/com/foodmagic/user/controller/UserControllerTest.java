package com.foodmagic.user.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.common.security.SecurityUtils;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.service.UserPreferenceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for UserController.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserPreferenceService userPreferenceService;

    @Autowired
    private ObjectMapper objectMapper;

    private UUID testUserId;
    private UserPreferencesDto testPreferencesDto;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        
        testPreferencesDto = new UserPreferencesDto();
        testPreferencesDto.setAllergies(Arrays.asList("peanuts"));
        testPreferencesDto.setIsVegetarian(true);
        testPreferencesDto.setDiet("LOW_CARB");
        testPreferencesDto.setUnitSystem("METRIC");
        testPreferencesDto.setLocale("zh-CN");
    }

    @Test
    @WithMockUser
    void getCurrentUserPreferences_Authenticated_ReturnsPreferences() throws Exception {
        // Arrange
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUserId).thenReturn(testUserId);
            when(userPreferenceService.getUserPreferences(testUserId))
                .thenReturn(testPreferencesDto);

            // Act & Assert
            mockMvc.perform(get("/api/v1/users/me/preferences"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.allergies[0]").value("peanuts"))
                .andExpect(jsonPath("$.isVegetarian").value(true))
                .andExpect(jsonPath("$.diet").value("LOW_CARB"))
                .andExpect(jsonPath("$.unitSystem").value("METRIC"))
                .andExpect(jsonPath("$.locale").value("zh-CN"));

            verify(userPreferenceService).getUserPreferences(testUserId);
        }
    }

    @Test
    void getCurrentUserPreferences_NotAuthenticated_ReturnsUnauthorized() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/v1/users/me/preferences"))
            .andExpect(status().isUnauthorized());

        verify(userPreferenceService, never()).getUserPreferences(any());
    }

    @Test
    @WithMockUser
    void updateCurrentUserPreferences_ValidData_ReturnsUpdated() throws Exception {
        // Arrange
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUserId).thenReturn(testUserId);
            when(userPreferenceService.updateUserPreferences(eq(testUserId), any(UserPreferencesDto.class)))
                .thenReturn(testPreferencesDto);

            String requestBody = objectMapper.writeValueAsString(testPreferencesDto);

            // Act & Assert
            mockMvc.perform(put("/api/v1/users/me/preferences")
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.isVegetarian").value(true))
                .andExpect(jsonPath("$.diet").value("LOW_CARB"));

            verify(userPreferenceService).updateUserPreferences(eq(testUserId), any(UserPreferencesDto.class));
        }
    }

    @Test
    @WithMockUser
    void updateCurrentUserPreferences_InvalidData_ReturnsBadRequest() throws Exception {
        // Arrange
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUserId).thenReturn(testUserId);
            when(userPreferenceService.updateUserPreferences(eq(testUserId), any(UserPreferencesDto.class)))
                .thenThrow(new IllegalArgumentException("Invalid preferences"));

            String requestBody = objectMapper.writeValueAsString(testPreferencesDto);

            // Act & Assert
            mockMvc.perform(put("/api/v1/users/me/preferences")
                    .with(csrf())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody))
                .andExpect(status().isBadRequest());

            verify(userPreferenceService).updateUserPreferences(eq(testUserId), any(UserPreferencesDto.class));
        }
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void getUserPreferences_AdminRole_ReturnsPreferences() throws Exception {
        // Arrange
        when(userPreferenceService.getUserPreferences(testUserId))
            .thenReturn(testPreferencesDto);

        // Act & Assert
        mockMvc.perform(get("/api/v1/users/{userId}/preferences", testUserId))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.isVegetarian").value(true));

        verify(userPreferenceService).getUserPreferences(testUserId);
    }

    @Test
    @WithMockUser
    void getUserPreferences_NonAdminRole_ReturnsForbidden() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/v1/users/{userId}/preferences", testUserId))
            .andExpect(status().isForbidden());

        verify(userPreferenceService, never()).getUserPreferences(any());
    }

    @Test
    @WithMockUser
    void deleteCurrentUserPreferences_Authenticated_ReturnsNoContent() throws Exception {
        // Arrange
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUserId).thenReturn(testUserId);
            when(userPreferenceService.deleteUserPreferences(testUserId))
                .thenReturn(true);

            // Act & Assert
            mockMvc.perform(delete("/api/v1/users/me/preferences")
                    .with(csrf()))
                .andExpect(status().isNoContent());

            verify(userPreferenceService).deleteUserPreferences(testUserId);
        }
    }

    @Test
    @WithMockUser
    void deleteCurrentUserPreferences_NoUserId_ReturnsUnauthorized() throws Exception {
        // Arrange
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUserId).thenReturn(null);

            // Act & Assert
            mockMvc.perform(delete("/api/v1/users/me/preferences")
                    .with(csrf()))
                .andExpect(status().isUnauthorized());

            verify(userPreferenceService, never()).deleteUserPreferences(any());
        }
    }
}