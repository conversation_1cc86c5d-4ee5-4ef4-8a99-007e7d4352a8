package com.foodmagic.user.service;

import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.entity.UserPreference;
import com.foodmagic.user.mapper.UserPreferenceMapper;
import com.foodmagic.user.repository.UserPreferenceRepository;
import com.foodmagic.user.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for UserPreferenceService.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@ExtendWith(MockitoExtension.class)
class UserPreferenceServiceTest {

    @Mock
    private UserPreferenceRepository userPreferenceRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserPreferenceMapper mapper;

    @InjectMocks
    private UserPreferenceService userPreferenceService;

    private UUID testUserId;
    private UserPreferencesDto testPreferencesDto;
    private UserPreference testUserPreference;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        
        // Setup test DTO
        testPreferencesDto = new UserPreferencesDto();
        testPreferencesDto.setAllergies(Arrays.asList("peanuts", "shellfish"));
        testPreferencesDto.setIsVegetarian(true);
        testPreferencesDto.setDiet("LOW_CARB");
        testPreferencesDto.setPreferredCuisines(Arrays.asList("ITALIAN", "MEXICAN"));
        testPreferencesDto.setUnitSystem("METRIC");
        testPreferencesDto.setLocale("zh-CN");
        testPreferencesDto.setMaxCookingTime(30);
        testPreferencesDto.setServingSize(4);
        testPreferencesDto.setSpiceLevel("MILD");
        
        // Setup test entity
        testUserPreference = new UserPreference();
        testUserPreference.setUserId(testUserId);
        Map<String, Object> prefsMap = new HashMap<>();
        prefsMap.put("allergies", Arrays.asList("peanuts", "shellfish"));
        prefsMap.put("isVegetarian", true);
        testUserPreference.setPreferences(prefsMap);
    }

    @Test
    void getUserPreferences_ExistingUser_ReturnsPreferences() {
        // Arrange
        when(userPreferenceRepository.findByUserId(testUserId))
            .thenReturn(Optional.of(testUserPreference));
        when(mapper.toDto(testUserPreference))
            .thenReturn(testPreferencesDto);

        // Act
        UserPreferencesDto result = userPreferenceService.getUserPreferences(testUserId);

        // Assert
        assertNotNull(result);
        assertEquals(testPreferencesDto, result);
        verify(userPreferenceRepository).findByUserId(testUserId);
        verify(mapper).toDto(testUserPreference);
    }

    @Test
    void getUserPreferences_NonExistingUser_ReturnsDefaults() {
        // Arrange
        UserPreferencesDto defaultPrefs = new UserPreferencesDto();
        when(userPreferenceRepository.findByUserId(testUserId))
            .thenReturn(Optional.empty());
        when(mapper.getDefaultPreferences())
            .thenReturn(defaultPrefs);

        // Act
        UserPreferencesDto result = userPreferenceService.getUserPreferences(testUserId);

        // Assert
        assertNotNull(result);
        assertEquals(defaultPrefs, result);
        verify(userPreferenceRepository).findByUserId(testUserId);
        verify(mapper).getDefaultPreferences();
    }

    @Test
    void updateUserPreferences_ExistingPreferences_UpdatesSuccessfully() {
        // Arrange
        when(userRepository.existsById(testUserId)).thenReturn(true);
        when(userPreferenceRepository.findByUserId(testUserId))
            .thenReturn(Optional.of(testUserPreference));
        when(userPreferenceRepository.save(any(UserPreference.class)))
            .thenReturn(testUserPreference);
        when(mapper.toPreferencesMap(testPreferencesDto))
            .thenReturn(testUserPreference.getPreferences());
        when(mapper.toDto(testUserPreference))
            .thenReturn(testPreferencesDto);

        // Act
        UserPreferencesDto result = userPreferenceService.updateUserPreferences(testUserId, testPreferencesDto);

        // Assert
        assertNotNull(result);
        assertEquals(testPreferencesDto, result);
        verify(userRepository).existsById(testUserId);
        verify(userPreferenceRepository).findByUserId(testUserId);
        verify(userPreferenceRepository).save(any(UserPreference.class));
    }

    @Test
    void updateUserPreferences_NewPreferences_CreatesSuccessfully() {
        // Arrange
        when(userRepository.existsById(testUserId)).thenReturn(true);
        when(userPreferenceRepository.findByUserId(testUserId))
            .thenReturn(Optional.empty());
        when(userPreferenceRepository.save(any(UserPreference.class)))
            .thenReturn(testUserPreference);
        when(mapper.toPreferencesMap(testPreferencesDto))
            .thenReturn(testUserPreference.getPreferences());
        when(mapper.toDto(any(UserPreference.class)))
            .thenReturn(testPreferencesDto);

        // Act
        UserPreferencesDto result = userPreferenceService.updateUserPreferences(testUserId, testPreferencesDto);

        // Assert
        assertNotNull(result);
        assertEquals(testPreferencesDto, result);
        verify(userRepository).existsById(testUserId);
        verify(userPreferenceRepository).findByUserId(testUserId);
        verify(userPreferenceRepository).save(any(UserPreference.class));
    }

    @Test
    void updateUserPreferences_UserNotFound_ThrowsException() {
        // Arrange
        when(userRepository.existsById(testUserId)).thenReturn(false);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            userPreferenceService.updateUserPreferences(testUserId, testPreferencesDto)
        );
        verify(userRepository).existsById(testUserId);
        verify(userPreferenceRepository, never()).save(any());
    }

    @Test
    void updateUserPreferences_InvalidAllergiesCount_ThrowsException() {
        // Arrange
        List<String> tooManyAllergies = new ArrayList<>();
        for (int i = 0; i < 51; i++) {
            tooManyAllergies.add("allergy" + i);
        }
        testPreferencesDto.setAllergies(tooManyAllergies);
        when(userRepository.existsById(testUserId)).thenReturn(true);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            userPreferenceService.updateUserPreferences(testUserId, testPreferencesDto)
        );
        verify(userPreferenceRepository, never()).save(any());
    }

    @Test
    void deleteUserPreferences_ExistingPreferences_DeletesSuccessfully() {
        // Arrange
        when(userPreferenceRepository.existsByUserId(testUserId)).thenReturn(true);

        // Act
        boolean result = userPreferenceService.deleteUserPreferences(testUserId);

        // Assert
        assertTrue(result);
        verify(userPreferenceRepository).existsByUserId(testUserId);
        verify(userPreferenceRepository).deleteByUserId(testUserId);
    }

    @Test
    void deleteUserPreferences_NonExistingPreferences_ReturnsFalse() {
        // Arrange
        when(userPreferenceRepository.existsByUserId(testUserId)).thenReturn(false);

        // Act
        boolean result = userPreferenceService.deleteUserPreferences(testUserId);

        // Assert
        assertFalse(result);
        verify(userPreferenceRepository).existsByUserId(testUserId);
        verify(userPreferenceRepository, never()).deleteByUserId(any());
    }

    @Test
    void hasPreferences_ExistingPreferences_ReturnsTrue() {
        // Arrange
        when(userPreferenceRepository.existsByUserId(testUserId)).thenReturn(true);

        // Act
        boolean result = userPreferenceService.hasPreferences(testUserId);

        // Assert
        assertTrue(result);
        verify(userPreferenceRepository).existsByUserId(testUserId);
    }

    @Test
    void hasPreferences_NonExistingPreferences_ReturnsFalse() {
        // Arrange
        when(userPreferenceRepository.existsByUserId(testUserId)).thenReturn(false);

        // Act
        boolean result = userPreferenceService.hasPreferences(testUserId);

        // Assert
        assertFalse(result);
        verify(userPreferenceRepository).existsByUserId(testUserId);
    }
}