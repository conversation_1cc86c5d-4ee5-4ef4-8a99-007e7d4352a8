package com.foodmagic.ai.client;

import com.foodmagic.user.dto.UserPreferencesDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PromptBuilder.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
class PromptBuilderTest {

    private PromptBuilder promptBuilder;
    private List<String> testIngredients;
    private UserPreferencesDto testPreferences;

    @BeforeEach
    void setUp() {
        promptBuilder = new PromptBuilder();
        testIngredients = Arrays.asList("chicken", "rice", "tomato", "onion");
        
        testPreferences = new UserPreferencesDto();
        testPreferences.setAllergies(Arrays.asList("peanuts", "shellfish"));
        testPreferences.setIsVegetarian(false);
        testPreferences.setDiet("LOW_CARB");
        testPreferences.setPreferredCuisines(Arrays.asList("ITALIAN", "MEXICAN"));
        testPreferences.setUnitSystem("METRIC");
        testPreferences.setLocale("zh-CN");
        testPreferences.setMaxCookingTime(30);
        testPreferences.setServingSize(4);
        testPreferences.setSpiceLevel("MILD");
    }

    @Test
    void buildRecipePrompt_WithPreferences_IncludesAllConstraints() {
        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);

        // Assert
        assertNotNull(prompt);
        assertTrue(prompt.contains("chicken, rice, tomato, onion"));
        assertTrue(prompt.contains("peanuts"));
        assertTrue(prompt.contains("shellfish"));
        assertTrue(prompt.contains("CRITICAL SAFETY REQUIREMENT"));
        assertTrue(prompt.contains("low in carbohydrates"));
        assertTrue(prompt.contains("ITALIAN"));
        assertTrue(prompt.contains("MEXICAN"));
        assertTrue(prompt.contains("metric"));
        assertTrue(prompt.contains("30 minutes"));
        assertTrue(prompt.contains("4 people"));
        assertTrue(prompt.contains("mild spice"));
        assertTrue(prompt.contains("Chinese cooking techniques"));
    }

    @Test
    void buildRecipePrompt_WithoutPreferences_UsesDefaults() {
        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, null);

        // Assert
        assertNotNull(prompt);
        assertTrue(prompt.contains("chicken, rice, tomato, onion"));
        assertTrue(prompt.contains("None specified"));
        assertTrue(prompt.contains("generally appealing"));
    }

    @Test
    void buildRecipePrompt_EmptyIngredients_HandlesGracefully() {
        // Act
        String prompt = promptBuilder.buildRecipePrompt(Arrays.asList(), testPreferences);

        // Assert
        assertNotNull(prompt);
        assertTrue(prompt.contains("No specific ingredients"));
    }

    @Test
    void buildRecipePrompt_VegetarianPreference_IncludesConstraint() {
        // Arrange
        testPreferences.setIsVegetarian(true);

        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);

        // Assert
        assertTrue(prompt.contains("must be vegetarian"));
        assertTrue(prompt.contains("no meat, poultry, or fish"));
    }

    @Test
    void buildRecipePrompt_VeganDiet_IncludesStrictConstraint() {
        // Arrange
        testPreferences.setDiet("VEGAN");

        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);

        // Assert
        assertTrue(prompt.contains("strictly vegan"));
        assertTrue(prompt.contains("no animal products"));
    }

    @Test
    void buildRecipePrompt_KetoDiet_IncludesKetoConstraint() {
        // Arrange
        testPreferences.setDiet("KETO");

        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);

        // Assert
        assertTrue(prompt.contains("ketogenic"));
        assertTrue(prompt.contains("very low carb, high fat"));
    }

    @Test
    void buildRecipePrompt_ImperialUnits_SpecifiesImperial() {
        // Arrange
        testPreferences.setUnitSystem("IMPERIAL");

        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);

        // Assert
        assertTrue(prompt.contains("imperial"));
        assertTrue(prompt.contains("cups, tablespoons, ounces, Fahrenheit"));
    }

    @Test
    void buildRecipePrompt_DifferentLocales_AddsLocaleInstructions() {
        // Test Japanese locale
        testPreferences.setLocale("ja-JP");
        String promptJP = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);
        assertTrue(promptJP.contains("Japanese cooking techniques"));

        // Test Italian locale
        testPreferences.setLocale("it-IT");
        String promptIT = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);
        assertTrue(promptIT.contains("Italian cooking techniques"));

        // Test Spanish locale
        testPreferences.setLocale("es-ES");
        String promptES = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);
        assertTrue(promptES.contains("Spanish or Latin American"));
    }

    @Test
    void buildRecipePrompt_SpiceLevels_CorrectlyDescribed() {
        // Test NONE
        testPreferences.setSpiceLevel("NONE");
        String promptNone = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);
        assertTrue(promptNone.contains("No spicy ingredients"));

        // Test HOT
        testPreferences.setSpiceLevel("HOT");
        String promptHot = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);
        assertTrue(promptHot.contains("Hot and spicy"));

        // Test EXTRA_HOT
        testPreferences.setSpiceLevel("EXTRA_HOT");
        String promptExtraHot = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);
        assertTrue(promptExtraHot.contains("Extra hot and very spicy"));
    }

    @Test
    void buildQuickSuggestionPrompt_WithPreferences_FormatsCorrectly() {
        // Act
        String prompt = promptBuilder.buildQuickSuggestionPrompt(testIngredients, testPreferences, 3);

        // Assert
        assertNotNull(prompt);
        assertTrue(prompt.contains("chicken, rice, tomato, onion"));
        assertTrue(prompt.contains("3 quick recipe ideas"));
        assertTrue(prompt.contains("brief descriptions"));
        assertTrue(prompt.contains("dietary constraints"));
    }

    @Test
    void buildRecipePrompt_AvoidIngredients_IncludesInPrompt() {
        // Arrange
        testPreferences.setAvoidIngredients(Arrays.asList("cilantro", "blue cheese"));

        // Act
        String prompt = promptBuilder.buildRecipePrompt(testIngredients, testPreferences);

        // Assert
        assertTrue(prompt.contains("avoid using these ingredients"));
        assertTrue(prompt.contains("cilantro"));
        assertTrue(prompt.contains("blue cheese"));
    }
}