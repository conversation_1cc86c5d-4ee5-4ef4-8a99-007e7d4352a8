package com.foodmagic.ingredient.service;

import com.foodmagic.ingredient.dto.IngredientSuggestionDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class IngredientServiceTest {

    private IngredientService ingredientService;

    @BeforeEach
    void setUp() {
        ingredientService = new IngredientService();
    }

    @Test
    void testSearchIngredients_FindsTomato() {
        // When
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("番茄");

        // Then
        assertNotNull(results);
        assertFalse(results.isEmpty());
        assertTrue(results.stream().anyMatch(dto -> dto.getName().equals("番茄")));
        assertTrue(results.stream().anyMatch(dto -> dto.getName().equals("番茄酱")));
    }

    @Test
    void testSearchIngredients_CaseInsensitive() {
        // When - searching with different cases
        List<IngredientSuggestionDto> results1 = ingredientService.searchIngredients("番茄");
        List<IngredientSuggestionDto> results2 = ingredientService.searchIngredients("番茄");

        // Then
        assertEquals(results1.size(), results2.size());
    }

    @Test
    void testSearchIngredients_PartialMatch() {
        // When
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("番");

        // Then
        assertNotNull(results);
        assertFalse(results.isEmpty());
        assertTrue(results.stream().anyMatch(dto -> dto.getName().contains("番")));
    }

    @Test
    void testSearchIngredients_LimitTo10Results() {
        // When - searching for a common character that matches many items
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("肉");

        // Then
        assertNotNull(results);
        assertTrue(results.size() <= 10);
    }

    @Test
    void testSearchIngredients_EmptyQuery() {
        // When
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("");

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testSearchIngredients_NoMatches() {
        // When
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("xyz123");

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testSearchIngredients_ReturnsCorrectCategories() {
        // When
        List<IngredientSuggestionDto> vegetableResults = ingredientService.searchIngredients("白菜");
        List<IngredientSuggestionDto> meatResults = ingredientService.searchIngredients("鸡肉");
        List<IngredientSuggestionDto> spiceResults = ingredientService.searchIngredients("生抽");

        // Then
        assertFalse(vegetableResults.isEmpty());
        assertEquals("蔬菜", vegetableResults.get(0).getCategory());
        
        assertFalse(meatResults.isEmpty());
        assertEquals("肉类", meatResults.get(0).getCategory());
        
        assertFalse(spiceResults.isEmpty());
        assertEquals("调料", spiceResults.get(0).getCategory());
    }

    @Test
    void testSearchIngredients_AllResultsHaveRequiredFields() {
        // When
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("土豆");

        // Then
        assertFalse(results.isEmpty());
        results.forEach(dto -> {
            assertNotNull(dto.getId());
            assertNotNull(dto.getName());
            assertNotNull(dto.getCategory());
            assertFalse(dto.getId().isEmpty());
            assertFalse(dto.getName().isEmpty());
            assertFalse(dto.getCategory().isEmpty());
        });
    }

    @Test
    void testRecordCustomIngredient_DoesNotThrow() {
        // This test verifies that the method doesn't throw exceptions
        // In production, this would log or store the custom ingredient
        assertDoesNotThrow(() -> {
            ingredientService.recordCustomIngredient("自定义食材");
        });
    }

    @Test
    void testSearchIngredients_MultipleCategories() {
        // When - searching for a term that might match different categories
        List<IngredientSuggestionDto> results = ingredientService.searchIngredients("蛋");

        // Then
        assertNotNull(results);
        assertFalse(results.isEmpty());
        // Verify we get eggs which should be in the database
        assertTrue(results.stream().anyMatch(dto -> dto.getName().equals("鸡蛋")));
    }
}