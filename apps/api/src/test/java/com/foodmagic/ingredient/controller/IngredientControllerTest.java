package com.foodmagic.ingredient.controller;

import com.foodmagic.ingredient.dto.IngredientSuggestionDto;
import com.foodmagic.ingredient.service.IngredientService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IngredientControllerTest {

    @Mock
    private IngredientService ingredientService;

    @InjectMocks
    private IngredientController ingredientController;

    private List<IngredientSuggestionDto> mockSuggestions;

    @BeforeEach
    void setUp() {
        mockSuggestions = Arrays.asList(
                new IngredientSuggestionDto("1", "番茄", "蔬菜"),
                new IngredientSuggestionDto("2", "番茄酱", "调料"),
                new IngredientSuggestionDto("3", "土豆", "蔬菜")
        );
    }

    @Test
    void testSearchIngredients_WithValidQuery() {
        // Given
        String query = "番";
        when(ingredientService.searchIngredients(query)).thenReturn(mockSuggestions);

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(3, response.getBody().size());
        assertEquals("番茄", response.getBody().get(0).getName());
        verify(ingredientService, times(1)).searchIngredients(query);
    }

    @Test
    void testSearchIngredients_WithEmptyQuery() {
        // Given
        String query = "";

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
        verify(ingredientService, never()).searchIngredients(anyString());
    }

    @Test
    void testSearchIngredients_WithNullQuery() {
        // Given
        String query = null;

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
        verify(ingredientService, never()).searchIngredients(anyString());
    }

    @Test
    void testSearchIngredients_WithWhitespaceQuery() {
        // Given
        String query = "   ";

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
        verify(ingredientService, never()).searchIngredients(anyString());
    }

    @Test
    void testSearchIngredients_TrimsQuery() {
        // Given
        String query = "  番茄  ";
        String trimmedQuery = "番茄";
        when(ingredientService.searchIngredients(trimmedQuery))
                .thenReturn(Collections.singletonList(
                        new IngredientSuggestionDto("1", "番茄", "蔬菜")
                ));

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        verify(ingredientService, times(1)).searchIngredients(trimmedQuery);
    }

    @Test
    void testSearchIngredients_NoResults() {
        // Given
        String query = "xyz";
        when(ingredientService.searchIngredients(query))
                .thenReturn(Collections.emptyList());

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
        verify(ingredientService, times(1)).searchIngredients(query);
    }

    @Test
    void testSearchIngredients_WithCategories() {
        // Given
        String query = "调料";
        List<IngredientSuggestionDto> spices = Arrays.asList(
                new IngredientSuggestionDto("1", "生抽", "调料"),
                new IngredientSuggestionDto("2", "老抽", "调料"),
                new IngredientSuggestionDto("3", "番茄酱", "调料")
        );
        when(ingredientService.searchIngredients(query)).thenReturn(spices);

        // When
        ResponseEntity<List<IngredientSuggestionDto>> response = 
                ingredientController.searchIngredients(query);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(3, response.getBody().size());
        response.getBody().forEach(dto -> assertEquals("调料", dto.getCategory()));
        verify(ingredientService, times(1)).searchIngredients(query);
    }
}