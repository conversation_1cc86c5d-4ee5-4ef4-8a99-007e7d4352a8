package com.foodmagic.ingredient.service;

import com.foodmagic.ingredient.dto.IngredientSuggestionDto;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IngredientService {

    private static final Map<String, IngredientSuggestionDto> INGREDIENT_DATABASE = new HashMap<>();
    
    static {
        // 蔬菜类
        INGREDIENT_DATABASE.put("番茄", new IngredientSuggestionDto("veg_001", "番茄", "蔬菜"));
        INGREDIENT_DATABASE.put("土豆", new IngredientSuggestionDto("veg_002", "土豆", "蔬菜"));
        INGREDIENT_DATABASE.put("洋葱", new IngredientSuggestionDto("veg_003", "洋葱", "蔬菜"));
        INGREDIENT_DATABASE.put("黄瓜", new IngredientSuggestionDto("veg_004", "黄瓜", "蔬菜"));
        INGREDIENT_DATABASE.put("胡萝卜", new IngredientSuggestionDto("veg_005", "胡萝卜", "蔬菜"));
        INGREDIENT_DATABASE.put("白菜", new IngredientSuggestionDto("veg_006", "白菜", "蔬菜"));
        INGREDIENT_DATABASE.put("青椒", new IngredientSuggestionDto("veg_007", "青椒", "蔬菜"));
        INGREDIENT_DATABASE.put("茄子", new IngredientSuggestionDto("veg_008", "茄子", "蔬菜"));
        INGREDIENT_DATABASE.put("西兰花", new IngredientSuggestionDto("veg_009", "西兰花", "蔬菜"));
        INGREDIENT_DATABASE.put("菠菜", new IngredientSuggestionDto("veg_010", "菠菜", "蔬菜"));
        
        // 肉类
        INGREDIENT_DATABASE.put("鸡肉", new IngredientSuggestionDto("meat_001", "鸡肉", "肉类"));
        INGREDIENT_DATABASE.put("猪肉", new IngredientSuggestionDto("meat_002", "猪肉", "肉类"));
        INGREDIENT_DATABASE.put("牛肉", new IngredientSuggestionDto("meat_003", "牛肉", "肉类"));
        INGREDIENT_DATABASE.put("羊肉", new IngredientSuggestionDto("meat_004", "羊肉", "肉类"));
        INGREDIENT_DATABASE.put("鱼肉", new IngredientSuggestionDto("meat_005", "鱼肉", "肉类"));
        INGREDIENT_DATABASE.put("虾", new IngredientSuggestionDto("meat_006", "虾", "肉类"));
        INGREDIENT_DATABASE.put("鸡蛋", new IngredientSuggestionDto("meat_007", "鸡蛋", "蛋类"));
        INGREDIENT_DATABASE.put("培根", new IngredientSuggestionDto("meat_008", "培根", "肉类"));
        INGREDIENT_DATABASE.put("香肠", new IngredientSuggestionDto("meat_009", "香肠", "肉类"));
        
        // 调料类
        INGREDIENT_DATABASE.put("盐", new IngredientSuggestionDto("spice_001", "盐", "调料"));
        INGREDIENT_DATABASE.put("糖", new IngredientSuggestionDto("spice_002", "糖", "调料"));
        INGREDIENT_DATABASE.put("生抽", new IngredientSuggestionDto("spice_003", "生抽", "调料"));
        INGREDIENT_DATABASE.put("老抽", new IngredientSuggestionDto("spice_004", "老抽", "调料"));
        INGREDIENT_DATABASE.put("醋", new IngredientSuggestionDto("spice_005", "醋", "调料"));
        INGREDIENT_DATABASE.put("料酒", new IngredientSuggestionDto("spice_006", "料酒", "调料"));
        INGREDIENT_DATABASE.put("生姜", new IngredientSuggestionDto("spice_007", "生姜", "调料"));
        INGREDIENT_DATABASE.put("大蒜", new IngredientSuggestionDto("spice_008", "大蒜", "调料"));
        INGREDIENT_DATABASE.put("花椒", new IngredientSuggestionDto("spice_009", "花椒", "调料"));
        INGREDIENT_DATABASE.put("辣椒", new IngredientSuggestionDto("spice_010", "辣椒", "调料"));
        INGREDIENT_DATABASE.put("番茄酱", new IngredientSuggestionDto("spice_011", "番茄酱", "调料"));
        
        // 主食类
        INGREDIENT_DATABASE.put("米饭", new IngredientSuggestionDto("staple_001", "米饭", "主食"));
        INGREDIENT_DATABASE.put("面条", new IngredientSuggestionDto("staple_002", "面条", "主食"));
        INGREDIENT_DATABASE.put("面粉", new IngredientSuggestionDto("staple_003", "面粉", "主食"));
        INGREDIENT_DATABASE.put("面包", new IngredientSuggestionDto("staple_004", "面包", "主食"));
        INGREDIENT_DATABASE.put("意大利面", new IngredientSuggestionDto("staple_005", "意大利面", "主食"));
        
        // 其他
        INGREDIENT_DATABASE.put("牛奶", new IngredientSuggestionDto("other_001", "牛奶", "乳制品"));
        INGREDIENT_DATABASE.put("黄油", new IngredientSuggestionDto("other_002", "黄油", "乳制品"));
        INGREDIENT_DATABASE.put("奶酪", new IngredientSuggestionDto("other_003", "奶酪", "乳制品"));
        INGREDIENT_DATABASE.put("豆腐", new IngredientSuggestionDto("other_004", "豆腐", "豆制品"));
        INGREDIENT_DATABASE.put("蘑菇", new IngredientSuggestionDto("other_005", "蘑菇", "菌类"));
    }

    public List<IngredientSuggestionDto> searchIngredients(String query) {
        String lowerQuery = query.toLowerCase();
        
        return INGREDIENT_DATABASE.entrySet().stream()
                .filter(entry -> entry.getKey().toLowerCase().contains(lowerQuery))
                .map(Map.Entry::getValue)
                .limit(10)
                .collect(Collectors.toList());
    }
    
    public void recordCustomIngredient(String ingredientName) {
        // 记录用户自定义食材，用于未来分析
        // 在MVP阶段，仅记录日志，后续可以存储到数据库
        System.out.println("Custom ingredient recorded: " + ingredientName);
    }
}