package com.foodmagic.ingredient.controller;

import com.foodmagic.ingredient.dto.IngredientSuggestionDto;
import com.foodmagic.ingredient.service.IngredientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/ingredients")
public class IngredientController {

    private final IngredientService ingredientService;

    @Autowired
    public IngredientController(IngredientService ingredientService) {
        this.ingredientService = ingredientService;
    }

    @GetMapping("/search")
    public ResponseEntity<List<IngredientSuggestionDto>> searchIngredients(
            @RequestParam(value = "q", required = false, defaultValue = "") String query) {
        
        if (query == null || query.trim().isEmpty()) {
            return ResponseEntity.ok(List.of());
        }
        
        List<IngredientSuggestionDto> suggestions = ingredientService.searchIngredients(query.trim());
        return ResponseEntity.ok(suggestions);
    }
    
    @PostMapping("/custom")
    public ResponseEntity<Void> recordCustomIngredient(@RequestBody CustomIngredientRequest request) {
        if (request == null || request.getName() == null || request.getName().trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        ingredientService.recordCustomIngredient(request.getName().trim());
        return ResponseEntity.ok().build();
    }
    
    // 内部类定义请求体
    public static class CustomIngredientRequest {
        private String name;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
}