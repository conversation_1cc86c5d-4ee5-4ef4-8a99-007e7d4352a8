package com.foodmagic.user.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.entity.UserPreference;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Mapper for converting between UserPreference entity and UserPreferencesDto.
 * Handles JSON to DTO conversion and vice versa.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Component
public class UserPreferenceMapper {

    private final ObjectMapper objectMapper;

    public UserPreferenceMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Convert UserPreference entity to UserPreferencesDto.
     * 
     * @param entity the UserPreference entity
     * @return the UserPreferencesDto
     */
    public UserPreferencesDto toDto(UserPreference entity) {
        if (entity == null || entity.getPreferences() == null) {
            return getDefaultPreferences();
        }

        UserPreferencesDto dto = new UserPreferencesDto();
        Map<String, Object> prefs = entity.getPreferences();

        dto.setAllergies(getListValue(prefs, "allergies"));
        dto.setIsVegetarian(getBooleanValue(prefs, "isVegetarian"));
        dto.setDiet(getStringValue(prefs, "diet"));
        dto.setPreferredCuisines(getListValue(prefs, "preferredCuisines"));
        dto.setUnitSystem(getStringValue(prefs, "unitSystem"));
        dto.setLocale(getStringValue(prefs, "locale"));
        dto.setMaxCookingTime(getIntegerValue(prefs, "maxCookingTime"));
        dto.setServingSize(getIntegerValue(prefs, "servingSize"));
        dto.setAvoidIngredients(getListValue(prefs, "avoidIngredients"));
        dto.setSpiceLevel(getStringValue(prefs, "spiceLevel"));

        return dto;
    }

    /**
     * Convert UserPreferencesDto to Map for storage in JSON column.
     * 
     * @param dto the UserPreferencesDto
     * @return the preferences map
     */
    public Map<String, Object> toPreferencesMap(UserPreferencesDto dto) {
        if (dto == null) {
            return new HashMap<>();
        }

        Map<String, Object> prefs = new HashMap<>();
        
        if (dto.getAllergies() != null && !dto.getAllergies().isEmpty()) {
            prefs.put("allergies", dto.getAllergies());
        }
        if (dto.getIsVegetarian() != null) {
            prefs.put("isVegetarian", dto.getIsVegetarian());
        }
        if (dto.getDiet() != null) {
            prefs.put("diet", dto.getDiet());
        }
        if (dto.getPreferredCuisines() != null && !dto.getPreferredCuisines().isEmpty()) {
            prefs.put("preferredCuisines", dto.getPreferredCuisines());
        }
        if (dto.getUnitSystem() != null) {
            prefs.put("unitSystem", dto.getUnitSystem());
        }
        if (dto.getLocale() != null) {
            prefs.put("locale", dto.getLocale());
        }
        if (dto.getMaxCookingTime() != null) {
            prefs.put("maxCookingTime", dto.getMaxCookingTime());
        }
        if (dto.getServingSize() != null) {
            prefs.put("servingSize", dto.getServingSize());
        }
        if (dto.getAvoidIngredients() != null && !dto.getAvoidIngredients().isEmpty()) {
            prefs.put("avoidIngredients", dto.getAvoidIngredients());
        }
        if (dto.getSpiceLevel() != null) {
            prefs.put("spiceLevel", dto.getSpiceLevel());
        }

        return prefs;
    }

    /**
     * Get default preferences for new users.
     * 
     * @return default UserPreferencesDto
     */
    public UserPreferencesDto getDefaultPreferences() {
        UserPreferencesDto defaults = new UserPreferencesDto();
        defaults.setAllergies(new ArrayList<>());
        defaults.setIsVegetarian(false);
        defaults.setDiet("NONE");
        defaults.setPreferredCuisines(new ArrayList<>());
        defaults.setUnitSystem("METRIC");
        defaults.setLocale("zh-CN");
        defaults.setMaxCookingTime(60);
        defaults.setServingSize(2);
        defaults.setAvoidIngredients(new ArrayList<>());
        defaults.setSpiceLevel("MEDIUM");
        return defaults;
    }

    @SuppressWarnings("unchecked")
    private List<String> getListValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return new ArrayList<>();
    }

    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}