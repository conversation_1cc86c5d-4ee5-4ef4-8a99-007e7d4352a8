package com.foodmagic.user.constants;

/**
 * Enum for supported spice levels.
 * Centralizes spice level definitions for consistent validation.
 * 
 * <AUTHOR> (QA)
 * @since 1.0
 */
public enum SpiceLevel {
    NONE("No spice"),
    MILD("Mild spice level"),
    MEDIUM("Medium spice level"),
    HOT("Hot and spicy"),
    EXTRA_HOT("Extra hot and very spicy");
    
    private final String description;
    
    SpiceLevel(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if a string value is a valid spice level.
     * 
     * @param value the string to check
     * @return true if valid, false otherwise
     */
    public static boolean isValid(String value) {
        if (value == null) {
            return false;
        }
        try {
            SpiceLevel.valueOf(value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}