package com.foodmagic.user.controller;

import com.foodmagic.common.security.SecurityUtils;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.service.UserPreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.UUID;

/**
 * REST controller for user-related operations.
 * Handles user preferences and profile management.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@RestController
@RequestMapping("/api/v1/users")
@Tag(name = "User Management", description = "User profile and preferences management endpoints")
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    private final UserPreferenceService userPreferenceService;

    public UserController(UserPreferenceService userPreferenceService) {
        this.userPreferenceService = userPreferenceService;
    }

    /**
     * Get current user's preferences.
     * 
     * @return UserPreferencesDto containing user preferences or defaults
     */
    @GetMapping("/me/preferences")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Get current user preferences",
        description = "Retrieves the dietary preferences for the currently authenticated user. Returns default preferences if none are set."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user preferences",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = UserPreferencesDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User is not authenticated",
            content = @Content
        )
    })
    public ResponseEntity<UserPreferencesDto> getCurrentUserPreferences() {
        UUID userId = SecurityUtils.getCurrentUserId();
        
        if (userId == null) {
            logger.error("Unable to get current user ID from security context");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        logger.info("Fetching preferences for user: {}", userId);
        UserPreferencesDto preferences = userPreferenceService.getUserPreferences(userId);
        
        return ResponseEntity.ok(preferences);
    }

    /**
     * Update current user's preferences.
     * 
     * @param preferencesDto the preferences to update
     * @return the updated UserPreferencesDto
     */
    @PutMapping("/me/preferences")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Update current user preferences",
        description = "Updates or creates dietary preferences for the currently authenticated user."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully updated user preferences",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = UserPreferencesDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid preferences data provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User is not authenticated",
            content = @Content
        )
    })
    public ResponseEntity<UserPreferencesDto> updateCurrentUserPreferences(
            @Valid @RequestBody 
            @Parameter(description = "User preferences to update", required = true)
            UserPreferencesDto preferencesDto) {
        
        UUID userId = SecurityUtils.getCurrentUserId();
        
        if (userId == null) {
            logger.error("Unable to get current user ID from security context");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        logger.info("Updating preferences for user: {}", userId);
        
        try {
            UserPreferencesDto updated = userPreferenceService.updateUserPreferences(userId, preferencesDto);
            return ResponseEntity.ok(updated);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid preferences data for user {}: {}", userId, e.getMessage());
            // Return error message in response body for better client error handling
            return ResponseEntity.badRequest()
                .body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * Get preferences for a specific user (admin only).
     * 
     * @param userId the user ID
     * @return UserPreferencesDto containing user preferences
     */
    @GetMapping("/{userId}/preferences")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(
        summary = "Get user preferences by ID (Admin only)",
        description = "Retrieves the dietary preferences for a specific user. Requires admin role."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved user preferences",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = UserPreferencesDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User is not authenticated",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "403",
            description = "User does not have admin role",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        )
    })
    public ResponseEntity<UserPreferencesDto> getUserPreferences(
            @PathVariable 
            @Parameter(description = "User ID", required = true)
            UUID userId) {
        
        logger.info("Admin fetching preferences for user: {}", userId);
        UserPreferencesDto preferences = userPreferenceService.getUserPreferences(userId);
        
        return ResponseEntity.ok(preferences);
    }

    /**
     * Delete current user's preferences.
     * 
     * @return empty response
     */
    @DeleteMapping("/me/preferences")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Delete current user preferences",
        description = "Deletes the dietary preferences for the currently authenticated user, reverting to defaults."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "204",
            description = "Successfully deleted user preferences",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User is not authenticated",
            content = @Content
        )
    })
    public ResponseEntity<Void> deleteCurrentUserPreferences() {
        UUID userId = SecurityUtils.getCurrentUserId();
        
        if (userId == null) {
            logger.error("Unable to get current user ID from security context");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        logger.info("Deleting preferences for user: {}", userId);
        userPreferenceService.deleteUserPreferences(userId);
        
        return ResponseEntity.noContent().build();
    }
    
    /**
     * Create error response body with message.
     * Helper method for consistent error responses.
     * 
     * @param message the error message
     * @return error response object
     */
    private Object createErrorResponse(String message) {
        return Map.of(
            "error", message,
            "timestamp", System.currentTimeMillis()
        );
    }
}