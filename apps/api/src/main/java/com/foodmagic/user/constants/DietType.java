package com.foodmagic.user.constants;

/**
 * Enum for supported diet types.
 * Centralizes diet type definitions for consistent validation.
 * 
 * <AUTHOR> (QA)
 * @since 1.0
 */
public enum DietType {
    LOW_CARB("Low carbohydrate diet"),
    HIGH_PROTEIN("High protein diet"),
    KETO("Ketogenic diet"),
    PALEO("Paleolithic diet"),
    MEDITERRANEAN("Mediterranean diet"),
    VEGAN("Vegan diet"),
    NONE("No specific diet");
    
    private final String description;
    
    DietType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if a string value is a valid diet type.
     * 
     * @param value the string to check
     * @return true if valid, false otherwise
     */
    public static boolean isValid(String value) {
        if (value == null) {
            return false;
        }
        try {
            DietType.valueOf(value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}