package com.foodmagic.user.repository;

import com.foodmagic.user.entity.UserPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for UserPreference entity.
 * Provides CRUD operations and custom queries for user preferences.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Repository
public interface UserPreferenceRepository extends JpaRepository<UserPreference, UUID> {

    /**
     * Find user preferences by user ID.
     * 
     * @param userId the user ID
     * @return Optional containing the user preferences if found
     */
    Optional<UserPreference> findByUserId(UUID userId);

    /**
     * Check if user preferences exist for a given user ID.
     * 
     * @param userId the user ID
     * @return true if preferences exist, false otherwise
     */
    boolean existsByUserId(UUID userId);

    /**
     * Delete user preferences by user ID.
     * 
     * @param userId the user ID
     */
    void deleteByUserId(UUID userId);

    /**
     * Find user preferences with user details loaded.
     * 
     * @param userId the user ID
     * @return Optional containing the user preferences with user details
     */
    @Query("SELECT up FROM UserPreference up LEFT JOIN FETCH up.user WHERE up.userId = :userId")
    Optional<UserPreference> findByUserIdWithUser(@Param("userId") UUID userId);

    /**
     * Get preferences JSON for a specific user.
     * This method returns only the preferences JSON without loading the entire entity.
     * 
     * @param userId the user ID
     * @return Optional containing the preferences map
     */
    @Query(value = "SELECT preferences_json FROM user_preferences WHERE user_id = :userId", nativeQuery = true)
    Optional<String> findPreferencesJsonByUserId(@Param("userId") UUID userId);
}