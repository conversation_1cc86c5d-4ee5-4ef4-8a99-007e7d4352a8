package com.foodmagic.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Data Transfer Object for user preferences.
 * Defines the structure for dietary preferences, allergies, and other food-related settings.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Schema(description = "User dietary preferences and settings")
public class UserPreferencesDto {

    @JsonProperty("allergies")
    @Schema(description = "List of allergens to avoid", example = "[\"peanuts\", \"shellfish\"]")
    @Size(max = 50, message = "Maximum 50 allergies allowed")
    private List<String> allergies;

    @JsonProperty("isVegetarian")
    @Schema(description = "Whether the user follows a vegetarian diet", example = "false")
    private Boolean isVegetarian;

    @JsonProperty("diet")
    @Schema(description = "Specific diet type", example = "LOW_CARB", allowableValues = {"LOW_CARB", "HIGH_PROTEIN", "KETO", "PALEO", "MEDITERRANEAN", "VEGAN", "NONE"})
    private String diet;

    @JsonProperty("preferredCuisines")
    @Schema(description = "List of preferred cuisine types", example = "[\"ITALIAN\", \"MEXICAN\"]")
    @Size(max = 20, message = "Maximum 20 cuisine preferences allowed")
    private List<String> preferredCuisines;

    @JsonProperty("unitSystem")
    @Schema(description = "Preferred measurement unit system", example = "METRIC", allowableValues = {"METRIC", "IMPERIAL"})
    private String unitSystem;

    @JsonProperty("locale")
    @Schema(description = "User's locale for localization", example = "zh-CN")
    private String locale;

    @JsonProperty("maxCookingTime")
    @Schema(description = "Maximum cooking time preference in minutes", example = "30")
    private Integer maxCookingTime;

    @JsonProperty("servingSize")
    @Schema(description = "Preferred serving size", example = "2")
    private Integer servingSize;

    @JsonProperty("avoidIngredients")
    @Schema(description = "List of ingredients to avoid (not necessarily allergies)", example = "[\"cilantro\", \"blue cheese\"]")
    @Size(max = 100, message = "Maximum 100 ingredients to avoid")
    private List<String> avoidIngredients;

    @JsonProperty("spiceLevel")
    @Schema(description = "Preferred spice level", example = "MILD", allowableValues = {"NONE", "MILD", "MEDIUM", "HOT", "EXTRA_HOT"})
    private String spiceLevel;

    // Constructors
    public UserPreferencesDto() {
    }

    // Getters and Setters
    public List<String> getAllergies() {
        return allergies;
    }

    public void setAllergies(List<String> allergies) {
        this.allergies = allergies;
    }

    public Boolean getIsVegetarian() {
        return isVegetarian;
    }

    public void setIsVegetarian(Boolean isVegetarian) {
        this.isVegetarian = isVegetarian;
    }

    public String getDiet() {
        return diet;
    }

    public void setDiet(String diet) {
        this.diet = diet;
    }

    public List<String> getPreferredCuisines() {
        return preferredCuisines;
    }

    public void setPreferredCuisines(List<String> preferredCuisines) {
        this.preferredCuisines = preferredCuisines;
    }

    public String getUnitSystem() {
        return unitSystem;
    }

    public void setUnitSystem(String unitSystem) {
        this.unitSystem = unitSystem;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public Integer getMaxCookingTime() {
        return maxCookingTime;
    }

    public void setMaxCookingTime(Integer maxCookingTime) {
        this.maxCookingTime = maxCookingTime;
    }

    public Integer getServingSize() {
        return servingSize;
    }

    public void setServingSize(Integer servingSize) {
        this.servingSize = servingSize;
    }

    public List<String> getAvoidIngredients() {
        return avoidIngredients;
    }

    public void setAvoidIngredients(List<String> avoidIngredients) {
        this.avoidIngredients = avoidIngredients;
    }

    public String getSpiceLevel() {
        return spiceLevel;
    }

    public void setSpiceLevel(String spiceLevel) {
        this.spiceLevel = spiceLevel;
    }

    @Override
    public String toString() {
        return "UserPreferencesDto{" +
                "allergies=" + allergies +
                ", isVegetarian=" + isVegetarian +
                ", diet='" + diet + '\'' +
                ", preferredCuisines=" + preferredCuisines +
                ", unitSystem='" + unitSystem + '\'' +
                ", locale='" + locale + '\'' +
                ", maxCookingTime=" + maxCookingTime +
                ", servingSize=" + servingSize +
                ", avoidIngredients=" + avoidIngredients +
                ", spiceLevel='" + spiceLevel + '\'' +
                '}';
    }
}