package com.foodmagic.user.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.Type;
import io.hypersistence.utils.hibernate.type.json.JsonType;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Entity representing user dietary preferences and settings.
 * Maps to the user_preferences table in PostgreSQL.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Entity
@Table(name = "user_preferences")
public class UserPreference {

    @Id
    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Type(JsonType.class)
    @Column(name = "preferences_json", columnDefinition = "jsonb", nullable = false)
    private Map<String, Object> preferences;

    @Column(name = "updated_at", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime updatedAt;

    @OneToOne(fetch = FetchType.LAZY)
    @MapsId
    @JoinColumn(name = "user_id")
    private User user;

    // Constructors
    public UserPreference() {
        this.preferences = Map.of();
        this.updatedAt = LocalDateTime.now();
    }

    public UserPreference(UUID userId, Map<String, Object> preferences) {
        this.userId = userId;
        this.preferences = preferences != null ? preferences : Map.of();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public Map<String, Object> getPreferences() {
        return preferences;
    }

    public void setPreferences(Map<String, Object> preferences) {
        this.preferences = preferences;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    @PrePersist
    @PreUpdate
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "UserPreference{" +
                "userId=" + userId +
                ", updatedAt=" + updatedAt +
                '}';
    }
}