package com.foodmagic.recipe.service;

import com.foodmagic.ai.client.PromptBuilder;
import com.foodmagic.recipe.dto.RecipeGenerationRequestDto;
import com.foodmagic.recipe.dto.RecipeResponseDto;
import com.foodmagic.user.dto.UserPreferencesDto;
import com.foodmagic.user.service.UserPreferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Service for generating recipes using AI with user preferences integration.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Service
public class RecipeGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(RecipeGenerationService.class);

    private final PromptBuilder promptBuilder;
    private final UserPreferenceService userPreferenceService;
    // Note: AIServiceClient will be injected when implemented from Story 1.4

    public RecipeGenerationService(
            PromptBuilder promptBuilder,
            UserPreferenceService userPreferenceService) {
        this.promptBuilder = promptBuilder;
        this.userPreferenceService = userPreferenceService;
    }

    /**
     * Generate a recipe for an authenticated user with their preferences.
     * 
     * @param userId the user ID
     * @param request the recipe generation request
     * @return generated recipe
     */
    public RecipeResponseDto generateRecipeForUser(UUID userId, RecipeGenerationRequestDto request) {
        logger.info("Generating recipe for user: {}", userId);

        // Get user preferences
        UserPreferencesDto userPreferences = userPreferenceService.getUserPreferences(userId);
        
        // Override request preferences with user's saved preferences if not explicitly provided
        if (request.getPreferences() == null) {
            request.setPreferences(userPreferences);
        }

        return generateRecipe(request);
    }

    /**
     * Generate a recipe for a guest user (no saved preferences).
     * 
     * @param request the recipe generation request
     * @return generated recipe
     */
    public RecipeResponseDto generateRecipeForGuest(RecipeGenerationRequestDto request) {
        logger.info("Generating recipe for guest user");
        
        // Guest users can provide preferences in the request, but they won't be saved
        return generateRecipe(request);
    }

    /**
     * Core recipe generation logic.
     * 
     * @param request the recipe generation request
     * @return generated recipe
     */
    private RecipeResponseDto generateRecipe(RecipeGenerationRequestDto request) {
        // Build the AI prompt with preferences
        String prompt = promptBuilder.buildRecipePrompt(
            request.getIngredients(),
            request.getPreferences()
        );

        logger.debug("Generated prompt: {}", prompt);

        // TODO: Call AI service client (from Story 1.4)
        // String aiResponse = aiServiceClient.generateRecipe(prompt);
        // RecipeResponseDto recipe = parseAIResponse(aiResponse);

        // For now, return a mock response
        return createMockRecipe(request);
    }

    /**
     * Create a mock recipe response for testing.
     * This will be replaced with actual AI integration.
     * 
     * @param request the recipe generation request
     * @return mock recipe
     */
    private RecipeResponseDto createMockRecipe(RecipeGenerationRequestDto request) {
        RecipeResponseDto recipe = new RecipeResponseDto();
        
        recipe.setId(UUID.randomUUID().toString());
        recipe.setName("AI Generated Recipe");
        recipe.setDescription("A delicious recipe generated based on your preferences and available ingredients.");
        recipe.setPrepTime(15);
        recipe.setCookTime(30);
        recipe.setServings(request.getPreferences() != null && request.getPreferences().getServingSize() != null 
            ? request.getPreferences().getServingSize() : 2);

        // Mock ingredients based on request
        List<RecipeResponseDto.IngredientDto> ingredients = new ArrayList<>();
        for (String ingredient : request.getIngredients()) {
            RecipeResponseDto.IngredientDto ing = new RecipeResponseDto.IngredientDto();
            ing.setName(ingredient);
            ing.setQuantity("100");
            ing.setUnit("grams");
            ingredients.add(ing);
        }
        recipe.setIngredients(ingredients);

        // Mock instructions
        List<String> instructions = new ArrayList<>();
        instructions.add("Prepare all ingredients");
        instructions.add("Cook according to preferences");
        instructions.add("Serve and enjoy");
        recipe.setInstructions(instructions);

        // Mock tags based on preferences
        List<String> tags = new ArrayList<>();
        if (request.getPreferences() != null) {
            if (request.getPreferences().getIsVegetarian() != null && request.getPreferences().getIsVegetarian()) {
                tags.add("vegetarian");
            }
            if (request.getPreferences().getDiet() != null) {
                tags.add(request.getPreferences().getDiet().toLowerCase());
            }
            if (request.getPreferences().getAllergies() != null && !request.getPreferences().getAllergies().isEmpty()) {
                tags.add("allergy-friendly");
            }
        }
        recipe.setTags(tags);

        logger.info("Generated mock recipe: {}", recipe.getName());
        
        return recipe;
    }
}