package com.foodmagic.recipe.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.foodmagic.user.dto.UserPreferencesDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for recipe generation.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Schema(description = "Recipe generation request")
public class RecipeGenerationRequestDto {

    @JsonProperty("ingredients")
    @Schema(description = "List of available ingredients", example = "[\"chicken\", \"rice\", \"tomato\"]", required = true)
    @NotEmpty(message = "At least one ingredient is required")
    @Size(max = 50, message = "Maximum 50 ingredients allowed")
    private List<String> ingredients;

    @JsonProperty("preferences")
    @Schema(description = "User preferences to apply (optional for guests)")
    private UserPreferencesDto preferences;

    @JsonProperty("recipeType")
    @Schema(description = "Type of recipe to generate", example = "MAIN_COURSE")
    private String recipeType;

    @JsonProperty("mealTime")
    @Schema(description = "Meal time", example = "DINNER")
    private String mealTime;

    // Constructors
    public RecipeGenerationRequestDto() {
    }

    // Getters and Setters
    public List<String> getIngredients() {
        return ingredients;
    }

    public void setIngredients(List<String> ingredients) {
        this.ingredients = ingredients;
    }

    public UserPreferencesDto getPreferences() {
        return preferences;
    }

    public void setPreferences(UserPreferencesDto preferences) {
        this.preferences = preferences;
    }

    public String getRecipeType() {
        return recipeType;
    }

    public void setRecipeType(String recipeType) {
        this.recipeType = recipeType;
    }

    public String getMealTime() {
        return mealTime;
    }

    public void setMealTime(String mealTime) {
        this.mealTime = mealTime;
    }
}