package com.foodmagic.recipe.controller;

import com.foodmagic.common.security.SecurityUtils;
import com.foodmagic.recipe.dto.RecipeGenerationRequestDto;
import com.foodmagic.recipe.dto.RecipeResponseDto;
import com.foodmagic.recipe.service.RecipeGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * REST controller for recipe generation endpoints.
 * Handles recipe creation with user preferences integration.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@RestController
@RequestMapping("/api/v1/recipes")
@Tag(name = "Recipe Generation", description = "Recipe generation and management endpoints")
public class RecipeController {

    private static final Logger logger = LoggerFactory.getLogger(RecipeController.class);

    private final RecipeGenerationService recipeGenerationService;

    public RecipeController(RecipeGenerationService recipeGenerationService) {
        this.recipeGenerationService = recipeGenerationService;
    }

    /**
     * Generate a recipe for authenticated users.
     * Automatically uses the user's saved preferences unless overridden in the request.
     * 
     * @param request the recipe generation request
     * @return generated recipe
     */
    @PostMapping("/generate")
    @SecurityRequirement(name = "bearerAuth")
    @Operation(
        summary = "Generate a recipe with user preferences",
        description = "Generates a recipe based on provided ingredients and the authenticated user's dietary preferences. " +
                      "User's saved preferences are automatically applied unless overridden in the request."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully generated recipe",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = RecipeResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User is not authenticated",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Error generating recipe",
            content = @Content
        )
    })
    public ResponseEntity<RecipeResponseDto> generateRecipe(
            @Valid @RequestBody 
            @Parameter(description = "Recipe generation request", required = true)
            RecipeGenerationRequestDto request) {
        
        // Check if user is authenticated
        UUID userId = SecurityUtils.getCurrentUserId();
        
        try {
            RecipeResponseDto recipe;
            
            if (userId != null) {
                // Authenticated user - use their saved preferences
                logger.info("Generating recipe for authenticated user: {}", userId);
                recipe = recipeGenerationService.generateRecipeForUser(userId, request);
            } else {
                // Guest user - use preferences from request if provided
                logger.info("Generating recipe for guest user");
                recipe = recipeGenerationService.generateRecipeForGuest(request);
            }
            
            return ResponseEntity.ok(recipe);
            
        } catch (Exception e) {
            logger.error("Error generating recipe: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate a recipe explicitly for guest users.
     * Does not require authentication and does not use saved preferences.
     * 
     * @param request the recipe generation request
     * @return generated recipe
     */
    @PostMapping("/generate/guest")
    @Operation(
        summary = "Generate a recipe for guest users",
        description = "Generates a recipe based on provided ingredients without requiring authentication. " +
                      "Preferences can be provided in the request but won't be saved."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully generated recipe",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = RecipeResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Error generating recipe",
            content = @Content
        )
    })
    public ResponseEntity<RecipeResponseDto> generateGuestRecipe(
            @Valid @RequestBody 
            @Parameter(description = "Recipe generation request", required = true)
            RecipeGenerationRequestDto request) {
        
        try {
            logger.info("Generating recipe for guest user (explicit endpoint)");
            RecipeResponseDto recipe = recipeGenerationService.generateRecipeForGuest(request);
            return ResponseEntity.ok(recipe);
            
        } catch (Exception e) {
            logger.error("Error generating guest recipe: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate multiple recipe suggestions.
     * Returns brief recipe ideas based on ingredients and preferences.
     * 
     * @param request the recipe generation request
     * @param count number of suggestions to generate (default 3, max 10)
     * @return list of recipe suggestions
     */
    @PostMapping("/suggest")
    @Operation(
        summary = "Get recipe suggestions",
        description = "Generates multiple brief recipe suggestions based on provided ingredients and preferences."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully generated suggestions",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = RecipeResponseDto[].class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request data",
            content = @Content
        )
    })
    public ResponseEntity<RecipeResponseDto[]> suggestRecipes(
            @Valid @RequestBody 
            @Parameter(description = "Recipe generation request", required = true)
            RecipeGenerationRequestDto request,
            @RequestParam(defaultValue = "3") 
            @Parameter(description = "Number of suggestions (max 10)")
            int count) {
        
        if (count < 1 || count > 10) {
            return ResponseEntity.badRequest().build();
        }
        
        // TODO: Implement multiple recipe suggestions
        // For now, return a single recipe as an array
        UUID userId = SecurityUtils.getCurrentUserId();
        RecipeResponseDto recipe;
        
        if (userId != null) {
            recipe = recipeGenerationService.generateRecipeForUser(userId, request);
        } else {
            recipe = recipeGenerationService.generateRecipeForGuest(request);
        }
        
        return ResponseEntity.ok(new RecipeResponseDto[]{recipe});
    }
}