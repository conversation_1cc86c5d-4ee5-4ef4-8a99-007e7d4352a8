package com.foodmagic.recipe.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * Response DTO for generated recipes.
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */
@Schema(description = "Generated recipe response")
public class RecipeResponseDto {

    @JsonProperty("id")
    @Schema(description = "Recipe ID", example = "550e8400-e29b-41d4-a716-446655440000")
    private String id;

    @JsonProperty("name")
    @Schema(description = "Recipe name", example = "Spicy Chicken Stir-Fry")
    private String name;

    @JsonProperty("description")
    @Schema(description = "Recipe description")
    private String description;

    @JsonProperty("prepTime")
    @Schema(description = "Preparation time in minutes", example = "15")
    private Integer prepTime;

    @JsonProperty("cookTime")
    @Schema(description = "Cooking time in minutes", example = "20")
    private Integer cookTime;

    @JsonProperty("servings")
    @Schema(description = "Number of servings", example = "4")
    private Integer servings;

    @JsonProperty("ingredients")
    @Schema(description = "List of ingredients with quantities")
    private List<IngredientDto> ingredients;

    @JsonProperty("instructions")
    @Schema(description = "Step-by-step cooking instructions")
    private List<String> instructions;

    @JsonProperty("nutrition")
    @Schema(description = "Nutritional information")
    private Map<String, Object> nutrition;

    @JsonProperty("tags")
    @Schema(description = "Recipe tags", example = "[\"quick\", \"healthy\", \"gluten-free\"]")
    private List<String> tags;

    @JsonProperty("imageUrl")
    @Schema(description = "Recipe image URL")
    private String imageUrl;

    // Nested DTO for ingredients
    @Schema(description = "Ingredient with quantity")
    public static class IngredientDto {
        @JsonProperty("name")
        @Schema(description = "Ingredient name", example = "chicken breast")
        private String name;

        @JsonProperty("quantity")
        @Schema(description = "Quantity amount", example = "500")
        private String quantity;

        @JsonProperty("unit")
        @Schema(description = "Measurement unit", example = "grams")
        private String unit;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getQuantity() {
            return quantity;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }
    }

    // Constructors
    public RecipeResponseDto() {
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getPrepTime() {
        return prepTime;
    }

    public void setPrepTime(Integer prepTime) {
        this.prepTime = prepTime;
    }

    public Integer getCookTime() {
        return cookTime;
    }

    public void setCookTime(Integer cookTime) {
        this.cookTime = cookTime;
    }

    public Integer getServings() {
        return servings;
    }

    public void setServings(Integer servings) {
        this.servings = servings;
    }

    public List<IngredientDto> getIngredients() {
        return ingredients;
    }

    public void setIngredients(List<IngredientDto> ingredients) {
        this.ingredients = ingredients;
    }

    public List<String> getInstructions() {
        return instructions;
    }

    public void setInstructions(List<String> instructions) {
        this.instructions = instructions;
    }

    public Map<String, Object> getNutrition() {
        return nutrition;
    }

    public void setNutrition(Map<String, Object> nutrition) {
        this.nutrition = nutrition;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
}