-- Create user_preferences table for storing user dietary preferences
-- Migration: V2__Create_user_preferences_table.sql
-- Date: 2025-01-14
-- Author: <PERSON> (Dev Agent)
-- Story: 2.1 - 基础个性化功能的后端支持

CREATE TABLE IF NOT EXISTS user_preferences (
    user_id UUID PRIMARY KEY,
    preferences_json JSONB NOT NULL DEFAULT '{}',
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Foreign key constraint with cascade delete
    CONSTRAINT fk_user_preferences_user
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);

-- Create index on updated_at for time-based queries
CREATE INDEX idx_user_preferences_updated_at ON user_preferences(updated_at);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON user_preferences 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comment for documentation
COMMENT ON TABLE user_preferences IS 'Stores user dietary preferences, allergies, and food-related settings';
COMMENT ON COLUMN user_preferences.user_id IS 'Foreign key reference to users table';
COMMENT ON COLUMN user_preferences.preferences_json IS 'JSONB field storing flexible preference data structure';
COMMENT ON COLUMN user_preferences.updated_at IS 'Timestamp of last preference update';