/**
 * User Preferences Data Transfer Object
 * Shared type definition for user dietary preferences across frontend and backend
 * 
 * <AUTHOR> (Dev Agent)
 * @since 1.0
 */

export type DietType = 
  | 'LOW_CARB' 
  | 'HIGH_PROTEIN' 
  | 'KETO' 
  | 'PALEO' 
  | 'MEDITERRANEAN' 
  | 'VEGAN' 
  | 'NONE';

export type UnitSystem = 'METRIC' | 'IMPERIAL';

export type SpiceLevel = 'NONE' | 'MILD' | 'MEDIUM' | 'HOT' | 'EXTRA_HOT';

export type CuisineType = 
  | 'ITALIAN'
  | 'MEXICAN'
  | 'CHINESE'
  | 'JAPANESE'
  | 'INDIAN'
  | 'THAI'
  | 'FRENCH'
  | 'SPANISH'
  | 'GREEK'
  | 'AMERICAN'
  | 'KOREAN'
  | 'VIETNAMESE'
  | 'MIDDLE_EASTERN'
  | 'AFRICAN'
  | 'CARIBBEAN';

export interface UserPreferencesDto {
  /**
   * List of allergens to avoid
   * @example ["peanuts", "shellfish"]
   */
  allergies?: string[];

  /**
   * Whether the user follows a vegetarian diet
   * @example false
   */
  isVegetarian?: boolean;

  /**
   * Specific diet type the user follows
   * @example "LOW_CARB"
   */
  diet?: DietType;

  /**
   * List of preferred cuisine types
   * @example ["ITALIAN", "MEXICAN"]
   */
  preferredCuisines?: CuisineType[];

  /**
   * Preferred measurement unit system
   * @example "METRIC"
   */
  unitSystem?: UnitSystem;

  /**
   * User's locale for localization
   * @example "zh-CN"
   */
  locale?: string;

  /**
   * Maximum cooking time preference in minutes
   * @example 30
   */
  maxCookingTime?: number;

  /**
   * Preferred serving size (number of servings)
   * @example 2
   */
  servingSize?: number;

  /**
   * List of ingredients to avoid (not necessarily allergies)
   * @example ["cilantro", "blue cheese"]
   */
  avoidIngredients?: string[];

  /**
   * Preferred spice level
   * @example "MILD"
   */
  spiceLevel?: SpiceLevel;
}

/**
 * Default preferences for new users or guests
 */
export const DEFAULT_USER_PREFERENCES: UserPreferencesDto = {
  allergies: [],
  isVegetarian: false,
  diet: 'NONE',
  preferredCuisines: [],
  unitSystem: 'METRIC',
  locale: 'en-US',
  maxCookingTime: 60,
  servingSize: 2,
  avoidIngredients: [],
  spiceLevel: 'MEDIUM'
};

/**
 * Helper function to validate preferences
 */
export function validateUserPreferences(prefs: UserPreferencesDto): boolean {
  if (prefs.allergies && prefs.allergies.length > 50) {
    return false;
  }
  if (prefs.preferredCuisines && prefs.preferredCuisines.length > 20) {
    return false;
  }
  if (prefs.avoidIngredients && prefs.avoidIngredients.length > 100) {
    return false;
  }
  if (prefs.maxCookingTime && (prefs.maxCookingTime < 0 || prefs.maxCookingTime > 480)) {
    return false;
  }
  if (prefs.servingSize && (prefs.servingSize < 1 || prefs.servingSize > 20)) {
    return false;
  }
  return true;
}