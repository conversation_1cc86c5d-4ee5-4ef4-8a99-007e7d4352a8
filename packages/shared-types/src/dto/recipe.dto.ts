export interface Ingredient {
  name: string;
  quantity: string;
}

export interface RecipeDto {
  id: string;
  title: string;
  description: string;
  imageUrl: string | null;
  altText?: string | null;
  cookingTimeMinutes: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  servings: number;
  caloriesPerServing?: number | null;
  tags?: string[] | null;
  ingredients: Ingredient[];
  instructions: string[];
  timers?: { step: number; seconds: number }[] | null;
  createdAt: string;
}