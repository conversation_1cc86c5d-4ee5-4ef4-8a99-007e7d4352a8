# **食光机 (FoodMagic) 全栈架构文档**

**版本:** 1.0
**日期:** 2025年8月9日
**作者:** <PERSON>, 架构师

## **1. 简介**

**本文档概述了“食光机 (FoodMagic)”项目的完整全栈架构，包括后端系统、前端实现及其集成方式 **。它将作为AI驱动开发过程的唯一事实来源，确保整个技术栈的一致性 。

**入门模板或现有项目 (Starter Template or Existing Project)** 

入门模板：无单一模板。采用 Nx Monorepo 统一工作区：后端用 Spring Initializr 生成 api，前端用 create-expo-app 生成 mobile。

鉴于我们的技术栈组合（Spring Boot + React Native）比较独特，市面上没有完美匹配的、开箱即用的单一全栈入门模板。因此，我建议我们采用一个更灵活、更强大的方法：

**建议方案:** 我们将使用 **Nx Monorepo** 作为我们项目的顶层骨架。在Nx工作区内，我们将分别使用以下官方工具来生成我们的前后端应用：

* **后端:** 使用 **Spring Initializr** (start.spring.io) 来生成符合行业标准的 `api` 应用。
* **前端:** 使用 **Expo** 提供的 `create-expo-app` 命令来生成我们的 `mobile` 应用。

## **2. 高阶架构 (High Level Architecture)**

### **技术摘要 (Technical Summary)**

“食光机”将作为一个采用**模块化单体架构**的全栈应用来构建。移动端优先的用户界面将使用 **React Native 和 Tamagui** 开发，后端服务则由 **Spring Boot** 强力驱动，并通过一个 **REST API** 与前端通信。整个项目将统一在一个 **Nx Monorepo** 中进行管理，并通过 **Docker** 容器化，部署在云平台（建议  **AWS** ）上。该架构旨在为用户提供一个快速、可靠且富有吸引力的AI食谱生成体验，同时保证系统在初期的高效开发和未来的平滑扩展能力。

### **平台与基础设施选择 (Platform and Infrastructure Choice)**

* **平台:** **阿里云** 或 **腾讯云** (待最终确定)
* **理由:** 为中国大陆的目标用户提供最佳访问性能，并与我们优先选择的本土AI大语言模型实现最低延迟的集成。
* **关键服务 (以阿里云为例):**
  * **计算:** ACK 或 SAE (用于运行Docker容器)
  * **数据库:** ApsaraDB RDS for PostgreSQL
  * **缓存:** ApsaraDB for Redis
  * **文件存储:** OSS
  * **搜索:** Elasticsearch Service
  * **网络:** SLB + API Gateway

### **代码仓库结构 (Repository Structure)**

* **结构:** **Nx Monorepo**
* **理由:** 正如我们之前深入讨论的，Nx为我们这种Java+JS的异构技术栈组合，提供了最成熟的集成、代码共享和CI/CD优化能力。

### **高阶架构图**

**代码段**

```
graph TD
    A[用户] --> B[移动应用 (React Native + Tamagui)]
    B --> C[SLB / API 网关]
    B --> J[Expo Updates CDN (EAS)]
    B --> K[图片/CDN (OSS + CDN 或等价)]
    C --> D[后端服务 (Spring Boot 模块化单体)]
    D --> E[数据库 (PostgreSQL on RDS)]
    D --> F[缓存 (Redis)]
    D --> G[文件存储 (OSS)]
    D --> H[搜索服务 (Elasticsearch 可选)]
    D --> I[第三方AI大语言模型 API]

    subgraph "云平台"
        C
        D
        E
        F
        G
        H
        K
    end

```

### **架构模式 (Architectural and Design Patterns)**

* **整体架构:****模块化单体 (Modular Monolith):**  在保证初期开发速度的同时，为未来的微服务化演进奠定基础。
* **后端模式:****仓库模式 (Repository Pattern):**  这是Spring Boot数据访问的最佳实践，它将数据访问逻辑与业务逻辑解耦，便于测试和维护。
* **前端模式:****组件化UI (Component-Based UI):**  采用可复用的UI组件来构建界面，提高开发效率和一致性。
* **集成模式:****API网关 (API Gateway):**  所有客户端请求都通过一个统一的入口点，便于实现统一的认证、限流和监控。
* **收藏交互（Toggle）的一致性与幂等：**
  - 服务端需要提供幂等接口（同一 user/recipe 多次收藏不会产生重复项；取消收藏安全可重复）。
  - 提供“查询是否已收藏”的轻量端点（GET/HEAD）。
* **事件外发（可选，后期）：**
  - 为 Cook Mode 使用行为与生成质量分析，可采用 **Transactional Outbox** 输出事件到队列/日志，再由分析作业汇聚（后期启用）。


## **3. 技术栈 (Tech Stack)**

### **技术栈清单**

| 类别         | 技术                        | 版本          | 用途               | 理由                                                               |
| ------------ | --------------------------- | ------------- | ------------------ | ------------------------------------------------------------------ |
| 前端语言     | TypeScript                  | 5.4.x         | 主要开发语言       | 提供强类型支持，提升代码质量和可维护性。                           |
| 前端框架     | React Native (Expo)         | 0.74.x        | 跨平台移动应用框架 | 一次开发，双端（iOS/Android）运行，生态成熟。                      |
| UI组件库     | Tamagui                     | 1.9x.x        | 统一的UI与样式系统 | 高性能，支持响应式设计，符合我们的设计哲学。                       |
| 状态管理     | **(建议)**Zustand           | 4.5.x         | 轻量级前端状态管理 | 相比Redux更简洁，API友好，足以满足MVP需求。                        |
| 后端语言     | Java                        | 17 (LTS)      | 主要后端开发语言   | 性能稳定，生态系统庞大，是企业级应用的首选。                       |
| 后端框架     | Spring Boot                 | 3.2.x         | 后端应用框架       | 开发效率高，社区活跃，拥有强大的安全和数据访问能力。               |
| API 风格     | REST                        | (OpenAPI 3.x) | 前后端通信接口风格 | 成熟、通用，生态工具链完整。                                       |
| 数据库       | PostgreSQL                  | 16.x          | 主关系型数据库     | 功能强大，高度可靠，支持复杂的查询和事务。                         |
| 缓存         | Redis                       | 7.2.x         | 键值对缓存数据库   | 性能极高，用于缓存高频访问的数据，降低数据库压力。                 |
| 文件存储     | 阿里云 OSS                  | N/A           | 对象存储服务       | 存储用户上传的图片等文件，可靠、可扩展且成本低。                   |
| 认证授权     | Spring Security (JWT)       | 6.2.x         | 安全框架           | Spring Boot生态中最强大的安全解决方案。                            |
| 前端测试     | Jest, RTL                   | 29.x.x        | 单元/组件测试      | React Native社区的官方标准，测试组件交互。                         |
| 后端测试     | JUnit 5, Mockito            | 5.10.x        | 单元/集成测试      | Java生态中最主流的测试框架。                                       |
| E2E测试      | **(MVP阶段暂缓)**     | N/A           | 端到端测试         | 根据我们之前的决策，在MVP之后引入。                                |
| 构建工具     | Gradle                      | 8.7.x         | Java项目构建工具   | 相比Maven更现代、更灵活。                                          |
| 基础设施代码 | **(建议)**Terraform         | 1.8.x         | 基础设施管理       | 行业标准，声明式地管理云资源，支持多云环境。                       |
| CI/CD        | **(建议)**GitHub Actions    | N/A           | 持续集成/持续部署  | 与代码仓库无缝集成，配置简单，社区生态丰富。                       |
| 监控         | 阿里云 CloudMonitor         | N/A           | 应用与基础设施监控 | 阿里云原生监控服务，开箱即用，与我们选择的平台紧密集成。           |
| 日志         | Logback, 阿里云 Log Service | N/A           | 日志记录与聚合     | Spring Boot默认日志框架 + 阿里云原生日志服务，便于集中查询和分析。 |
| 动效/手势 | React Native Reanimated / Gesture Handler | 最新稳定 | 高帧率动效/原生手势 |
| 图形      | React Native Skia                         | 最新稳定 | 颗粒/柔光/特效     |
| 触觉/朗读 | Expo Haptics / Speech (TTS)               | 最新稳定 | 成功/错误/朗读     |
| 合同生成  | openapi-typescript or gradle-openapi-gen  | N/A      | 生成 TS API 类型   |


## **4. 数据模型 (Data Models)**

本节将定义应用的核心业务实体，它们的数据结构将在前后端之间共享，并最终映射到我们的数据库表中。我们将逐一进行定义和审议，首先从最核心的**用户 (User)** 模型开始。

### **模型 1: User (用户)**

* **用途:** 代表应用的每一个独立用户。用于存储认证信息、个人资料以及与其他数据（如收藏的食谱）的关联关系。
* **关键属性:**

  * `id`: `UUID`
  * `email`: `String` (可为空, 唯一)
  * `password_hash`: `String` (可为空)
  * `display_name`: `String` (可为空)
  * **`avatar_url`** : `String` (可为空)
  * **`auth_provider`** : `String` (例如: 'LOCAL', 'WECHAT')
  * **`provider_id`** : `String` (可为空)
  * **`status`** : `String` (例如: 'ACTIVE', 'SUSPENDED')
  * **`subscription_tier`** : `String` (例如: 'FREE', 'PREMIUM')
  * `created_at`: `Timestamp`
  * `updated_at`: `Timestamp`
* **共享TypeScript接口 (Shared DTO):**

  * 这是将在我们的Monorepo中，由前端和后端共享的数据传输对象，它**不包含**密码等敏感信息。

  **TypeScript**

  ```
  export interface UserDto {
    id: string;
    email: string | null;
    displayName: string | null;
    avatarUrl: string | null;
    subscriptionTier: 'FREE' | 'PREMIUM';
  }
  ```
* **关系:**

  * 一个 **User** 可以拥有多个  **SavedRecipe (收藏的食谱)** 。
  * 一个 **User** 拥有一个  **UserPreference (用户偏好)** 。

### **模型 2: Recipe (食谱)**

* **用途:** 代表由AI生成的一份具体的食谱。这是我们应用的核心内容实体，用户将围绕它进行浏览、收藏等主要操作。
* **关键属性:**

  * `id`: `UUID` - 主键，唯一标识符
  * `title`: `String` - 食谱的标题（菜品名称）
  * `description`: `Text` - AI生成的、一段简短诱人的菜品描述
  * `image_url`: `String` (可为空) - 用于展示的菜品图片链接
  * `cooking_time_minutes`: `Integer` - 预估的烹饪总时长（分钟）
  * `difficulty`: `String` (枚举: 'EASY', 'MEDIUM', 'HARD') - 难度等级
  * `servings`: `Integer` - 适用份量（例如，2人份）
  * `ingredients_json`: `JSONB` - 存储结构化的食材清单，例如 `[{"name": "番茄", "quantity": "2个"}, ...]`
  * `instructions_json`: `JSONB` - 存储结构化的、分步的烹饪指南，例如 `["第一步：...", "第二步：..."]`
  * `source_ingredients_json`: `JSONB` - **(重要)** 存储当初用户输入的、用于生成这份食谱的原始食材列表，用于数据分析和AI效果调试
  * `created_by_user_id`: `UUID` - 外键，关联到 `User`表，记录这份食谱是由哪位用户触发生成的
  * `created_at`: `Timestamp` - 创建时间
  * `calories_per_serving`: `Integer` (可为空) - 单份能量（kcal）
  * `tags`: `TEXT[]` (可为空) - 口味/风格标签（如 '清爽','高蛋白'）
  * `timers_json`: `JSONB` (可为空) - 步骤内计时标注（秒），示例 [{"step":2,"seconds":180}]
  * `alt_text`: `String` (可为空) - 主图无障碍文本（菜名 + 主要食材）

* **共享TypeScript接口 (Shared DTO):**
  **TypeScript**

  ```
  interface Ingredient { name: string; quantity: string; }

  export interface RecipeDto {
    id: string;
    title: string;
    description: string;
    imageUrl: string | null;
    altText?: string | null;
    cookingTimeMinutes: number;
    difficulty: 'EASY' | 'MEDIUM' | 'HARD';
    servings: number;
    caloriesPerServing?: number | null;
    tags?: string[] | null;
    ingredients: Ingredient[];
    instructions: string[];
    timers?: { step: number; seconds: number }[] | null;
    createdAt: string; // ISO
  }

  ```
* **关系:**

  * 一份 **Recipe** 由一个 **User** 创建。
  * 一份 **Recipe** 可以被多个 **User** 收藏（通过一个中间的关联表 `SavedRecipe`）。

### **模型 3: SavedRecipe (收藏记录)**

* **用途:** 这是一个 **关联模型（或称“连接表”）** ，用于建立 `User`和 `Recipe`之间的多对多关系。它的每一条记录，都代表一个用户将一份食谱收藏到了他/她的个人收藏夹中。
* **关键属性:**
  * `user_id`: `UUID` - 外键，关联到 `User`表的 `id`。
  * `recipe_id`: `UUID` - 外键，关联到 `Recipe`表的 `id`。
  * `saved_at`: `Timestamp` - 用户点击收藏的时间。
  * *(注：`user_id`和 `recipe_id`将共同构成一个复合主键，确保一个用户只能收藏同一份食谱一次。)*
* **共享TypeScript接口 (Shared DTO):**
  * 这个模型主要用于后端的数据库结构，它本身通常 **没有一个直接对应的DTO** 。当“我的收藏夹”页面请求数据时，后端会通过这个关联表，直接查询并返回一个 `RecipeDto[]`（用户收藏的食谱列表）。
* **关系:**
  * 属于一个  **User** 。
  * 属于一个  **Recipe** 。

### **模型 4: UserPreference (用户偏好)**

* **用途:** 存储与单个用户关联的所有个性化设置。这份数据是“食光机”能够从一个通用工具，升级为“懂你的智能伙伴”的核心。
* **关键属性:**

  * `user_id`: `UUID` - 主键，同时也是外键，与 `User`表建立一对一关系。
  * `preferences_json`: `JSONB` - 用于存储所有偏好设置的灵活字段。采用JSON格式可以让我们在未来轻松增改偏好，而无需修改数据库表结构。
    * **示例:** `{
  "allergies": ["peanuts", "shellfish"],
  "diet": "LOW_CARB",
  "preferred_cuisines": ["ITALIAN", "MEXICAN"],
  "unit_system": "METRIC",           // or "IMPERIAL"
  "locale": "zh-CN"                  // 用于文案/单位/朗读
  }
  `
  * `updated_at`: `Timestamp` - 偏好设置的最后更新时间。
* **共享TypeScript接口 (Shared DTO):**
  **TypeScript**

  ```
  export interface UserPreferencesDto {
  allergies?: string[];
  isVegetarian?: boolean;
  diet?: 'LOW_CARB' | 'HIGH_PROTEIN' | 'KETO';
  preferredCuisines?: string[];
  unitSystem?: 'METRIC' | 'IMPERIAL';
  locale?: string; // e.g., 'zh-CN'
  }

  ```
* **关系:**

  * 属于一个 **User** (一对一关系)。

## **5. API 规范 (API Specification)**

根据我们在“技术栈”部分确定的**REST API**风格，我将为您起草一份 **OpenAPI 3.0** 规范的骨架。这份规范将精确定义每一个API端点（Endpoint）的路径、请求方式、参数和返回的数据结构。它是前后端开发团队之间最重要的协作文档。

以下是基于我们已批准的用户故事，草拟的API规范核心部分：

**YAML**

```
openapi: 3.0.1
info:
  title: 食光机 (FoodMagic) API
  version: "1.0.0"
  description: 食光机应用的核心后端API

servers:
  - url: /api/v1
    description: API V1 版本

paths:
  # === 认证模块 ===
  /auth/register:
    post:
      summary: 用户注册
      # ... (定义请求体和响应)
  /auth/login:
    post:
      summary: 用户登录
      # ... (定义请求体和响应)

  # === 用户模块 ===
  /users/me:
    get:
      summary: 获取当前用户信息
      security:
        - bearerAuth: [] # 这是一个受保护的路由
      responses:
        '200':
          description: 成功的响应
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDto' # 引用我们之前定义的数据模型

  /users/me/preferences:
    get:
      summary: 获取用户偏好
      security:
        - bearerAuth: []
      # ...
    put:
      summary: 更新用户偏好
      security:
        - bearerAuth: []
      # ...

  # === 核心功能模块 ===
  /recipes/generate:
    post:
      summary: 根据食材生成食谱
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ingredients:
                  type: array
                  items:
                    type: string
                preferences:
                  $ref: '#/components/schemas/UserPreferencesDto'
                locale:
                  type: string
                  example: zh-CN
                unitSystem:
                  type: string
                  enum: [METRIC, IMPERIAL]
                reduceMotion:
                  type: boolean
                  description: 前端系统设置透传，便于后端选择简化返回（如不返回动效资源提示）
              required:[ingredients]     
      responses:
        '200':
          description: 成功生成的食谱
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecipeDto'

  # === 收藏夹模块 ===
    # 查询是否已收藏（轻量）
    /saved-recipes/{recipeId}:
      get:
        summary: 是否已收藏
        security: [{ bearerAuth: [] }]
        responses:
          '200': { description: 已收藏 }
          '404': { description: 未收藏 }
      head:
        summary: 是否已收藏(HEAD)
        security: [{ bearerAuth: [] }]

    # 收藏（幂等）
    /saved-recipes/{recipeId}:
      put:
        summary: 收藏（幂等）
        security: [{ bearerAuth: [] }]
        responses:
          '204': { description: 已收藏或成功收藏 }

    # 取消收藏（幂等）
      delete:
        summary: 取消收藏（幂等）
        security: [{ bearerAuth: [] }]
        responses:
          '204': { description: 已取消或本就未收藏 }
    # ...

# === 可复用的数据模型定义 ===
components:
  schemas:
    UserDto:
      type: object
      properties:
        id:
          type: string
        # ... (根据我们之前定义的UserDto)
    RecipeDto:
      type: object
      properties:
        id:
          type: string
        # ... (根据我们之前定义的RecipeDto)

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
```

## **6. 组件 (Components)**

本节描述了构成“食光机”全栈系统的主要逻辑组件/服务。这里的“组件”是高阶架构层面的概念，定义了系统的主要功能模块及其职责边界。

### **组件列表**

#### **1. Mobile Client (移动客户端)**

* **职责:** 提供完整的用户界面（UI），管理前端本地状态，并与后端API进行安全通信。是我们用户直接交互的媒介。
* **关键接口:** 消费我们在上一节中定义的REST API。
* **依赖关系:** 依赖于后端API。
* **技术栈:** React Native, Tamagui, Zustand。

#### **2. API Gateway (API网关)**

* **职责:** 作为所有外部请求的统一入口，负责流量路由、SSL证书卸载、安全防护等。
* **关键接口:** 向公网暴露我们在上一节中定义的REST API。
* **依赖关系:** 依赖于后端服务。
* **技术栈:** 阿里云 API Gateway / SLB。

#### **3. Backend Service (后端服务 - 模块化单体)**

* **职责:** 承载应用的所有核心业务逻辑。它在内部被划分为几个清晰的、高内聚的模块。
* **关键接口:** 实现我们在上一节中定义的REST API。
* **依赖关系:** 依赖于数据库、缓存和AI服务客户端。
* **技术栈:** Spring Boot, Java 17。
* **核心内部模块:**
  * **认证模块 (Auth Module):** 负责处理用户注册、登录和会话管理。
  * **用户模块 (User Module):** 负责管理用户资料和个人偏好。
  * **食谱模块 (Recipe Module):** 负责处理食谱的生成、收藏等核心业务。

#### **4. AI Service Client (AI服务客户端)**

* **职责:** 这是一个 **专用的内部组件** ，负责封装与所有外部AI大语言模型通信的复杂逻辑。它处理API密钥管理、提示工程(Prompt Engineering)、以及对AI返回结果的解析和验证。
* **关键接口:**
  * **对内:** 向“食谱模块”提供一个简洁的Java接口（例如 `generateRecipe(ingredients, preferences)`）。
  * **对外:** 消费第三方AI模型的REST API。
* **依赖关系:** 依赖于第三方AI大语言模型API。
* **技术栈:** Java HTTP Client, Jackson (用于JSON解析)。

## **7. 外部API (External APIs)**

本节将详细定义“食光机”项目需要集成的所有第三方服务API。

### **1. AI大语言模型 API (AI Large Language Model APIs)**

* **用途:** 这是我们应用实现核心功能的引擎，用于根据用户输入动态生成食谱。我们的**“AI服务客户端 (AI Service Client)”**组件将专门负责与这些API的集成。
* **具体服务:** 根据PRD，我们将优先集成中国的主流模型，例如：
  * **智谱AI (GLM系列)**
  * **阿里通义千问 (Qwen系列)**
  * **DeepSeek**
  * (备选) Google Gemini, OpenAI GPT系列
* **文档链接:** 开发团队在实现时，需要查阅并集成具体模型的官方API文档。
* **认证方式:** 通常使用  **API Key** ，通过 `Authorization` HTTP Header 进行认证。这些密钥必须作为安全机密（Secrets）进行管理，绝不能硬编码在代码中。
* **速率限制:** 所有主流LLM API都有速率限制（Rate Limiting，如每分钟请求数）和配额。我们的“AI服务客户端”必须能优雅地处理因达到限制而返回的HTTP 429错误，并采用适当的重试或降级策略。
* **使用的关键端点 (Key Endpoints Used):**
  * **`POST /vX/chat/completions` (通用模式):**
    * **描述:** 向此端点发送一个精心构造的、包含用户食材和偏好等信息的指令(Prompt)，并接收一个包含完整食谱内容的流式或非流式响应。

## **8. 核心工作流 (Core Workflows)**

本节将使用序列图来描绘我们系统中最关键的工作流程—— **用户生成食谱** 。这张图将清晰地展示从用户点击按钮到最终看到食谱，我们定义的各个组件之间的数据流转和调用顺序。

### **工作流 1: 用户生成食谱**

**代码段**

```
sequenceDiagram
    actor User as 用户
    participant MobileClient as 移动客户端
    participant APIGateway as API网关
    participant BackendService as 后端服务
    participant Cache as 缓存 (Redis)
    participant AIServiceClient as AI服务客户端
    participant ExternalAI as 外部AI模型API

    User->>MobileClient: 输入食材并点击“生成”
    activate MobileClient
    MobileClient->>APIGateway: POST /recipes/generate
    activate APIGateway
    APIGateway->>BackendService: (转发请求)
    deactivate APIGateway
    activate BackendService

    Note over BackendService, Cache: 食谱模块先检查缓存
    BackendService->>Cache: GET recipe:[hash(ingredients + preferences + locale + unitSystem)]
  
    alt 缓存命中 (Cache Hit)
        Cache-->>BackendService: (返回已缓存的RecipeDto)
        BackendService-->>MobileClient: (HTTP 200 OK, from Cache)
    else 缓存未命中 (Cache Miss)
        BackendService->>AIServiceClient: generateRecipe(ingredients, prefs)
        activate AIServiceClient
    
        AIServiceClient->>ExternalAI: POST /vX/chat/completions
        activate ExternalAI
        ExternalAI-->>AIServiceClient: (AI-generated recipe text)
        deactivate ExternalAI
    
        alt AI响应验证失败
            AIServiceClient-->>BackendService: (抛出 ValidationException)
            BackendService-->>MobileClient: (HTTP 502 Bad Gateway Error)
        else AI响应验证成功
            AIServiceClient-->>BackendService: (返回已解析的RecipeDto)
            deactivate AIServiceClient
        
            Note right of BackendService: 将新结果异步存入缓存
            BackendService->>Cache: SET recipe:[ingredient_hash] (with TTL) # TTL 视配置而定（如 24h）；命中率与错配率按 ingredients+prefs 组合评估。
        
            BackendService-->>MobileClient: (HTTP 200 OK, from AI)
        end
    end
    deactivate BackendService
  
    MobileClient->>User: 展示精美的食谱页面
    deactivate MobileClient
```


## **9. 数据库模式 (Database Schema)**

本节将提供具体的SQL DDL (数据定义语言) 语句，用于在我们的 **PostgreSQL** 数据库中创建应用的表结构。这份模式严格遵循了我们已批准的四个核心数据模型。

**SQL**

```
-- 用户表 (Users Table)
-- 用于存储所有用户的基础信息和认证资料
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255),
    display_name VARCHAR(100),
    avatar_url VARCHAR(255),
    auth_provider VARCHAR(50) NOT NULL,
    provider_id VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    subscription_tier VARCHAR(50) NOT NULL DEFAULT 'FREE',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    -- 确保社交登录的唯一性
    UNIQUE (auth_provider, provider_id)
);

-- 食谱表 (Recipes Table)
-- 存储由AI生成的核心食谱内容
CREATE TABLE recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    cooking_time_minutes INT CHECK (cooking_time_minutes > 0),
    difficulty VARCHAR(50),
    servings INT CHECK (servings > 0),
    ingredients_json JSONB NOT NULL,
    instructions_json JSONB NOT NULL,
    source_ingredients_json JSONB,
    created_by_user_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
-- 新增字段（如为线上迁移，逐步增加并填充/回填）
ALTER TABLE recipes
  ADD COLUMN calories_per_serving INT,
  ADD COLUMN tags TEXT[],
  ADD COLUMN timers_json JSONB,
  ADD COLUMN alt_text VARCHAR(255);

-- 索引（标签检索/组合查询）
CREATE INDEX IF NOT EXISTS idx_recipes_on_tags ON recipes USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_recipes_on_created_at ON recipes (created_at);

-- 收藏幂等语义已由 (user_id, recipe_id) 主键保障；补逆向查询索引：
CREATE INDEX IF NOT EXISTS idx_user_saved_recipes_on_recipe_id ON user_saved_recipes (recipe_id);

-- 为查询某个用户创建的所有食谱建立索引
CREATE INDEX idx_recipes_on_user_id ON recipes (created_by_user_id);

-- 用户偏好表 (User Preferences Table)
-- 与用户表建立一对一关系，存储个性化设置
CREATE TABLE user_preferences (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    preferences_json JSONB,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 用户收藏食谱关联表 (User Saved Recipes Join Table)
-- 建立用户和食谱之间的多对多关系
CREATE TABLE user_saved_recipes (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    saved_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (user_id, recipe_id)
);
-- 为快速查询某个用户的所有收藏建立索引
CREATE INDEX idx_user_saved_recipes_on_user_id ON user_saved_recipes (user_id);
```


## **10. 统一项目结构 (Unified Project Structure)**

本节将提供一个可视化的文件目录树，它严格遵循了我们已确定的**Nx Monorepo**策略，并为我们 **异构的技术栈** （Java Spring Boot + TypeScript React Native）提供了清晰、合理的组织方式。

**Plaintext**

```
food-magic-app/
├── .github/
│   └── workflows/
│       └── ci.yml          # GitHub Actions CI/CD 流水线配置
├── apps/                   # 存放可独立运行的应用程序
│   ├── api/                # Spring Boot 后端应用 (Java)
│   │   ├── src/main/java/com/foodmagic/
│   │   │   ├── auth/       # 认证模块
│   │   │   ├── recipe/     # 食谱模块
│   │   │   ├── user/       # 用户模块
│   │   │   └── FoodMagicApplication.java
│   │   └── build.gradle    # 后端构建脚本
│   └── mobile/             # React Native (Expo) 前端应用 (TypeScript)
│       ├── app/            # 页面与导航
│       ├── assets/         # 静态资源 (图片, 字体)
│       ├── components/     # 可复用的UI组件
│       ├── services/       # 调用API的服务
│       └── package.json    # 前端依赖
├── packages/               # 存放可在应用之间共享的代码包
│   ├── shared-types/       #【关键】前后端共享的TypeScript DTOs/类型定义
│   │   └── src/index.ts
│   └── eslint-config/      # 共享的前端代码检查规则
│   ├── api-contract/       # 由 OpenAPI 生成的 TS 客户端与类型（单一事实来源）
│   └── ui/                 # 前端 UI 共享包（tamagui 主题/组件变体/图标适配）
├── docs/                   # 项目文档
│   ├── prd.md
│   └── architecture.md
├── nx.json                 # Nx Monorepo 的核心配置文件
└── package.json            # Monorepo 根目录的 package.json
```


## **11. 开发工作流 (Development Workflow)**

本节旨在为团队提供一套清晰、统一的本地开发设置和工作流程，以最大限度地提高生产力并减少环境不一致导致的问题。

### **本地开发设置 (Local Development Setup)**

#### **先决条件 (Prerequisites)**

在开始之前，每位开发者都需要在本地机器上安装以下工具：

* Node.js (v20.x LTS)
* JDK 17 (LTS)
* Docker Desktop
* Git

#### **首次安装 (Initial Setup)**

只需执行一次的安装步骤：

**Bash**

```
# 1. 克隆代码仓库
git clone [repository_url]
cd food-magic-app

# 2. 安装所有JavaScript依赖
npm install
```

#### **日常开发命令 (Development Commands)**

得益于Nx Monorepo，我们可以使用简洁的命令来操作我们的应用：

**Bash**

```
# 启动前端移动应用 (React Native)
nx serve mobile

# 启动后端服务 (Spring Boot)
nx serve api

# 同时启动所有应用
nx run-many --target=serve --all

# 运行后端测试
nx test api

# 运行前端代码检查
nx lint mobile
```

### **环境配置 (Environment Configuration)**

项目使用 `.env`文件来管理环境变量。在首次安装后，开发者需要根据模板创建自己的本地配置文件。

#### **必需的环境变量**

* **后端 (`apps/api/.env`):**
  **代码段**

  ```
  # 数据库连接
  DB_URL=******************************************
  DB_USER=postgres
  DB_PASSWORD=your_local_db_password

  # Redis连接
  REDIS_URL=redis://localhost:6379

  # JWT 安全密钥
  JWT_SECRET=a_very_strong_and_long_random_secret

  # AI 模型 API Key
  AI_API_KEY=your_ai_provider_api_key
  ```
* **前端 (`apps/mobile/.env`):**
  **代码段**

  ```
  # 后端API的基础URL
  API_BASE_URL=http://localhost:8080/api/v1
  ```


## **12. 部署架构 (Deployment Architecture)**

本节将定义我们的部署策略和CI/CD（持续集成/持续部署）流水线，确保我们能实现快速、可重复的发布。

### **部署策略 (Deployment Strategy)**

* **后端 (Spring Boot API):**
  * **打包:** 后端应用将被打包成一个 **Docker镜像** 。
  * **存储:** 生成的镜像将被推送到 **阿里云容器镜像服务 (ACR)** 。
  * **运行:** 我们将在**阿里云容器服务Kubernetes版 (ACK)**上运行我们的后端服务，以获得最佳的弹性和可管理性。
* **前端 (React Native Mobile App):**
  * **应用壳:** 应用的二进制文件 (`.apk`/`.ipa`) 将通过标准的App Store和Google Play流程进行发布和更新。
  * **业务逻辑与界面更新:** 我们将使用 **Expo Updates (EAS Update)** 服务。这允许我们在不重新提交应用审核的情况下，向用户手机上的App**实时推送**JavaScript代码包的更新，极大地加快了迭代速度。

### **CI/CD 流水线 (GitHub Actions)**

我们将使用与代码仓库无缝集成的GitHub Actions来自动化我们的构建、测试和部署流程。

* **触发器:** 推送（push）到 `main` (生产) 或 `develop` (预发) 分支。
* **主要阶段:**
  1. **Setup:** 检出代码，安装Java和Node.js环境。
  2. **Lint & Test:** 使用 `nx affected --target=test` 命令，**仅对被代码改动影响到的项目**运行测试。
  3. **Build:**
     * (后端) 构建Spring Boot应用并将其打包成Docker镜像。
     * (前端) 构建JS代码包 (JS Bundle)。
  4. **Push:**
     * (后端) 将新的Docker镜像推送到阿里云ACR。
  5. **Deploy:**
     * (后端) 使用 `kubectl`命令，将ACK集群中的服务更新到最新的镜像版本。
     * (前端) 使用 `eas update`命令，将新的JS代码包发布给用户。

### **环境 (Environments)**

我们将设立三个标准环境：

| 环境                       | 前端连接的后端URL                     | 后端服务地址                  | 部署触发分支 | 用途             |
| -------------------------- | ------------------------------------- | ----------------------------- | ------------ | ---------------- |
| **开发**             | `http://localhost:8080`             | 本地运行                      | N/A          | 日常开发         |
| **预发(Staging)**    | `https://api-staging.foodmagic.com` | `api-staging.foodmagic.com` | `develop`  | 发布前的最终测试 |
| **生产(Production)** | `https://api.foodmagic.com`         | `api.foodmagic.com`         | `main`     | 面向最终用户     |

导出到 Google 表格


## **13. 安全与性能 (Security and Performance)**

本节将统一阐述我们为确保“食光机”应用既安全可靠又快速响应而采取的核心策略。

### **安全要求 (Security Requirements)**

* **前端安全 (Frontend Security):**
  * **令牌存储:** Access Token 存于内存；Refresh Token 存于 **OS 安全存储**（iOS Keychain / Android Keystore，经 Expo SecureStore 或等价），避免使用 Web Cookie 方案。
  * **安全通信:** 移动客户端与后端的所有通信都必须强制使用HTTPS/TLS加密。
* **后端安全 (Backend Security):**
  * **认证与授权:** 所有API端点，除非被明确标记为公开（如 `/auth/register`），都必须受Spring Security保护，需要一个有效的JWT才能访问。
  * **输入验证:** 必须在数据进入业务逻辑层之前，对所有来自客户端的输入进行严格的合法性校验。
  * **速率限制 (Rate Limiting):** 对关键的、成本较高的API（特别是 `/recipes/generate`）实施速率限制，以防止恶意攻击和滥用。
  * **CORS策略:** 配置严格的跨源资源共享（CORS）策略，只允许我们自己的前端应用访问API。
* **数据安全 (Data Security):**
  * **密码存储:** 所有用户密码必须使用业界推荐的强哈希算法（如BCrypt）进行加盐哈希后才能存入数据库。
  * **数据加密:** 数据库中的个人身份信息（PII）等敏感数据，应考虑在静态时进行加密。

### **性能优化 (Performance Optimization)**

* **后端性能:**
  * **缓存策略:** 积极、主动地利用Redis进行缓存。特别是缓存AI生成的结果和不常变化但读取频繁的数据（如食材列表）。
  * **数据库优化:** 所有数据库查询都必须经过优化。必须为所有外键和高频查询字段（`WHERE`子句中出现的字段）建立索引。
  * **异步处理:** 对于非核心、耗时的操作（例如，记录分析数据），应考虑使用异步任务处理。
* **前端性能:**
  * **打包体积:** 严格控制应用的最终打包体积。所有引入的第三方库都需经过评估。
  * **资源优化:** 所有静态资源（图片、字体、动画文件）都必须经过极致压缩。
  * **渲染性能:** 采用代码分割和异步加载技术，优先渲染核心内容。所有动画必须使用高性能库（`React Native Reanimated`）并经过性能测试，确保稳定在60FPS。
  * **动效与资源预算:** 遵守 UI/UX 规范——单页动效资源 ≤ 50KB；遵循系统“减少动态”开关，必要时降级为静态/渐隐。



## **14. 测试策略 (Testing Strategy)**

本节旨在为“食光机”项目定义一个清晰、分层的测试策略，以确保产品质量、加速开发反馈并降低风险。

### **测试金字塔 (Testing Pyramid)**

我们的MVP阶段将遵循一个务实的测试金字塔模型：

**Plaintext**

```
      /      \
     /        \
    / MANUAL QA \  <-- (MVP阶段, 由详尽的手动测试清单覆盖核心流程)
   /_____________\
  /               \
 / INTEGRATION T.  \
/___________________\
/                     \
/    UNIT TESTS       \
-----------------------
```

* **基础 (单元测试):** 大量的、快速的单元测试，用于验证单个组件和方法的正确性。
* **中间 (集成测试):** 少量的、覆盖核心流程的集成测试，用于验证服务内部模块之间以及与数据库、缓存的交互。
* **顶层 (端到端测试):** 在MVP阶段，我们将使用一份**严格的手动QA测试清单**来替代自动化的E2E测试，以平衡质量与速度。

### **测试组织 (Test Organization)**

* **前端测试 (Frontend Tests):**
  * **位置:** 测试代码将与业务代码并置（co-located），遵循 `*.test.tsx` 的命名约定。
  * **内容:** 测试将专注于组件的渲染、用户交互（如点击）和状态变化。
  * **工具:** Jest, React Native Testing Library。
* **后端测试 (Backend Tests):**
  * **位置:** 测试代码将遵循标准的Maven/Gradle目录结构，位于 `src/test/java` 下。
  * **内容:** 单元测试将使用Mockito来模拟外部依赖。集成测试将使用 **Testcontainers** 库来启动真实的PostgreSQL和Redis实例，以验证完整的服务逻辑。
* **端到端测试 (E2E Tests):**
  * **策略:** 根据我们之前的决策，自动化的端到端（E2E）测试将被 **暂缓** 。其职责将由QA团队维护的、一份**详尽的、覆盖核心用户流程的手动测试清单**来承担。


## **15. 编码规范 (Coding Standards)**

**本节旨在定义一套**

**最简化、但最关键**的编码规范。我们的目标不是创建一本厚厚的、无人阅读的规则手册，而是定下几条能确保代码一致性、可维护性和安全性的核心“黄金法则”。

### **关键规则 (Critical Rules)**

这些规则是强制性的，并将通过代码审查和自动化工具来保障执行。

* **1. 严格的类型共享 (Strict Type Sharing):**
  * 所有需要在前后端之间共享的数据结构（DTOs），**必须**在 `packages/shared-types` 包中以TypeScript接口的形式唯一定义。**严禁**在前端或后端应用中重复定义同一种数据结构。
* **2. 绝对的服务封装 (Absolute Service Encapsulation):**
  * 所有对外部AI大语言模型的API调用，**必须**通过我们架构中定义的 `AI服务客户端`组件进行。**严禁**在任何其他业务逻辑模块中直接发起HTTP请求来调用AI。
* **3. 不可逾越的模块边界 (Inviolable Module Boundaries):**
  * 后端模块之间**必须**通过定义好的API接口进行通信，**严禁**跨模块直接调用内部实现或共享数据库表。此规则将由我们的CI/CD流水线通过自动化工具强制执行。
* **4. 安全第一 (Security First):**
  * **严禁**在代码的任何地方（包括配置文件）硬编码任何密钥、密码或敏感配置。所有机密信息**必须**通过环境变量进行加载。

### **命名约定 (Naming Conventions)**

| 元素      | 前端 (TypeScript) | 后端 (Java)    | 数据库 (PostgreSQL) | 示例                                      |
| --------- | ----------------- | -------------- | ------------------- | ----------------------------------------- |
| 文件/类   | `PascalCase`    | `PascalCase` | N/A                 | `RecipeCard.tsx`,`RecipeService.java` |
| 变量/方法 | `camelCase`     | `camelCase`  | N/A                 | `getRecipe()`,`currentUser`           |
| 组件      | `PascalCase`    | N/A            | N/A                 | `<IngredientInput />`                   |
| API 端点  | N/A               | N/A            | `kebab-case`      | `/saved-recipes`                        |
| 表/列     | N/A               | N/A            | `snake_case`      | `user_preferences`                      |


## **16. 错误处理策略 (Error Handling Strategy)**

本节旨在为“食光机”应用定义一个统一、健壮的错误处理流程，确保无论是用户操作失误、网络问题还是服务器内部异常，我们都能以一种可预测的、用户友好的方式进行响应。

### **统一错误响应格式 (Error Response Format)**

为了实现前后端的清晰协作，所有由后端API返回的错误信息，都**必须**遵循以下标准化的JSON结构：

**JSON**

```
{
  "error": {
    "timestamp": "2025-08-09T23:31:52Z",
    "requestId": "a1b2c3d4-e5f6-4a3b-8c7d-9e8f7g6h5i4j",
    "code": "USER_ALREADY_EXISTS",
    "message": "A user with this email already exists."
  }
}
```

### **后端错误处理 (Backend Error Handling)**

* **策略:** 我们将使用Spring Boot的 `@RestControllerAdvice`和 `@ExceptionHandler`注解，来创建一个 **全局异常处理器 (Global Exception Handler)** 。
* **职责:**
  1. 捕获所有在业务逻辑中未被处理的异常（如数据库约束冲突、空指针等）。
  2. 将捕获到的异常，映射为上述定义的 **统一错误响应格式** 。
  3. 根据异常的类型，返回一个最合适的HTTP状态码（例如，`400`表示客户端请求错误，`404`表示资源未找到，`500`表示服务器内部错误）。

### **前端错误处理 (Frontend Error Handling)**

* **策略:** 前端的API客户端（Service层），将包含一个 **统一的响应拦截器** 。
* **职责:**
  1. 检查所有API调用的HTTP响应状态码。
  2. 如果状态码表示失败（例如 `4xx` 或 `5xx`），拦截器会自动解析后端返回的 **统一错误响应体** 。
  3. 根据错误体中的 `error.code`，前端可以决定向用户展示一个具体的、友好的提示信息（例如，当 `code`为 `USER_ALREADY_EXISTS`时，在注册页面提示“该邮箱已被注册”），或者一个通用的错误提示（例如“操作失败，请稍后再试”）。


## **17. 监控与可观测性 (Monitoring and Observability)**

本节旨在定义一套监控策略，让我们能主动地了解应用的运行状态、快速发现和定位问题，并衡量其性能表现。

### **监控技术栈 (Monitoring Stack)**

* **前端监控 (Frontend Monitoring):**
  * **工具:** **(建议)** **Sentry**
  * **用途:** 自动捕捉前端应用中发生的JavaScript错误和崩溃（Crashes），并提供详细的诊断报告，帮助我们快速定位和修复移动端的问题。
* **后端监控 (Backend Monitoring):**
  * **工具:** **阿里云 CloudMonitor**
  * **用途:** 使用我们云平台原生的监控服务，来监控后端应用（ACK中的容器）和基础设施（RDS, Redis等）的健康状况，包括CPU、内存、网络等指标。
* **错误追踪 (Error Tracking):**
  * **工具:** **Sentry**
  * **用途:** Sentry可以同时集成到前端和后端，为我们提供一个 **统一的错误追踪平台** ，方便我们关联一次用户操作所引发的前后端完整错误链路。
* **性能监控 (Performance Monitoring):**
  * **前端:** Sentry Performance Monitoring，用于追踪移动端的应用启动时间、交互响应速度等。
  * **后端:** 阿里云 CloudMonitor (APM)，用于追踪API的响应延迟、吞吐量等。

### **核心监控指标 (Key Metrics)**

我们将关注以下几类核心指标：

* **前端指标:**
  * **应用启动时间:** 衡量用户打开应用到可交互状态所需的时间。
  * **无崩溃率 (Crash-Free Rate):** 衡量应用的稳定性。
  * **API响应延迟 (客户端视角):** 从移动端测量调用后端API的往返时间。
* **后端指标 (The Four Golden Signals):**
  * **延迟 (Latency):** API响应时间的P95和P99分位数。
  * **流量 (Traffic):** API的请求速率（每分钟请求数）。
  * **错误 (Errors):** API返回 `5xx`服务端错误的百分比。
  * **饱和度 (Saturation):** 我们后端服务容器的CPU和内存使用率。
