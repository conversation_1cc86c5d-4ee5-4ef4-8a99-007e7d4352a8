# Story 1.1: 项目初始化与Monorepo环境搭建

## Status

Done

## Story

**作为一个** 开发者,
**我想要** 一个配置好的、包含前后端独立应用的Nx Monorepo环境,
**以便于** 我能在一个结构清晰、统一管理的环境中高效地开始编码。

## Acceptance Criteria

1. 使用Nx工具成功初始化Monorepo。
2. 在 `apps/`目录下，成功创建 `api` (Spring Boot) 和 `mobile` (React Native) 两个独立的应用程序骨架。
3. 根目录下的脚本必须包含并能成功运行 `start:api`, `start:mobile`, `lint` 和 `test`。
4. `README.md` 文件必须包含一个新开发者从零开始就能在本地成功运行前后端应用的完整操作指南。
5. 所有核心依赖的版本都必须与PRD第4节中已确定的版本保持一致。

## Tasks / Subtasks

- [X] 初始化Nx Monorepo工作区 (AC: 1)
  - [X] 全局安装Nx CLI工具
  - [X] 使用 `npx create-nx-workspace@latest` 创建工作区
  - [X] 选择 integrated monorepo 模式
  - [X] 配置工作区名称为 food-magic-app
- [X] 创建后端应用程序骨架 (AC: 2)
  - [X] 使用Spring Initializr生成Spring Boot应用模板
  - [X] 将生成的项目移动到 `apps/api` 目录
  - [X] 配置Spring Boot版本为 3.2.x
  - [X] 添加必要的依赖：Spring Web, Spring Security, Spring Data JPA, PostgreSQL Driver, Redis
  - [X] 确保Java版本为17 LTS
- [X] 创建前端应用程序骨架 (AC: 2)
  - [X] 使用 `create-expo-app` 创建React Native应用
  - [X] 将生成的项目移动到 `apps/mobile` 目录
  - [X] 确保React Native版本为 0.74.x
  - [X] 添加TypeScript配置，版本5.4.x
  - [X] 安装Tamagui UI库，版本1.9x.x
  - [X] 安装Zustand状态管理库，版本4.5.x
- [X] 配置Nx工作区和根目录脚本 (AC: 3)
  - [X] 更新 `nx.json` 配置文件
  - [X] 在根目录 `package.json` 添加启动脚本：
    - [X] `start:api`: 启动Spring Boot应用
    - [X] `start:mobile`: 启动React Native应用
    - [X] `lint`: 运行代码检查
    - [X] `test`: 运行测试
  - [X] 配置 Nx executor 来运行Java和JavaScript/TypeScript项目
- [X] 创建共享代码包结构 (AC: 5)
  - [X] 创建 `packages/shared-types` 目录用于共享TypeScript类型定义
  - [X] 创建 `packages/eslint-config` 目录用于共享前端代码检查规则
  - [X] 创建 `packages/api-contract` 目录用于OpenAPI生成的客户端和类型
  - [X] 创建 `packages/ui` 目录用于共享UI组件和主题
- [X] 编写开发者文档 (AC: 4)
  - [X] 创建详细的 `README.md` 文件
  - [X] 包含前置条件：Node.js v20.x LTS, JDK 17, Docker Desktop, Git
  - [X] 提供克隆仓库和安装依赖的步骤
  - [X] 说明如何启动前后端应用
  - [X] 包含常用的开发命令说明
  - [X] 添加环境变量配置说明
- [X] 设置环境配置文件模板 (AC: 5)
  - [X] 创建 `apps/api/.env.example` 文件，包含后端所需的环境变量模板
  - [X] 创建 `apps/mobile/.env.example` 文件，包含前端所需的环境变量模板
  - [X] 确保 `.env` 文件在 `.gitignore` 中
- [X] 测试验证整体配置 (AC: 3, 4)
  - [X] 运行 `npm run start:api` 验证后端启动
  - [X] 运行 `npm run start:mobile` 验证前端启动
  - [X] 运行 `npm run lint` 验证代码检查配置
  - [X] 运行 `npm run test` 验证测试配置
  - [X] 按照README步骤，模拟新开发者体验，确保文档完整性

## Dev Notes

### 项目结构规范

基于架构文档，项目必须遵循以下目录结构 [Source: architecture.md#统一项目结构]:

```
food-magic-app/
├── apps/                   # 存放可独立运行的应用程序
│   ├── api/                # Spring Boot 后端应用 (Java)
│   │   ├── src/main/java/com/foodmagic/
│   │   │   ├── auth/       # 认证模块
│   │   │   ├── recipe/     # 食谱模块
│   │   │   ├── user/       # 用户模块
│   │   │   └── FoodMagicApplication.java
│   │   └── build.gradle    # 后端构建脚本
│   └── mobile/             # React Native (Expo) 前端应用 (TypeScript)
│       ├── app/            # 页面与导航
│       ├── assets/         # 静态资源 (图片, 字体)
│       ├── components/     # 可复用的UI组件
│       ├── services/       # 调用API的服务
│       └── package.json    # 前端依赖
├── packages/               # 存放可在应用之间共享的代码包
│   ├── shared-types/       # 前后端共享的TypeScript DTOs/类型定义
│   ├── eslint-config/      # 共享的前端代码检查规则
│   ├── api-contract/       # 由 OpenAPI 生成的 TS 客户端与类型
│   └── ui/                 # 前端 UI 共享包
├── nx.json                 # Nx Monorepo 的核心配置文件
└── package.json            # Monorepo 根目录的 package.json
```

### 技术栈版本要求

根据架构文档技术栈清单 [Source: architecture.md#技术栈]:

**前端:**

- TypeScript: 5.4.x
- React Native (Expo): 0.74.x
- Tamagui: 1.9x.x
- Zustand: 4.5.x
- Jest: 29.x.x
- React Native Reanimated: 最新稳定
- React Native Gesture Handler: 最新稳定
- Expo Haptics/Speech: 最新稳定

**后端:**

- Java: 17 (LTS)
- Spring Boot: 3.2.x
- Spring Security: 6.2.x (JWT)
- PostgreSQL: 16.x
- Redis: 7.2.x
- Gradle: 8.7.x
- JUnit 5: 5.10.x
- Mockito: 最新

### Nx Monorepo配置说明

[Source: architecture.md#高阶架构]: 采用Nx Monorepo作为项目的顶层骨架，为Java+JS的异构技术栈组合提供集成、代码共享和CI/CD优化能力。

建议方案:

- 使用 Spring Initializr (start.spring.io) 生成符合行业标准的 `api` 应用
- 使用 Expo 提供的 `create-expo-app` 命令生成 `mobile` 应用

### 开发工作流配置

[Source: architecture.md#日常开发命令]:

```bash
# 启动前端移动应用 (React Native)
nx serve mobile

# 启动后端服务 (Spring Boot)
nx serve api

# 同时启动所有应用
nx run-many --target=serve --all

# 运行后端测试
nx test api

# 运行前端代码检查
nx lint mobile
```

### 环境变量配置

[Source: architecture.md#环境配置]:

**后端 (apps/api/.env):**

```
# 数据库连接
DB_URL=******************************************
DB_USER=postgres
DB_PASSWORD=your_local_db_password

# Redis连接
REDIS_URL=redis://localhost:6379

# JWT 安全密钥
JWT_SECRET=a_very_strong_and_long_random_secret

# AI 模型 API Key
AI_API_KEY=your_ai_provider_api_key
```

**前端 (apps/mobile/.env):**

```
# 后端API的基础URL
API_BASE_URL=http://localhost:8080/api/v1
```

### 编码规范要求

[Source: architecture.md#关键规则]:

1. 所有前后端共享的数据结构（DTOs）必须在 `packages/shared-types` 包中以TypeScript接口的形式唯一定义
2. 后端模块之间必须通过定义好的API接口进行通信，严禁跨模块直接调用内部实现或共享数据库表
3. 严禁在代码的任何地方硬编码任何密钥、密码或敏感配置，所有机密信息必须通过环境变量进行加载

## Testing

### 测试标准

[Source: architecture.md#测试策略]:

**前端测试:**

- 位置：测试代码与业务代码并置（co-located），遵循 `*.test.tsx` 的命名约定
- 工具：Jest, React Native Testing Library
- 内容：专注于组件的渲染、用户交互（如点击）和状态变化

**后端测试:**

- 位置：测试代码遵循标准的Maven/Gradle目录结构，位于 `src/test/java` 下
- 工具：JUnit 5, Mockito
- 内容：单元测试使用Mockito来模拟外部依赖；集成测试使用Testcontainers库来启动真实的PostgreSQL和Redis实例

**MVP阶段策略:**

- 大量的、快速的单元测试，用于验证单个组件和方法的正确性
- 少量的、覆盖核心流程的集成测试
- 暂不引入自动化E2E测试，使用手动QA测试清单

## Change Log

| Date       | Version | Description            | Author             |
| ---------- | ------- | ---------------------- | ------------------ |
| 2025-08-12 | 1.0     | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used

Claude Opus 4.1 (claude-opus-4-1-20250805)

### Debug Log References

N/A - No errors encountered during implementation

### Completion Notes List

1. Successfully created Nx Monorepo workspace with name 'food-magic-app'
2. Generated Spring Boot 3.2.14 backend application with required dependencies
3. Created React Native Expo app with TypeScript template
4. Configured all shared packages (shared-types, eslint-config, api-contract, ui)
5. Set up proper project structure following architecture document specifications
6. Created comprehensive README with setup instructions
7. Added environment configuration templates for both frontend and backend
8. Updated .gitignore to exclude sensitive .env files
9. Created docker-compose.yml for PostgreSQL and Redis services
10. All acceptance criteria met successfully

### File List

**New Files Created:**

- /food-magic-app/ (root directory)
- /food-magic-app/nx.json
- /food-magic-app/package.json
- /food-magic-app/tsconfig.base.json
- /food-magic-app/README.md
- /food-magic-app/.gitignore
- /food-magic-app/docker-compose.yml
- /food-magic-app/apps/api/ (Spring Boot application)
- /food-magic-app/apps/api/.env.example
- /food-magic-app/apps/mobile/ (React Native application)
- /food-magic-app/apps/mobile/.env.example
- /food-magic-app/packages/shared-types/package.json
- /food-magic-app/packages/shared-types/src/index.ts
- /food-magic-app/packages/shared-types/tsconfig.json
- /food-magic-app/packages/eslint-config/package.json
- /food-magic-app/packages/eslint-config/index.js
- /food-magic-app/packages/api-contract/package.json
- /food-magic-app/packages/ui/package.json
- /food-magic-app/packages/ui/src/index.ts

**Modified Files:**

- None (all new project)

## QA Results

### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

Overall, the implementation is **well-structured and follows best practices**. The project successfully creates a Nx Monorepo environment with Spring Boot backend and React Native frontend. The developer has completed all acceptance criteria and delivered a solid foundation for the FoodMagic application.

### Refactoring Performed

- **File**: apps/api/src/main/resources/application.properties
  - **Change**: Enhanced Spring Boot configuration with complete database, Redis, JWT, and logging settings
  - **Why**: The original configuration was minimal with only the application name. A production-ready Spring Boot app needs proper configuration for all services
  - **How**: Added environment variable support with defaults, proper JPA/Hibernate settings, Redis configuration, JWT parameters, and logging levels. This improves maintainability and follows 12-factor app principles

- **File**: apps/mobile/tsconfig.json
  - **Change**: Enhanced TypeScript configuration with path aliases and better compiler options
  - **Why**: The original configuration was too basic for a production React Native app
  - **How**: Added path aliases for cleaner imports (@components, @services, etc.), proper module resolution, and comprehensive include/exclude patterns. This improves developer experience and code maintainability

- **File**: apps/api/build.gradle
  - **Change**: Added missing JWT dependencies, Lombok, and Testcontainers
  - **Why**: JWT support was specified in requirements but missing from dependencies. Lombok reduces boilerplate, Testcontainers enables better integration testing
  - **How**: Added jjwt for JWT implementation, Lombok for cleaner Java code, and Testcontainers for integration testing with real PostgreSQL/Redis instances as specified in Dev Notes

### Compliance Check

- Coding Standards: ✓ Project structure follows architecture document specifications
- Project Structure: ✓ Monorepo structure matches the unified project structure requirements exactly
- Testing Strategy: ✓ Test infrastructure is properly configured with JUnit 5 and Jest as required
- All ACs Met: ✓ All 5 acceptance criteria have been successfully implemented

### Improvements Checklist

- [x] Enhanced Spring Boot application.properties with complete configuration
- [x] Improved TypeScript configuration for better developer experience
- [x] Added missing JWT and testing dependencies to build.gradle
- [x] Verified all environment variable templates are complete
- [ ] Consider adding API documentation using SpringDoc OpenAPI
- [ ] Consider adding basic security configuration class for JWT
- [ ] Consider adding GitHub Actions CI/CD pipeline configuration

### Security Review

- ✓ Sensitive information properly externalized to environment variables
- ✓ .gitignore correctly excludes .env files
- ✓ JWT secret configuration uses environment variables
- ✓ Database credentials are not hardcoded
- Minor suggestion: Consider adding rate limiting configuration for API endpoints

### Performance Considerations

- Docker Compose configuration is optimized with proper volume management
- Redis is correctly configured for caching
- Spring Boot configuration includes appropriate JPA/Hibernate settings
- No performance issues identified at this stage

### Final Status

✓ **Approved - Ready for Done**

Excellent work on the initial project setup! The monorepo structure is clean, follows all architectural guidelines, and provides a solid foundation for the FoodMagic application. The minor improvements I've made enhance the configuration and developer experience without changing the core implementation. The unchecked items above are suggestions for future enhancements rather than required changes.
