# Story 2.1: 基础个性化功能的后端支持

## Status

Done

## Story

**作为一个** 系统,
**我需要** 提供API来管理用户的核心饮食偏好（如过敏原），并将这些偏好整合进AI指令中,
**以便于** 我能为有特殊饮食需求的用户生成更安全、更相关的食谱。

## Acceptance Criteria

1. 在数据库中创建 `user_preferences`表，并与 `users`表正确关联。
2. 后端提供API端点（例如 `/api/users/me/preferences`），允许获取和更新用户的偏好设置。
3. 核心食谱生成逻辑在构建AI指令(Prompt)时，会获取并包含用户的饮食偏好（例如，明确指示AI"不要包含以下成分：花生、海鲜"）。
4. 后端保存用户偏好时，应使用预定义的键值对格式（例如 `allergy_peanuts: true`, `diet_vegetarian: true`），以确保数据结构的一致性。

## Tasks / Subtasks

- [x] 创建数据库迁移脚本 (AC: 1)
  - [x] 创建 `user_preferences` 表的migration文件
  - [x] 定义表结构：user_id (UUID主键+外键), preferences_json (JSONB), updated_at (TIMESTAMPTZ)
  - [x] 添加级联删除约束 (ON DELETE CASCADE)
  - [x] 执行数据库迁移并验证表创建成功

- [x] 创建用户偏好数据模型和DTO (AC: 2, 4)
  - [x] 在 `apps/api/src/main/java/com/foodmagic/user/entity/` 创建 `UserPreference.java` 实体类
  - [x] 在 `apps/api/src/main/java/com/foodmagic/user/dto/` 创建 `UserPreferencesDto.java`
  - [x] 在 `packages/shared-types/src/dto/` 创建 `user-preferences.dto.ts` TypeScript接口
  - [x] 定义预定义的偏好键值对结构（allergies, diet, preferredCuisines等）

- [x] 实现用户偏好Repository层 (AC: 2)
  - [x] 在 `apps/api/src/main/java/com/foodmagic/user/repository/` 创建 `UserPreferenceRepository.java`
  - [x] 实现基于Spring Data JPA的CRUD操作
  - [x] 添加自定义查询方法 findByUserId

- [x] 实现用户偏好Service层 (AC: 2, 4)
  - [x] 在 `apps/api/src/main/java/com/foodmagic/user/service/` 创建 `UserPreferenceService.java`
  - [x] 实现获取用户偏好逻辑（如不存在则返回默认值）
  - [x] 实现更新用户偏好逻辑（upsert操作）
  - [x] 实现偏好数据验证和标准化

- [x] 创建用户偏好API端点 (AC: 2)
  - [x] 在 `apps/api/src/main/java/com/foodmagic/user/controller/` 创建或更新 `UserController.java`
  - [x] 实现 `GET /api/v1/users/me/preferences` 端点
  - [x] 实现 `PUT /api/v1/users/me/preferences` 端点
  - [x] 添加OpenAPI文档注解
  - [x] 实现JWT认证保护

- [x] 更新AI服务集成用户偏好 (AC: 3)
  - [x] 修改 `apps/api/src/main/java/com/foodmagic/ai/client/PromptBuilder.java`
  - [x] 添加方法接收用户偏好参数
  - [x] 实现偏好到AI指令的转换逻辑（过敏原→"不要包含", 饮食类型→"请确保食谱符合"）
  - [x] 更新生成prompt时包含用户偏好约束

- [x] 更新食谱生成API以使用用户偏好 (AC: 3)
  - [x] 修改 `apps/api/src/main/java/com/foodmagic/recipe/controller/` 中的食谱生成端点
  - [x] 在生成食谱前获取当前用户的偏好设置
  - [x] 将用户偏好传递给AI服务客户端
  - [x] 确保游客用户也能正常使用（使用默认偏好）

- [x] 编写单元测试 (AC: 全部)
  - [x] 为UserPreferenceService编写单元测试
  - [x] 为UserPreferenceController编写单元测试
  - [x] 为PromptBuilder的偏好集成编写单元测试
  - [x] 使用Mockito模拟依赖

- [x] 编写集成测试 (AC: 全部)
  - [x] 创建用户偏好API的集成测试
  - [x] 测试偏好的CRUD操作
  - [x] 测试偏好在食谱生成中的应用
  - [x] 使用TestContainers运行真实数据库

## Dev Notes

### Previous Story Insights
从Story 1.4学到的关键经验：
- AI服务客户端已经实现了完整的错误处理和重试机制，使用Resilience4j断路器模式
- PromptBuilder类已存在于 `apps/api/src/main/java/com/foodmagic/ai/client/PromptBuilder.java`
- 环境变量配置模式已建立，使用Spring Boot的配置属性
- JWT认证已在 `JwtUtil` 中实现，使用0.12.x API版本

### Data Models
**用户偏好数据模型** [Source: architecture/4-数据模型-data-models.md#模型4]
- 表名：`user_preferences`
- 与users表一对一关系
- 存储格式：JSONB，支持灵活的偏好扩展
- 预定义偏好结构：
  ```json
  {
    "allergies": ["peanuts", "shellfish"],
    "diet": "LOW_CARB",
    "preferred_cuisines": ["ITALIAN", "MEXICAN"],
    "unit_system": "METRIC",
    "locale": "zh-CN"
  }
  ```

**TypeScript DTO定义** [Source: architecture/4-数据模型-data-models.md#L126-135]
```typescript
export interface UserPreferencesDto {
  allergies?: string[];
  isVegetarian?: boolean;
  diet?: 'LOW_CARB' | 'HIGH_PROTEIN' | 'KETO';
  preferredCuisines?: string[];
  unitSystem?: 'METRIC' | 'IMPERIAL';
  locale?: string;
}
```

### API Specifications
**用户偏好端点** [Source: architecture/5-api-规范-api-specification.md#L45-55]
- `GET /api/v1/users/me/preferences` - 获取当前用户偏好
- `PUT /api/v1/users/me/preferences` - 更新用户偏好
- 两个端点都需要Bearer认证
- 返回/接收 UserPreferencesDto

**食谱生成端点更新** [Source: architecture/5-api-规范-api-specification.md#L58-86]
- `/api/v1/recipes/generate` 已包含preferences字段
- 请求体中preferences使用UserPreferencesDto类型
- 支持locale和unitSystem参数

### Database Schema
**user_preferences表结构** [Source: architecture/9-数据库模式-database-schema.md#L59-65]
```sql
CREATE TABLE user_preferences (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    preferences_json JSONB,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### File Locations
基于项目结构 [Source: architecture/10-统一项目结构-unified-project-structure.md]：
- 后端代码位置：`apps/api/src/main/java/com/foodmagic/`
  - user模块：`user/` 目录（包含entity, dto, repository, service, controller）
  - AI集成：`ai/` 目录（已存在）
- 共享类型：`packages/shared-types/src/dto/`
- 测试代码：`apps/api/src/test/java/com/foodmagic/`

### Technical Constraints
- Java版本：17 LTS [Source: architecture/3-技术栈-tech-stack.md#L11]
- Spring Boot版本：3.2.x [Source: architecture/3-技术栈-tech-stack.md#L12]
- PostgreSQL版本：16.x [Source: architecture/3-技术栈-tech-stack.md#L14]
- 使用Spring Security JWT进行认证 [Source: architecture/3-技术栈-tech-stack.md#L17]

### Testing Requirements
[Source: architecture/14-测试策略-testing-strategy.md]

**后端测试位置**: `src/test/java` 目录下，遵循标准Maven/Gradle结构

**测试工具**:
- 单元测试：JUnit 5, Mockito
- 集成测试：TestContainers（启动真实PostgreSQL实例）

**测试覆盖要求**:
- 所有Service层的公共方法
- Controller端点的正常和异常情况
- 偏好数据的CRUD操作
- AI prompt集成用户偏好的逻辑

## Change Log

| Date       | Version | Description                   | Author             |
| ---------- | ------- | ----------------------------- | ------------------ |
| 2025-01-14 | 1.0     | Initial story creation        | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used
claude-3-5-sonnet-20241022

### Debug Log References
- Task 1: Database migration scripts created successfully
- Task 2: Data models and DTOs created successfully  
- Task 3: Repository layer implemented successfully
- Task 4: Service layer with validation implemented successfully
- Task 5: API endpoints with JWT protection created successfully
- Task 6: AI service integration with PromptBuilder completed successfully
- Task 7: Recipe generation API updated with preferences integration successfully
- Task 8: Unit tests implemented with Mockito successfully
- Task 9: Integration tests implemented with TestContainers successfully

### Completion Notes List
- Created V1__Create_users_table.sql for users table (prerequisite)
- Created V2__Create_user_preferences_table.sql with proper foreign key constraints
- Added indexes for performance optimization
- Included auto-update triggers for updated_at timestamps
- Created UserPreference and User entities with JPA annotations
- Created UserPreferencesDto with comprehensive preference fields
- Created TypeScript interface with type safety and validation helpers
- Implemented UserPreferenceRepository with custom queries
- Created UserPreferenceService with upsert logic and validation
- Added UserPreferenceMapper for JSON to DTO conversion
- Created UserController with JWT-protected endpoints
- Implemented PromptBuilder with comprehensive preference handling
- Created RecipeGenerationService integrating user preferences
- Added RecipeController supporting both authenticated and guest users
- Implemented comprehensive unit tests for service, controller, and PromptBuilder
- Created integration tests with TestContainers for end-to-end validation

### File List
- apps/api/src/main/resources/db/migration/V1__Create_users_table.sql (created)
- apps/api/src/main/resources/db/migration/V2__Create_user_preferences_table.sql (created)
- apps/api/src/main/java/com/foodmagic/user/entity/UserPreference.java (created)
- apps/api/src/main/java/com/foodmagic/user/entity/User.java (created)
- apps/api/src/main/java/com/foodmagic/user/dto/UserPreferencesDto.java (created)
- apps/api/src/main/java/com/foodmagic/user/repository/UserPreferenceRepository.java (created)
- apps/api/src/main/java/com/foodmagic/user/repository/UserRepository.java (created)
- apps/api/src/main/java/com/foodmagic/user/service/UserPreferenceService.java (created)
- apps/api/src/main/java/com/foodmagic/user/mapper/UserPreferenceMapper.java (created)
- apps/api/src/main/java/com/foodmagic/user/controller/UserController.java (created)
- apps/api/src/main/java/com/foodmagic/common/security/SecurityUtils.java (created)
- apps/api/src/main/java/com/foodmagic/ai/client/PromptBuilder.java (created)
- apps/api/src/main/java/com/foodmagic/recipe/dto/RecipeGenerationRequestDto.java (created)
- apps/api/src/main/java/com/foodmagic/recipe/dto/RecipeResponseDto.java (created)
- apps/api/src/main/java/com/foodmagic/recipe/service/RecipeGenerationService.java (created)
- apps/api/src/main/java/com/foodmagic/recipe/controller/RecipeController.java (created)
- apps/api/src/test/java/com/foodmagic/user/service/UserPreferenceServiceTest.java (created)
- apps/api/src/test/java/com/foodmagic/user/controller/UserControllerTest.java (created)
- apps/api/src/test/java/com/foodmagic/ai/client/PromptBuilderTest.java (created)
- apps/api/src/test/java/com/foodmagic/integration/UserPreferenceIntegrationTest.java (created)
- apps/api/src/test/java/com/foodmagic/integration/RecipeGenerationIntegrationTest.java (created)
- packages/shared-types/src/dto/user-preferences.dto.ts (created)
- packages/shared-types/src/dto/index.ts (created)
- packages/shared-types/src/index.ts (created)

## QA Results

### Review Date: 2025-01-14

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

Overall, the implementation is **well-structured and comprehensive**. The developer has successfully implemented all acceptance criteria with proper attention to separation of concerns, error handling, and test coverage. The code follows Java Spring Boot best practices and includes comprehensive OpenAPI documentation. The integration with the AI service through the PromptBuilder is particularly well-designed with appropriate safety constraints for allergies.

### Refactoring Performed

- **File**: UserPreferenceService.java
  - **Change**: Replaced string comparison chains with enum-based validation
  - **Why**: Improved maintainability, type safety, and performance (O(1) lookups)
  - **How**: Created DietType and SpiceLevel enums to centralize validation logic and make the code more maintainable

- **File**: DietType.java (new)
  - **Change**: Created enum for diet type validation
  - **Why**: Centralized validation logic, improved type safety
  - **How**: Provides a single source of truth for diet types with validation helper method

- **File**: SpiceLevel.java (new)
  - **Change**: Created enum for spice level validation
  - **Why**: Centralized validation logic, improved type safety
  - **How**: Provides a single source of truth for spice levels with validation helper method

- **File**: UserController.java
  - **Change**: Enhanced error response handling with meaningful error messages
  - **Why**: Better client-side error handling and debugging
  - **How**: Added error response body with message and timestamp for 400 responses

### Compliance Check

- Coding Standards: ✓ Code follows Java conventions, proper documentation, and Spring Boot patterns
- Project Structure: ✓ Files correctly placed according to domain-driven design structure
- Testing Strategy: ✓ Comprehensive unit tests with Mockito, edge cases covered
- All ACs Met: ✓ All acceptance criteria fully implemented and functional

### Improvements Checklist

[x] Refactored validation logic to use enums for better maintainability (UserPreferenceService.java)
[x] Added error response body for better client error handling (UserController.java)
[x] Created enum constants for type-safe validation (DietType.java, SpiceLevel.java)
[ ] Consider adding request/response logging interceptor for debugging
[ ] Consider implementing caching for frequently accessed preferences
[ ] Add metrics/monitoring for preference update frequency

### Security Review

- **Good**: Proper JWT authentication on all endpoints
- **Good**: Input validation with size limits on arrays to prevent abuse
- **Good**: SQL injection prevention through JPA parameterized queries
- **Good**: Critical safety handling for allergies in PromptBuilder
- **Minor Suggestion**: Consider rate limiting on preference update endpoint

### Performance Considerations

- **Good**: Lazy loading of User entity in UserPreference
- **Good**: Indexed columns for efficient queries
- **Good**: Efficient upsert logic in service layer
- **Suggestion**: The preferences JSON column could benefit from GIN indexing for complex queries if needed in future
- **Suggestion**: Consider implementing caching for user preferences as they don't change frequently

### Final Status

✓ **Approved - Ready for Done**

Excellent implementation with all requirements met. The refactoring performed improves code maintainability without changing functionality. The minor suggestions for caching and monitoring can be addressed in future optimization stories.