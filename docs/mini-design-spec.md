# 冒烟测试功能 - 迷你设计规范

> **Generated by**: UX Expert (Sally)  
> **Date**: 2025-01-13  
> **Version**: 1.0  
> **Purpose**: Story 1.4 Acceptance Criteria #1 - 前置条件设计规范

---

## 🎯 Executive Summary

基于Story 1.4的需求，这个冒烟测试功能将提供一个极简的端到端验证界面，包含一个食材输入框和提交按钮。用户可以输入预设食材（如"番茄"），点击按钮后调用AI服务，并显示完整的AI原始响应文本。这个功能的核心目标是验证整个技术栈的连通性，同时建立产品的基础设计基调。

---

## 📋 Feature Overview

### 主要用户旅程:
1. 用户打开冒烟测试界面
2. 在输入框中输入食材（如"番茄"） 
3. 点击"获取AI推荐"按钮
4. 看到加载状态指示器
5. 查看AI返回的完整原始文本响应

### 关键交互和预期行为:
- **输入验证**: 限制输入长度，提供实时反馈
- **状态管理**: 清晰的loading、success、error状态
- **响应显示**: 可滚动的文本区域显示AI原始响应
- **错误处理**: 网络错误、API错误的友好提示

### 成功指标:
- 端到端数据流验证（前端→后端→AI→返回显示）
- 用户能成功输入并获得AI响应
- 界面响应式且可用性良好

### 后端集成点:
- POST `/api/v1/smoke-test/generate` 端点
- 处理请求/响应DTO结构
- 错误状态码处理

### 利益相关者洞察:

**产品团队视角:** "我们需要看到一个食材输入进去，AI的原始响应能完整显示出来，证明所有环节都通了"

**开发团队视角:** "界面要简单，但状态管理要清晰。Loading、成功、失败状态都要考虑到"

**UX设计原则:**
1. **简洁至上** - 界面元素最小化，但每个元素都有明确目的
2. **状态透明** - 用户始终知道系统在做什么
3. **错误友好** - 出错时给出清晰指导
4. **品牌一致** - 体现"食物魔法"的产品调性

---

## 🧩 UI Components & Layout

### 布局结构:
- **主容器**: 全屏垂直布局，居中对齐
- **标题区域**: 功能名称和简短说明
- **输入区域**: 食材输入框 + 标签
- **操作区域**: 提交按钮
- **结果区域**: AI响应显示区（可滚动）
- **状态区域**: 加载指示器和错误提示

### 输入组件:
- **文本输入框**: 单行输入，placeholder "请输入食材，如：番茄"
- **主要操作按钮**: "获取AI推荐" - 使用品牌主色
- **输入验证**: 实时字符计数，最大长度限制

### 显示组件:
- **标题文本**: 层次清晰的标题组合
- **响应文本卡片**: 圆角卡片容器，内含可滚动文本区
- **状态指示器**: 加载动画 + 文本提示

### 导航元素:
- **返回按钮**: 简单的后退导航（如需要）
- **清除按钮**: 快速清空输入和结果

### 交互元素状态:
- **输入框状态**: 默认、聚焦、错误、禁用
- **按钮状态**: 默认、悬停、按下、禁用、加载中
- **结果区状态**: 空状态、加载中、成功、错误

---

## 🎨 Design System & Styling

### 颜色调色板:
- **主色 (Primary)**: #2D5C3B - 深绿色，体现食材的自然感
- **次色 (Secondary)**: #F4A460 - 温暖的橙色，象征食物的丰富
- **强调色 (Accent)**: #FF6B6B - 珊瑚红，用于错误状态和重要提示
- **中性色系**:
  - 深灰 #2C3E50 - 主要文本
  - 中灰 #7F8C8D - 次要文本
  - 浅灰 #ECF0F1 - 背景和分割线
  - 纯白 #FFFFFF - 卡片和输入框背景

### 字体系统:
- **主标题**: 24pt/30pt，Weight: 600 (Semibold)
- **副标题**: 18pt/24pt，Weight: 500 (Medium)  
- **正文**: 16pt/22pt，Weight: 400 (Regular)
- **辅助文本**: 14pt/20pt，Weight: 400 (Regular)
- **按钮文本**: 16pt/20pt，Weight: 500 (Medium)
- **字体族**: 系统默认 (iOS: SF Pro, Android: Roboto)

### 间距系统 (8pt网格):
- **XS**: 4pt - 细微间距
- **S**: 8pt - 组件内部间距  
- **M**: 16pt - 相关元素间距
- **L**: 24pt - 不同区块间距
- **XL**: 32pt - 主要区域间距
- **XXL**: 48pt - 页面级间距

### 组件样式模式:
- **输入框**: 圆角12pt，边框1pt #E1E8ED，聚焦时边框变为主色
- **按钮**: 圆角8pt，最小高度48pt，水平内边距24pt
- **卡片**: 圆角16pt，阴影subtle (0 2pt 8pt rgba(0,0,0,0.1))
- **状态指示**: 圆形加载器，直径24pt，主色动画

### 响应式设计方法:
- **移动优先**: 基础设计针对320-414pt宽度
- **断点**: 手机(<768pt) | 平板(768-1024pt) | 桌面(>1024pt)
- **自适应**: 内容区域最大宽度480pt，超出则居中显示

### 无障碍考量:
- **对比度**: 所有文本满足WCAG AA标准(4.5:1)
- **触摸目标**: 最小44x44pt
- **焦点指示**: 明显的焦点环，2pt边框，对比色
- **语义化**: 适当的accessibility labels和hints

---

## ⚡ Interaction Design

### 触摸/点击交互:
- **输入框点击**: 软键盘弹起，输入框获得焦点，边框颜色变化
- **按钮点击**: 轻微缩放动画(0.95x)，触觉反馈(轻触震动)
- **结果区域**: 支持滚动浏览，惯性滚动效果
- **清除操作**: 双击输入框快速清空，或使用专用清除按钮

### 加载状态和动画:
- **提交加载**: 按钮文字变为"获取中..."，显示旋转加载器
- **按钮禁用**: 透明度降至0.6，防止重复提交
- **整体加载**: 结果区域显示骨架屏或加载动画
- **加载时长**: 显示预估等待时间"通常需要3-5秒"

### 错误状态和反馈:
- **输入验证错误**: 
  - 输入框边框变红色
  - 底部显示错误文案"请输入有效的食材名称"
  - 轻微震动反馈
- **网络/API错误**:
  - 结果区显示友好错误提示
  - 提供"重试"按钮
  - 错误图标 + 说明文字

### 成功状态和确认:
- **输入成功**: 输入框边框短暂变绿，确认接受输入
- **API成功**: 
  - 结果区淡入动画显示内容
  - 简短的成功提示"获取成功！"
  - 可选的成功音效或触觉反馈

### 微交互和转场:
- **页面进入**: 从底部滑入动画(300ms ease-out)
- **内容展现**: 错开的淡入动画，自上而下(100ms间隔)
- **状态切换**: 平滑的透明度和高度变化(200ms)
- **焦点移动**: 软性的缩放和颜色过渡
- **键盘处理**: 内容区域自动调整避免遮挡

---

## ⚙️ Technical Implementation

### 框架和库选择:
- **前端框架**: React Native (Expo) 0.74.x - 符合项目技术栈
- **UI组件库**: Tamagui 1.9x.x - 提供一致的设计系统组件
- **状态管理**: Zustand 4.5.x - 轻量级状态管理，适合简单功能
- **网络请求**: Expo Fetch API / Axios - 处理API调用
- **表单处理**: React Hook Form - 输入验证和状态管理

### 组件架构:
```
SmokeTestScreen/
├── components/
│   ├── IngredientInput.tsx    # 食材输入组件
│   ├── SubmitButton.tsx       # 提交按钮组件  
│   ├── ResponseDisplay.tsx    # AI响应显示组件
│   └── LoadingIndicator.tsx   # 加载状态组件
├── hooks/
│   ├── useSmokeTest.ts        # 业务逻辑hook
│   └── useInputValidation.ts  # 输入验证hook
├── services/
│   └── smokeTest.service.ts   # API调用服务
└── SmokeTestScreen.tsx        # 主页面组件
```

### 状态管理结构:
```typescript
interface SmokeTestState {
  input: string
  isLoading: boolean
  response: string | null
  error: string | null
  hasSubmitted: boolean
}
```

### API集成模式:
- **RESTful调用**: POST `/api/v1/smoke-test/generate`
- **请求格式**: `{ ingredient: string }`
- **响应处理**: 直接显示原始文本，无需解析
- **错误处理**: HTTP状态码 + 业务错误码双重处理
- **超时设置**: 30秒超时，适应AI响应时间

### 性能考量:
- **组件优化**: 使用React.memo避免不必要重渲染
- **防抖输入**: 输入验证使用300ms防抖
- **图片优化**: 如有图标使用SVG格式
- **包大小**: 避免引入重型依赖，复用现有库

### 测试方法:
- **单元测试**: Jest + React Native Testing Library
- **组件测试**: 输入交互、状态变化、API调用mock
- **集成测试**: 端到端用户流程测试
- **可访问性测试**: Screen reader兼容性测试

---

## 📱 Responsive & Accessibility

### 移动优先设计考量:
- **最小屏幕支持**: 320px宽度 (iPhone SE)
- **触摸目标**: 最小44x44pt，间距至少8pt
- **单手操作**: 主要操作元素在拇指可达区域
- **键盘适配**: 软键盘弹起时自动调整布局

### 平板和桌面适配:
- **平板 (768-1024px)**: 内容区域限制最大宽度，两侧留白
- **桌面 (>1024px)**: 居中卡片布局，最大宽度600px
- **横屏模式**: 优化布局比例，避免内容拉伸

### 无障碍要求 (WCAG 2.1 AA):
- **颜色对比**: 文本对比度 ≥ 4.5:1，大文本 ≥ 3:1
- **键盘导航**: 支持Tab键顺序导航，明确焦点指示
- **Screen Reader**: 适当的accessibilityLabel和accessibilityHint
- **语义化**: 使用正确的语义化组件和角色

### Screen Reader兼容性:
- **输入框**: "食材输入框，请输入要查询的食材名称"
- **按钮**: "获取AI推荐按钮，点击提交食材查询"
- **结果区**: "AI响应结果，双击可复制内容"
- **状态提示**: 加载和错误状态的语音播报

### 键盘导航顺序:
1. 食材输入框 → 2. 提交按钮 → 3. 结果区域(如有内容)

### 触摸目标尺寸:
- **按钮**: 最小48x48pt
- **输入框**: 高度48pt，点击区域扩展
- **可交互元素**: 间距至少8pt避免误触

---

## 🧪 User Testing & Validation

### 可用性测试方法:
- **游击测试**: 5-8名用户快速测试基本流程
- **任务导向测试**: "请使用这个功能查询番茄的AI推荐"
- **思维导出**: 用户操作时说出想法和困惑
- **A/B测试**: 不同按钮文案或布局的对比测试

### 关键用户流程测试:
1. **首次使用**: 用户能否理解功能目的
2. **输入操作**: 输入框使用是否直观
3. **等待体验**: 加载状态是否清晰
4. **结果理解**: AI响应展示是否有效
5. **错误恢复**: 出错后用户能否继续使用

### 成功指标和KPIs:
- **完成率**: >90% 用户能完成基本流程
- **错误率**: <5% 用户操作错误率
- **满意度**: SUS量表得分 >70分
- **任务完成时间**: 首次使用 <2分钟
- **学习曲线**: 第二次使用时间减少50%

### 反馈收集方法:
- **应用内反馈**: 简单的点赞/点踩按钮
- **退出调研**: 使用后的简短问卷
- **用户访谈**: 深度用户体验访谈
- **数据分析**: 使用行为和错误日志分析

### 迭代和改进流程:
- **每周回顾**: 分析用户反馈和数据
- **优先级排序**: 根据影响程度和实现成本排序
- **快速迭代**: 小改动立即测试和发布
- **重大更改**: 充分测试后发布

---

## 📝 Implementation Notes

### 开发优先级和阶段:

**Phase 1 - 核心功能 (Week 1)**
- 基础UI组件实现
- API集成和状态管理
- 基本错误处理

**Phase 2 - 体验优化 (Week 2)**
- 加载动画和微交互
- 输入验证和反馈
- 响应式布局调整

**Phase 3 - 完善和测试 (Week 3)**
- 无障碍性优化
- 性能优化
- 测试和Bug修复

### 已知技术约束:
- AI API响应时间不确定，需要合理的超时设置
- 移动端软键盘处理需要细致调试
- Tamagui组件定制可能需要额外学习成本

### 依赖和前置条件:
- 后端AI服务端点已完成
- 共享类型定义已建立
- 开发环境已配置AI API密钥

### 风险区域和缓解策略:
- **AI API不稳定**: 实现重试机制和降级提示
- **移动端兼容性**: 在多种设备上充分测试
- **性能问题**: 监控渲染性能，优化动画

### 未来增强机会:
- 预设食材选择器
- 响应结果的格式化显示
- 历史查询记录
- 分享功能
- 离线缓存机制

---

## 📋 设计决策记录

### 关键设计决策:
1. **极简界面**: 符合"冒烟测试"的简洁要求，但保留了完整的用户体验考量
2. **绿色主调**: 建立"食物魔法"品牌的自然感，为后续功能奠定色彩基调
3. **原始文本显示**: 满足技术验证需求，展示完整的AI响应内容
4. **移动优先**: 确保在最小屏幕上的可用性，体现产品的移动端定位

### 设计权衡:
- **简洁 vs 完整**: 选择简洁布局，但保留必要的状态指示和错误处理
- **技术验证 vs 用户体验**: 在满足技术验证需求的同时，确保用户交互的友好性
- **现在 vs 未来**: 设计考虑了后续功能扩展的可能性，避免重大重构

### 成功标准:
✅ **验证技术栈连通性** - 前端→后端→AI→显示的完整数据流  
✅ **建立设计基调** - 色彩、字体、间距系统可复用  
✅ **移动端可用性** - 在各种设备尺寸上正常工作  
✅ **无障碍性** - 符合基本的可访问性标准

---

*本设计规范满足 Story 1.4 Acceptance Criteria 第一点的前置条件要求，为冒烟测试功能的开发实现提供完整的设计指导。*