# **5. API 规范 (API Specification)**

根据我们在“技术栈”部分确定的**REST API**风格，我将为您起草一份 **OpenAPI 3.0** 规范的骨架。这份规范将精确定义每一个API端点（Endpoint）的路径、请求方式、参数和返回的数据结构。它是前后端开发团队之间最重要的协作文档。

以下是基于我们已批准的用户故事，草拟的API规范核心部分：

**YAML**

```
openapi: 3.0.1
info:
  title: 食光机 (FoodMagic) API
  version: "1.0.0"
  description: 食光机应用的核心后端API

servers:
  - url: /api/v1
    description: API V1 版本

paths:
  # === 认证模块 ===
  /auth/register:
    post:
      summary: 用户注册
      # ... (定义请求体和响应)
  /auth/login:
    post:
      summary: 用户登录
      # ... (定义请求体和响应)

  # === 用户模块 ===
  /users/me:
    get:
      summary: 获取当前用户信息
      security:
        - bearerAuth: [] # 这是一个受保护的路由
      responses:
        '200':
          description: 成功的响应
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDto' # 引用我们之前定义的数据模型

  /users/me/preferences:
    get:
      summary: 获取用户偏好
      security:
        - bearerAuth: []
      # ...
    put:
      summary: 更新用户偏好
      security:
        - bearerAuth: []
      # ...

  # === 核心功能模块 ===
  /recipes/generate:
    post:
      summary: 根据食材生成食谱
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ingredients:
                  type: array
                  items:
                    type: string
                preferences:
                  $ref: '#/components/schemas/UserPreferencesDto'
                locale:
                  type: string
                  example: zh-CN
                unitSystem:
                  type: string
                  enum: [METRIC, IMPERIAL]
                reduceMotion:
                  type: boolean
                  description: 前端系统设置透传，便于后端选择简化返回（如不返回动效资源提示）
              required:[ingredients]     
      responses:
        '200':
          description: 成功生成的食谱
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecipeDto'

  # === 收藏夹模块 ===
    # 查询是否已收藏（轻量）
    /saved-recipes/{recipeId}:
      get:
        summary: 是否已收藏
        security: [{ bearerAuth: [] }]
        responses:
          '200': { description: 已收藏 }
          '404': { description: 未收藏 }
      head:
        summary: 是否已收藏(HEAD)
        security: [{ bearerAuth: [] }]

    # 收藏（幂等）
    /saved-recipes/{recipeId}:
      put:
        summary: 收藏（幂等）
        security: [{ bearerAuth: [] }]
        responses:
          '204': { description: 已收藏或成功收藏 }

    # 取消收藏（幂等）
      delete:
        summary: 取消收藏（幂等）
        security: [{ bearerAuth: [] }]
        responses:
          '204': { description: 已取消或本就未收藏 }
    # ...
