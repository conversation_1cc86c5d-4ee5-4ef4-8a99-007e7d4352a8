# **12. 部署架构 (Deployment Architecture)**

本节将定义我们的部署策略和CI/CD（持续集成/持续部署）流水线，确保我们能实现快速、可重复的发布。

## **部署策略 (Deployment Strategy)**

* **后端 (Spring Boot API):**
  * **打包:** 后端应用将被打包成一个 **Docker镜像** 。
  * **存储:** 生成的镜像将被推送到 **阿里云容器镜像服务 (ACR)** 。
  * **运行:** 我们将在**阿里云容器服务Kubernetes版 (ACK)**上运行我们的后端服务，以获得最佳的弹性和可管理性。
* **前端 (React Native Mobile App):**
  * **应用壳:** 应用的二进制文件 (`.apk`/`.ipa`) 将通过标准的App Store和Google Play流程进行发布和更新。
  * **业务逻辑与界面更新:** 我们将使用 **Expo Updates (EAS Update)** 服务。这允许我们在不重新提交应用审核的情况下，向用户手机上的App**实时推送**JavaScript代码包的更新，极大地加快了迭代速度。

## **CI/CD 流水线 (GitHub Actions)**

我们将使用与代码仓库无缝集成的GitHub Actions来自动化我们的构建、测试和部署流程。

* **触发器:** 推送（push）到 `main` (生产) 或 `develop` (预发) 分支。
* **主要阶段:**
  1. **Setup:** 检出代码，安装Java和Node.js环境。
  2. **Lint & Test:** 使用 `nx affected --target=test` 命令，**仅对被代码改动影响到的项目**运行测试。
  3. **Build:**
     * (后端) 构建Spring Boot应用并将其打包成Docker镜像。
     * (前端) 构建JS代码包 (JS Bundle)。
  4. **Push:**
     * (后端) 将新的Docker镜像推送到阿里云ACR。
  5. **Deploy:**
     * (后端) 使用 `kubectl`命令，将ACK集群中的服务更新到最新的镜像版本。
     * (前端) 使用 `eas update`命令，将新的JS代码包发布给用户。

## **环境 (Environments)**

我们将设立三个标准环境：

| 环境                       | 前端连接的后端URL                     | 后端服务地址                  | 部署触发分支 | 用途             |
| -------------------------- | ------------------------------------- | ----------------------------- | ------------ | ---------------- |
| **开发**             | `http://localhost:8080`             | 本地运行                      | N/A          | 日常开发         |
| **预发(Staging)**    | `https://api-staging.foodmagic.com` | `api-staging.foodmagic.com` | `develop`  | 发布前的最终测试 |
| **生产(Production)** | `https://api.foodmagic.com`         | `api.foodmagic.com`         | `main`     | 面向最终用户     |

导出到 Google 表格

