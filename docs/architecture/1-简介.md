# **1. 简介**

**本文档概述了“食光机 (FoodMagic)”项目的完整全栈架构，包括后端系统、前端实现及其集成方式 **。它将作为AI驱动开发过程的唯一事实来源，确保整个技术栈的一致性 。

**入门模板或现有项目 (Starter Template or Existing Project)** 

入门模板：无单一模板。采用 Nx Monorepo 统一工作区：后端用 Spring Initializr 生成 api，前端用 create-expo-app 生成 mobile。

鉴于我们的技术栈组合（Spring Boot + React Native）比较独特，市面上没有完美匹配的、开箱即用的单一全栈入门模板。因此，我建议我们采用一个更灵活、更强大的方法：

**建议方案:** 我们将使用 **Nx Monorepo** 作为我们项目的顶层骨架。在Nx工作区内，我们将分别使用以下官方工具来生成我们的前后端应用：

* **后端:** 使用 **Spring Initializr** (start.spring.io) 来生成符合行业标准的 `api` 应用。
* **前端:** 使用 **Expo** 提供的 `create-expo-app` 命令来生成我们的 `mobile` 应用。
