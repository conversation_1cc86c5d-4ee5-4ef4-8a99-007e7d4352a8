# **7. 外部API (External APIs)**

本节将详细定义“食光机”项目需要集成的所有第三方服务API。

## **1. AI大语言模型 API (AI Large Language Model APIs)**

* **用途:** 这是我们应用实现核心功能的引擎，用于根据用户输入动态生成食谱。我们的**“AI服务客户端 (AI Service Client)”**组件将专门负责与这些API的集成。
* **具体服务:** 根据PRD，我们将优先集成中国的主流模型，例如：
  * **智谱AI (GLM系列)**
  * **阿里通义千问 (Qwen系列)**
  * **DeepSeek**
  * (备选) Google Gemini, OpenAI GPT系列
* **文档链接:** 开发团队在实现时，需要查阅并集成具体模型的官方API文档。
* **认证方式:** 通常使用  **API Key** ，通过 `Authorization` HTTP Header 进行认证。这些密钥必须作为安全机密（Secrets）进行管理，绝不能硬编码在代码中。
* **速率限制:** 所有主流LLM API都有速率限制（Rate Limiting，如每分钟请求数）和配额。我们的“AI服务客户端”必须能优雅地处理因达到限制而返回的HTTP 429错误，并采用适当的重试或降级策略。
* **使用的关键端点 (Key Endpoints Used):**
  * **`POST /vX/chat/completions` (通用模式):**
    * **描述:** 向此端点发送一个精心构造的、包含用户食材和偏好等信息的指令(Prompt)，并接收一个包含完整食谱内容的流式或非流式响应。
