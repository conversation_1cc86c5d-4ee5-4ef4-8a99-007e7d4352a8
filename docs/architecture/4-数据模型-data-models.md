# **4. 数据模型 (Data Models)**

本节将定义应用的核心业务实体，它们的数据结构将在前后端之间共享，并最终映射到我们的数据库表中。我们将逐一进行定义和审议，首先从最核心的**用户 (User)** 模型开始。

## **模型 1: User (用户)**

* **用途:** 代表应用的每一个独立用户。用于存储认证信息、个人资料以及与其他数据（如收藏的食谱）的关联关系。
* **关键属性:**

  * `id`: `UUID`
  * `email`: `String` (可为空, 唯一)
  * `password_hash`: `String` (可为空)
  * `display_name`: `String` (可为空)
  * **`avatar_url`** : `String` (可为空)
  * **`auth_provider`** : `String` (例如: 'LOCAL', 'WECHAT')
  * **`provider_id`** : `String` (可为空)
  * **`status`** : `String` (例如: 'ACTIVE', 'SUSPENDED')
  * **`subscription_tier`** : `String` (例如: 'FREE', 'PREMIUM')
  * `created_at`: `Timestamp`
  * `updated_at`: `Timestamp`
* **共享TypeScript接口 (Shared DTO):**

  * 这是将在我们的Monorepo中，由前端和后端共享的数据传输对象，它**不包含**密码等敏感信息。

  **TypeScript**

  ```
  export interface UserDto {
    id: string;
    email: string | null;
    displayName: string | null;
    avatarUrl: string | null;
    subscriptionTier: 'FREE' | 'PREMIUM';
  }
  ```
* **关系:**

  * 一个 **User** 可以拥有多个  **SavedRecipe (收藏的食谱)** 。
  * 一个 **User** 拥有一个  **UserPreference (用户偏好)** 。

## **模型 2: Recipe (食谱)**

* **用途:** 代表由AI生成的一份具体的食谱。这是我们应用的核心内容实体，用户将围绕它进行浏览、收藏等主要操作。
* **关键属性:**

  * `id`: `UUID` - 主键，唯一标识符
  * `title`: `String` - 食谱的标题（菜品名称）
  * `description`: `Text` - AI生成的、一段简短诱人的菜品描述
  * `image_url`: `String` (可为空) - 用于展示的菜品图片链接
  * `cooking_time_minutes`: `Integer` - 预估的烹饪总时长（分钟）
  * `difficulty`: `String` (枚举: 'EASY', 'MEDIUM', 'HARD') - 难度等级
  * `servings`: `Integer` - 适用份量（例如，2人份）
  * `ingredients_json`: `JSONB` - 存储结构化的食材清单，例如 `[{"name": "番茄", "quantity": "2个"}, ...]`
  * `instructions_json`: `JSONB` - 存储结构化的、分步的烹饪指南，例如 `["第一步：...", "第二步：..."]`
  * `source_ingredients_json`: `JSONB` - **(重要)** 存储当初用户输入的、用于生成这份食谱的原始食材列表，用于数据分析和AI效果调试
  * `created_by_user_id`: `UUID` - 外键，关联到 `User`表，记录这份食谱是由哪位用户触发生成的
  * `created_at`: `Timestamp` - 创建时间
  * `calories_per_serving`: `Integer` (可为空) - 单份能量（kcal）
  * `tags`: `TEXT[]` (可为空) - 口味/风格标签（如 '清爽','高蛋白'）
  * `timers_json`: `JSONB` (可为空) - 步骤内计时标注（秒），示例 [{"step":2,"seconds":180}]
  * `alt_text`: `String` (可为空) - 主图无障碍文本（菜名 + 主要食材）

* **共享TypeScript接口 (Shared DTO):**
  **TypeScript**

  ```
  interface Ingredient { name: string; quantity: string; }

  export interface RecipeDto {
    id: string;
    title: string;
    description: string;
    imageUrl: string | null;
    altText?: string | null;
    cookingTimeMinutes: number;
    difficulty: 'EASY' | 'MEDIUM' | 'HARD';
    servings: number;
    caloriesPerServing?: number | null;
    tags?: string[] | null;
    ingredients: Ingredient[];
    instructions: string[];
    timers?: { step: number; seconds: number }[] | null;
    createdAt: string; // ISO
  }

  ```
* **关系:**

  * 一份 **Recipe** 由一个 **User** 创建。
  * 一份 **Recipe** 可以被多个 **User** 收藏（通过一个中间的关联表 `SavedRecipe`）。

## **模型 3: SavedRecipe (收藏记录)**

* **用途:** 这是一个 **关联模型（或称“连接表”）** ，用于建立 `User`和 `Recipe`之间的多对多关系。它的每一条记录，都代表一个用户将一份食谱收藏到了他/她的个人收藏夹中。
* **关键属性:**
  * `user_id`: `UUID` - 外键，关联到 `User`表的 `id`。
  * `recipe_id`: `UUID` - 外键，关联到 `Recipe`表的 `id`。
  * `saved_at`: `Timestamp` - 用户点击收藏的时间。
  * *(注：`user_id`和 `recipe_id`将共同构成一个复合主键，确保一个用户只能收藏同一份食谱一次。)*
* **共享TypeScript接口 (Shared DTO):**
  * 这个模型主要用于后端的数据库结构，它本身通常 **没有一个直接对应的DTO** 。当“我的收藏夹”页面请求数据时，后端会通过这个关联表，直接查询并返回一个 `RecipeDto[]`（用户收藏的食谱列表）。
* **关系:**
  * 属于一个  **User** 。
  * 属于一个  **Recipe** 。

## **模型 4: UserPreference (用户偏好)**

* **用途:** 存储与单个用户关联的所有个性化设置。这份数据是“食光机”能够从一个通用工具，升级为“懂你的智能伙伴”的核心。
* **关键属性:**

  * `user_id`: `UUID` - 主键，同时也是外键，与 `User`表建立一对一关系。
  * `preferences_json`: `JSONB` - 用于存储所有偏好设置的灵活字段。采用JSON格式可以让我们在未来轻松增改偏好，而无需修改数据库表结构。
    * **示例:** `{
  "allergies": ["peanuts", "shellfish"],
  "diet": "LOW_CARB",
  "preferred_cuisines": ["ITALIAN", "MEXICAN"],
  "unit_system": "METRIC",           // or "IMPERIAL"
  "locale": "zh-CN"                  // 用于文案/单位/朗读
  }
  `
  * `updated_at`: `Timestamp` - 偏好设置的最后更新时间。
* **共享TypeScript接口 (Shared DTO):**
  **TypeScript**

  ```
  export interface UserPreferencesDto {
  allergies?: string[];
  isVegetarian?: boolean;
  diet?: 'LOW_CARB' | 'HIGH_PROTEIN' | 'KETO';
  preferredCuisines?: string[];
  unitSystem?: 'METRIC' | 'IMPERIAL';
  locale?: string; // e.g., 'zh-CN'
  }

  ```
* **关系:**

  * 属于一个 **User** (一对一关系)。
