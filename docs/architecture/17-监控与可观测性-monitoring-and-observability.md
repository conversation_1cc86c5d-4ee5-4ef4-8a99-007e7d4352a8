# **17. 监控与可观测性 (Monitoring and Observability)**

本节旨在定义一套监控策略，让我们能主动地了解应用的运行状态、快速发现和定位问题，并衡量其性能表现。

## **监控技术栈 (Monitoring Stack)**

* **前端监控 (Frontend Monitoring):**
  * **工具:** **(建议)** **Sentry**
  * **用途:** 自动捕捉前端应用中发生的JavaScript错误和崩溃（Crashes），并提供详细的诊断报告，帮助我们快速定位和修复移动端的问题。
* **后端监控 (Backend Monitoring):**
  * **工具:** **阿里云 CloudMonitor**
  * **用途:** 使用我们云平台原生的监控服务，来监控后端应用（ACK中的容器）和基础设施（RDS, Redis等）的健康状况，包括CPU、内存、网络等指标。
* **错误追踪 (Error Tracking):**
  * **工具:** **Sentry**
  * **用途:** Sentry可以同时集成到前端和后端，为我们提供一个 **统一的错误追踪平台** ，方便我们关联一次用户操作所引发的前后端完整错误链路。
* **性能监控 (Performance Monitoring):**
  * **前端:** Sentry Performance Monitoring，用于追踪移动端的应用启动时间、交互响应速度等。
  * **后端:** 阿里云 CloudMonitor (APM)，用于追踪API的响应延迟、吞吐量等。

## **核心监控指标 (Key Metrics)**

我们将关注以下几类核心指标：

* **前端指标:**
  * **应用启动时间:** 衡量用户打开应用到可交互状态所需的时间。
  * **无崩溃率 (Crash-Free Rate):** 衡量应用的稳定性。
  * **API响应延迟 (客户端视角):** 从移动端测量调用后端API的往返时间。
* **后端指标 (The Four Golden Signals):**
  * **延迟 (Latency):** API响应时间的P95和P99分位数。
  * **流量 (Traffic):** API的请求速率（每分钟请求数）。
  * **错误 (Errors):** API返回 `5xx`服务端错误的百分比。
  * **饱和度 (Saturation):** 我们后端服务容器的CPU和内存使用率。
