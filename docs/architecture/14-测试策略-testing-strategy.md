# **14. 测试策略 (Testing Strategy)**

本节旨在为“食光机”项目定义一个清晰、分层的测试策略，以确保产品质量、加速开发反馈并降低风险。

## **测试金字塔 (Testing Pyramid)**

我们的MVP阶段将遵循一个务实的测试金字塔模型：

**Plaintext**

```
      /      \
     /        \
    / MANUAL QA \  <-- (MVP阶段, 由详尽的手动测试清单覆盖核心流程)
   /_____________\
  /               \
 / INTEGRATION T.  \
/___________________\
/                     \
/    UNIT TESTS       \
-----------------------
```

* **基础 (单元测试):** 大量的、快速的单元测试，用于验证单个组件和方法的正确性。
* **中间 (集成测试):** 少量的、覆盖核心流程的集成测试，用于验证服务内部模块之间以及与数据库、缓存的交互。
* **顶层 (端到端测试):** 在MVP阶段，我们将使用一份**严格的手动QA测试清单**来替代自动化的E2E测试，以平衡质量与速度。

## **测试组织 (Test Organization)**

* **前端测试 (Frontend Tests):**
  * **位置:** 测试代码将与业务代码并置（co-located），遵循 `*.test.tsx` 的命名约定。
  * **内容:** 测试将专注于组件的渲染、用户交互（如点击）和状态变化。
  * **工具:** Jest, React Native Testing Library。
* **后端测试 (Backend Tests):**
  * **位置:** 测试代码将遵循标准的Maven/Gradle目录结构，位于 `src/test/java` 下。
  * **内容:** 单元测试将使用Mockito来模拟外部依赖。集成测试将使用 **Testcontainers** 库来启动真实的PostgreSQL和Redis实例，以验证完整的服务逻辑。
* **端到端测试 (E2E Tests):**
  * **策略:** 根据我们之前的决策，自动化的端到端（E2E）测试将被 **暂缓** 。其职责将由QA团队维护的、一份**详尽的、覆盖核心用户流程的手动测试清单**来承担。

