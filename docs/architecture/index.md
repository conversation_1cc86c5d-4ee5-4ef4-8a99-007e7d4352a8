# 食光机 (FoodMagic) 全栈架构文档

## Table of Contents

- [食光机 (FoodMagic) 全栈架构文档](#table-of-contents)
  - [1. 简介](#1-简介)
  - [2. 高阶架构 (High Level Architecture)](#2-高阶架构-high-level-architecture)
  - [3. 技术栈 (Tech Stack)](#3-技术栈-tech-stack)
  - [4. 数据模型 (Data Models)](#4-数据模型-data-models)
  - [5. API 规范 (API Specification)](#5-api-规范-api-specification)
  - [6. 组件 (Components)](#6-组件-components)
  - [7. 外部API (External APIs)](#7-外部api-external-apis)
  - [8. 核心工作流 (Core Workflows)](#8-核心工作流-core-workflows)
  - [9. 数据库模式 (Database Schema)](#9-数据库模式-database-schema)
  - [10. 统一项目结构 (Unified Project Structure)](#10-统一项目结构-unified-project-structure)
  - [11. 开发工作流 (Development Workflow)](#11-开发工作流-development-workflow)
  - [12. 部署架构 (Deployment Architecture)](#12-部署架构-deployment-architecture)
  - [13. 安全与性能 (Security and Performance)](#13-安全与性能-security-and-performance)
  - [14. 测试策略 (Testing Strategy)](#14-测试策略-testing-strategy)
  - [15. 编码规范 (Coding Standards)](#15-编码规范-coding-standards)
  - [16. 错误处理策略 (Error Handling Strategy)](#16-错误处理策略-error-handling-strategy)
  - [17. 监控与可观测性 (Monitoring and Observability)](#17-监控与可观测性-monitoring-and-observability)
