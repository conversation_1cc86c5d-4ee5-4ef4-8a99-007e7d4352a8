# **3. 技术栈 (Tech Stack)**

## **技术栈清单**

| 类别         | 技术                        | 版本          | 用途               | 理由                                                               |
| ------------ | --------------------------- | ------------- | ------------------ | ------------------------------------------------------------------ |
| 前端语言     | TypeScript                  | 5.4.x         | 主要开发语言       | 提供强类型支持，提升代码质量和可维护性。                           |
| 前端框架     | React Native (Expo)         | 0.74.x        | 跨平台移动应用框架 | 一次开发，双端（iOS/Android）运行，生态成熟。                      |
| UI组件库     | Tamagui                     | 1.9x.x        | 统一的UI与样式系统 | 高性能，支持响应式设计，符合我们的设计哲学。                       |
| 状态管理     | **(建议)**Zustand           | 4.5.x         | 轻量级前端状态管理 | 相比Redux更简洁，API友好，足以满足MVP需求。                        |
| 后端语言     | Java                        | 17 (LTS)      | 主要后端开发语言   | 性能稳定，生态系统庞大，是企业级应用的首选。                       |
| 后端框架     | Spring Boot                 | 3.2.x         | 后端应用框架       | 开发效率高，社区活跃，拥有强大的安全和数据访问能力。               |
| API 风格     | REST                        | (OpenAPI 3.x) | 前后端通信接口风格 | 成熟、通用，生态工具链完整。                                       |
| 数据库       | PostgreSQL                  | 16.x          | 主关系型数据库     | 功能强大，高度可靠，支持复杂的查询和事务。                         |
| 缓存         | Redis                       | 7.2.x         | 键值对缓存数据库   | 性能极高，用于缓存高频访问的数据，降低数据库压力。                 |
| 文件存储     | 阿里云 OSS                  | N/A           | 对象存储服务       | 存储用户上传的图片等文件，可靠、可扩展且成本低。                   |
| 认证授权     | Spring Security (JWT)       | 6.2.x         | 安全框架           | Spring Boot生态中最强大的安全解决方案。                            |
| 前端测试     | Jest, RTL                   | 29.x.x        | 单元/组件测试      | React Native社区的官方标准，测试组件交互。                         |
| 后端测试     | JUnit 5, Mockito            | 5.10.x        | 单元/集成测试      | Java生态中最主流的测试框架。                                       |
| E2E测试      | **(MVP阶段暂缓)**     | N/A           | 端到端测试         | 根据我们之前的决策，在MVP之后引入。                                |
| 构建工具     | Gradle                      | 8.7.x         | Java项目构建工具   | 相比Maven更现代、更灵活。                                          |
| 基础设施代码 | **(建议)**Terraform         | 1.8.x         | 基础设施管理       | 行业标准，声明式地管理云资源，支持多云环境。                       |
| CI/CD        | **(建议)**GitHub Actions    | N/A           | 持续集成/持续部署  | 与代码仓库无缝集成，配置简单，社区生态丰富。                       |
| 监控         | 阿里云 CloudMonitor         | N/A           | 应用与基础设施监控 | 阿里云原生监控服务，开箱即用，与我们选择的平台紧密集成。           |
| 日志         | Logback, 阿里云 Log Service | N/A           | 日志记录与聚合     | Spring Boot默认日志框架 + 阿里云原生日志服务，便于集中查询和分析。 |
| 动效/手势 | React Native Reanimated / Gesture Handler | 最新稳定 | 高帧率动效/原生手势 |
| 图形      | React Native Skia                         | 最新稳定 | 颗粒/柔光/特效     |
| 触觉/朗读 | Expo Haptics / Speech (TTS)               | 最新稳定 | 成功/错误/朗读     |
| 合同生成  | openapi-typescript or gradle-openapi-gen  | N/A      | 生成 TS API 类型   |

