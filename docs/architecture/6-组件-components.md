# **6. 组件 (Components)**

本节描述了构成“食光机”全栈系统的主要逻辑组件/服务。这里的“组件”是高阶架构层面的概念，定义了系统的主要功能模块及其职责边界。

## **组件列表**

### **1. Mobile Client (移动客户端)**

* **职责:** 提供完整的用户界面（UI），管理前端本地状态，并与后端API进行安全通信。是我们用户直接交互的媒介。
* **关键接口:** 消费我们在上一节中定义的REST API。
* **依赖关系:** 依赖于后端API。
* **技术栈:** React Native, Tamagui, Zustand。

### **2. API Gateway (API网关)**

* **职责:** 作为所有外部请求的统一入口，负责流量路由、SSL证书卸载、安全防护等。
* **关键接口:** 向公网暴露我们在上一节中定义的REST API。
* **依赖关系:** 依赖于后端服务。
* **技术栈:** 阿里云 API Gateway / SLB。

### **3. Backend Service (后端服务 - 模块化单体)**

* **职责:** 承载应用的所有核心业务逻辑。它在内部被划分为几个清晰的、高内聚的模块。
* **关键接口:** 实现我们在上一节中定义的REST API。
* **依赖关系:** 依赖于数据库、缓存和AI服务客户端。
* **技术栈:** Spring Boot, Java 17。
* **核心内部模块:**
  * **认证模块 (Auth Module):** 负责处理用户注册、登录和会话管理。
  * **用户模块 (User Module):** 负责管理用户资料和个人偏好。
  * **食谱模块 (Recipe Module):** 负责处理食谱的生成、收藏等核心业务。

### **4. AI Service Client (AI服务客户端)**

* **职责:** 这是一个 **专用的内部组件** ，负责封装与所有外部AI大语言模型通信的复杂逻辑。它处理API密钥管理、提示工程(Prompt Engineering)、以及对AI返回结果的解析和验证。
* **关键接口:**
  * **对内:** 向“食谱模块”提供一个简洁的Java接口（例如 `generateRecipe(ingredients, preferences)`）。
  * **对外:** 消费第三方AI模型的REST API。
* **依赖关系:** 依赖于第三方AI大语言模型API。
* **技术栈:** Java HTTP Client, Jackson (用于JSON解析)。
