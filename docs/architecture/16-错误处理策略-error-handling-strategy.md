# **16. 错误处理策略 (Error Handling Strategy)**

本节旨在为“食光机”应用定义一个统一、健壮的错误处理流程，确保无论是用户操作失误、网络问题还是服务器内部异常，我们都能以一种可预测的、用户友好的方式进行响应。

## **统一错误响应格式 (Error Response Format)**

为了实现前后端的清晰协作，所有由后端API返回的错误信息，都**必须**遵循以下标准化的JSON结构：

**JSON**

```
{
  "error": {
    "timestamp": "2025-08-09T23:31:52Z",
    "requestId": "a1b2c3d4-e5f6-4a3b-8c7d-9e8f7g6h5i4j",
    "code": "USER_ALREADY_EXISTS",
    "message": "A user with this email already exists."
  }
}
```

## **后端错误处理 (Backend Error Handling)**

* **策略:** 我们将使用Spring Boot的 `@RestControllerAdvice`和 `@ExceptionHandler`注解，来创建一个 **全局异常处理器 (Global Exception Handler)** 。
* **职责:**
  1. 捕获所有在业务逻辑中未被处理的异常（如数据库约束冲突、空指针等）。
  2. 将捕获到的异常，映射为上述定义的 **统一错误响应格式** 。
  3. 根据异常的类型，返回一个最合适的HTTP状态码（例如，`400`表示客户端请求错误，`404`表示资源未找到，`500`表示服务器内部错误）。

## **前端错误处理 (Frontend Error Handling)**

* **策略:** 前端的API客户端（Service层），将包含一个 **统一的响应拦截器** 。
* **职责:**
  1. 检查所有API调用的HTTP响应状态码。
  2. 如果状态码表示失败（例如 `4xx` 或 `5xx`），拦截器会自动解析后端返回的 **统一错误响应体** 。
  3. 根据错误体中的 `error.code`，前端可以决定向用户展示一个具体的、友好的提示信息（例如，当 `code`为 `USER_ALREADY_EXISTS`时，在注册页面提示“该邮箱已被注册”），或者一个通用的错误提示（例如“操作失败，请稍后再试”）。

