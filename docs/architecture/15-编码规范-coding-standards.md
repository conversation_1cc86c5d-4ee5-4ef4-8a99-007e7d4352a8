# **15. 编码规范 (Coding Standards)**

**本节旨在定义一套**

**最简化、但最关键**的编码规范。我们的目标不是创建一本厚厚的、无人阅读的规则手册，而是定下几条能确保代码一致性、可维护性和安全性的核心“黄金法则”。

## **关键规则 (Critical Rules)**

这些规则是强制性的，并将通过代码审查和自动化工具来保障执行。

* **1. 严格的类型共享 (Strict Type Sharing):**
  * 所有需要在前后端之间共享的数据结构（DTOs），**必须**在 `packages/shared-types` 包中以TypeScript接口的形式唯一定义。**严禁**在前端或后端应用中重复定义同一种数据结构。
* **2. 绝对的服务封装 (Absolute Service Encapsulation):**
  * 所有对外部AI大语言模型的API调用，**必须**通过我们架构中定义的 `AI服务客户端`组件进行。**严禁**在任何其他业务逻辑模块中直接发起HTTP请求来调用AI。
* **3. 不可逾越的模块边界 (Inviolable Module Boundaries):**
  * 后端模块之间**必须**通过定义好的API接口进行通信，**严禁**跨模块直接调用内部实现或共享数据库表。此规则将由我们的CI/CD流水线通过自动化工具强制执行。
* **4. 安全第一 (Security First):**
  * **严禁**在代码的任何地方（包括配置文件）硬编码任何密钥、密码或敏感配置。所有机密信息**必须**通过环境变量进行加载。

## **命名约定 (Naming Conventions)**

| 元素      | 前端 (TypeScript) | 后端 (Java)    | 数据库 (PostgreSQL) | 示例                                      |
| --------- | ----------------- | -------------- | ------------------- | ----------------------------------------- |
| 文件/类   | `PascalCase`    | `PascalCase` | N/A                 | `RecipeCard.tsx`,`RecipeService.java` |
| 变量/方法 | `camelCase`     | `camelCase`  | N/A                 | `getRecipe()`,`currentUser`           |
| 组件      | `PascalCase`    | N/A            | N/A                 | `<IngredientInput />`                   |
| API 端点  | N/A               | N/A            | `kebab-case`      | `/saved-recipes`                        |
| 表/列     | N/A               | N/A            | `snake_case`      | `user_preferences`                      |

