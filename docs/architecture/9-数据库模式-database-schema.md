# **9. 数据库模式 (Database Schema)**

本节将提供具体的SQL DDL (数据定义语言) 语句，用于在我们的 **PostgreSQL** 数据库中创建应用的表结构。这份模式严格遵循了我们已批准的四个核心数据模型。

**SQL**

```
-- 用户表 (Users Table)
-- 用于存储所有用户的基础信息和认证资料
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255),
    display_name VARCHAR(100),
    avatar_url VARCHAR(255),
    auth_provider VARCHAR(50) NOT NULL,
    provider_id VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    subscription_tier VARCHAR(50) NOT NULL DEFAULT 'FREE',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    -- 确保社交登录的唯一性
    UNIQUE (auth_provider, provider_id)
);

-- 食谱表 (Recipes Table)
-- 存储由AI生成的核心食谱内容
CREATE TABLE recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    cooking_time_minutes INT CHECK (cooking_time_minutes > 0),
    difficulty VARCHAR(50),
    servings INT CHECK (servings > 0),
    ingredients_json JSONB NOT NULL,
    instructions_json JSONB NOT NULL,
    source_ingredients_json JSONB,
    created_by_user_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
-- 新增字段（如为线上迁移，逐步增加并填充/回填）
ALTER TABLE recipes
  ADD COLUMN calories_per_serving INT,
  ADD COLUMN tags TEXT[],
  ADD COLUMN timers_json JSONB,
  ADD COLUMN alt_text VARCHAR(255);

-- 索引（标签检索/组合查询）
CREATE INDEX IF NOT EXISTS idx_recipes_on_tags ON recipes USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_recipes_on_created_at ON recipes (created_at);

-- 收藏幂等语义已由 (user_id, recipe_id) 主键保障；补逆向查询索引：
CREATE INDEX IF NOT EXISTS idx_user_saved_recipes_on_recipe_id ON user_saved_recipes (recipe_id);

-- 为查询某个用户创建的所有食谱建立索引
CREATE INDEX idx_recipes_on_user_id ON recipes (created_by_user_id);

-- 用户偏好表 (User Preferences Table)
-- 与用户表建立一对一关系，存储个性化设置
CREATE TABLE user_preferences (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    preferences_json JSONB,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 用户收藏食谱关联表 (User Saved Recipes Join Table)
-- 建立用户和食谱之间的多对多关系
CREATE TABLE user_saved_recipes (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    saved_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (user_id, recipe_id)
);
-- 为快速查询某个用户的所有收藏建立索引
CREATE INDEX idx_user_saved_recipes_on_user_id ON user_saved_recipes (user_id);
```

