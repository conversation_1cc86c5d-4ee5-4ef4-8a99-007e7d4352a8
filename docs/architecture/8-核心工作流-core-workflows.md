# **8. 核心工作流 (Core Workflows)**

本节将使用序列图来描绘我们系统中最关键的工作流程—— **用户生成食谱** 。这张图将清晰地展示从用户点击按钮到最终看到食谱，我们定义的各个组件之间的数据流转和调用顺序。

## **工作流 1: 用户生成食谱**

**代码段**

```
sequenceDiagram
    actor User as 用户
    participant MobileClient as 移动客户端
    participant APIGateway as API网关
    participant BackendService as 后端服务
    participant Cache as 缓存 (Redis)
    participant AIServiceClient as AI服务客户端
    participant ExternalAI as 外部AI模型API

    User->>MobileClient: 输入食材并点击“生成”
    activate MobileClient
    MobileClient->>APIGateway: POST /recipes/generate
    activate APIGateway
    APIGateway->>BackendService: (转发请求)
    deactivate APIGateway
    activate BackendService

    Note over BackendService, Cache: 食谱模块先检查缓存
    BackendService->>Cache: GET recipe:[hash(ingredients + preferences + locale + unitSystem)]
  
    alt 缓存命中 (Cache Hit)
        Cache-->>BackendService: (返回已缓存的RecipeDto)
        BackendService-->>MobileClient: (HTTP 200 OK, from Cache)
    else 缓存未命中 (Cache Miss)
        BackendService->>AIServiceClient: generateRecipe(ingredients, prefs)
        activate AIServiceClient
    
        AIServiceClient->>ExternalAI: POST /vX/chat/completions
        activate ExternalAI
        ExternalAI-->>AIServiceClient: (AI-generated recipe text)
        deactivate ExternalAI
    
        alt AI响应验证失败
            AIServiceClient-->>BackendService: (抛出 ValidationException)
            BackendService-->>MobileClient: (HTTP 502 Bad Gateway Error)
        else AI响应验证成功
            AIServiceClient-->>BackendService: (返回已解析的RecipeDto)
            deactivate AIServiceClient
        
            Note right of BackendService: 将新结果异步存入缓存
            BackendService->>Cache: SET recipe:[ingredient_hash] (with TTL) # TTL 视配置而定（如 24h）；命中率与错配率按 ingredients+prefs 组合评估。
        
            BackendService-->>MobileClient: (HTTP 200 OK, from AI)
        end
    end
    deactivate BackendService
  
    MobileClient->>User: 展示精美的食谱页面
    deactivate MobileClient
```

