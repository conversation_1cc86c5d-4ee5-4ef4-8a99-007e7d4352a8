# **13. 安全与性能 (Security and Performance)**

本节将统一阐述我们为确保“食光机”应用既安全可靠又快速响应而采取的核心策略。

## **安全要求 (Security Requirements)**

* **前端安全 (Frontend Security):**
  * **令牌存储:** Access Token 存于内存；Refresh Token 存于 **OS 安全存储**（iOS Keychain / Android Keystore，经 Expo SecureStore 或等价），避免使用 Web Cookie 方案。
  * **安全通信:** 移动客户端与后端的所有通信都必须强制使用HTTPS/TLS加密。
* **后端安全 (Backend Security):**
  * **认证与授权:** 所有API端点，除非被明确标记为公开（如 `/auth/register`），都必须受Spring Security保护，需要一个有效的JWT才能访问。
  * **输入验证:** 必须在数据进入业务逻辑层之前，对所有来自客户端的输入进行严格的合法性校验。
  * **速率限制 (Rate Limiting):** 对关键的、成本较高的API（特别是 `/recipes/generate`）实施速率限制，以防止恶意攻击和滥用。
  * **CORS策略:** 配置严格的跨源资源共享（CORS）策略，只允许我们自己的前端应用访问API。
* **数据安全 (Data Security):**
  * **密码存储:** 所有用户密码必须使用业界推荐的强哈希算法（如BCrypt）进行加盐哈希后才能存入数据库。
  * **数据加密:** 数据库中的个人身份信息（PII）等敏感数据，应考虑在静态时进行加密。

## **性能优化 (Performance Optimization)**

* **后端性能:**
  * **缓存策略:** 积极、主动地利用Redis进行缓存。特别是缓存AI生成的结果和不常变化但读取频繁的数据（如食材列表）。
  * **数据库优化:** 所有数据库查询都必须经过优化。必须为所有外键和高频查询字段（`WHERE`子句中出现的字段）建立索引。
  * **异步处理:** 对于非核心、耗时的操作（例如，记录分析数据），应考虑使用异步任务处理。
* **前端性能:**
  * **打包体积:** 严格控制应用的最终打包体积。所有引入的第三方库都需经过评估。
  * **资源优化:** 所有静态资源（图片、字体、动画文件）都必须经过极致压缩。
  * **渲染性能:** 采用代码分割和异步加载技术，优先渲染核心内容。所有动画必须使用高性能库（`React Native Reanimated`）并经过性能测试，确保稳定在60FPS。
  * **动效与资源预算:** 遵守 UI/UX 规范——单页动效资源 ≤ 50KB；遵循系统“减少动态”开关，必要时降级为静态/渐隐。


