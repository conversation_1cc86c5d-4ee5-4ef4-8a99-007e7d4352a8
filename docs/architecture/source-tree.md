# 项目目录结构指南 (Project Directory Structure Guide)

**版本:** 1.0  
**更新日期:** 2025-01-12  
**用途:** 开发代理的文件组织参考文档

## 概述

本文档定义了 FoodMagic 项目的标准目录结构和文件组织规范。所有开发人员和 AI 代理必须严格遵循这些规范，以确保代码库的一致性和可维护性。

## 项目根目录结构

```
food-magic-app/                 # Nx Monorepo 根目录
├── .github/                    # GitHub 配置
├── apps/                       # 可独立运行的应用程序
├── packages/                   # 共享代码包
├── docs/                       # 项目文档
├── tools/                      # 构建和开发工具
├── nx.json                     # Nx 配置
├── package.json               # 根 package.json
└── tsconfig.base.json         # TypeScript 基础配置
```

## 详细目录结构

### Apps 目录 - 应用程序

```
apps/
├── api/                        # Spring Boot 后端应用
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/foodmagic/
│   │   │   │   ├── auth/           # 认证授权模块
│   │   │   │   │   ├── controller/
│   │   │   │   │   ├── service/
│   │   │   │   │   ├── repository/
│   │   │   │   │   ├── dto/
│   │   │   │   │   └── entity/
│   │   │   │   ├── recipe/         # 食谱生成模块
│   │   │   │   │   ├── controller/
│   │   │   │   │   ├── service/
│   │   │   │   │   ├── repository/
│   │   │   │   │   ├── dto/
│   │   │   │   │   └── entity/
│   │   │   │   ├── user/           # 用户管理模块
│   │   │   │   │   ├── controller/
│   │   │   │   │   ├── service/
│   │   │   │   │   ├── repository/
│   │   │   │   │   ├── dto/
│   │   │   │   │   └── entity/
│   │   │   │   ├── saved/          # 收藏管理模块
│   │   │   │   │   └── [同上结构]
│   │   │   │   ├── ai/             # AI 集成模块
│   │   │   │   │   ├── service/
│   │   │   │   │   ├── client/
│   │   │   │   │   └── config/
│   │   │   │   ├── common/         # 通用功能
│   │   │   │   │   ├── exception/
│   │   │   │   │   ├── util/
│   │   │   │   │   ├── config/
│   │   │   │   │   └── aspect/
│   │   │   │   └── FoodMagicApplication.java
│   │   │   └── resources/
│   │   │       ├── application.yml
│   │   │       ├── application-dev.yml
│   │   │       └── application-prod.yml
│   │   └── test/              # 测试代码
│   └── build.gradle           # Gradle 构建文件
│
└── mobile/                    # React Native 前端应用
    ├── app/                   # Expo Router 页面
    │   ├── (auth)/           # 认证相关页面
    │   │   ├── login.tsx
    │   │   └── register.tsx
    │   ├── (tabs)/           # 标签页布局
    │   │   ├── _layout.tsx
    │   │   ├── index.tsx     # 首页/食材输入
    │   │   ├── saved.tsx     # 收藏夹
    │   │   └── profile.tsx   # 个人设置
    │   ├── recipe/           # 食谱相关页面
    │   │   └── [id].tsx      # 动态路由
    │   └── _layout.tsx       # 根布局
    ├── components/           # UI 组件
    │   ├── common/          # 通用组件
    │   │   ├── Button.tsx
    │   │   ├── Input.tsx
    │   │   └── Card.tsx
    │   ├── recipe/          # 食谱相关组件
    │   │   ├── RecipeCard.tsx
    │   │   ├── IngredientList.tsx
    │   │   └── StepsList.tsx
    │   └── layout/          # 布局组件
    │       ├── Header.tsx
    │       └── TabBar.tsx
    ├── services/            # API 服务层
    │   ├── api.ts          # API 客户端配置
    │   ├── auth.service.ts
    │   ├── recipe.service.ts
    │   └── user.service.ts
    ├── stores/             # 状态管理 (Zustand)
    │   ├── auth.store.ts
    │   ├── recipe.store.ts
    │   └── preferences.store.ts
    ├── hooks/              # 自定义 Hooks
    │   ├── useAuth.ts
    │   ├── useRecipe.ts
    │   └── useDebounce.ts
    ├── utils/              # 工具函数
    │   ├── format.ts
    │   ├── validation.ts
    │   └── constants.ts
    ├── assets/             # 静态资源
    │   ├── images/
    │   ├── fonts/
    │   └── animations/
    └── package.json
```

### Packages 目录 - 共享包

```
packages/
├── shared-types/              # 前后端共享类型定义
│   ├── src/
│   │   ├── dto/              # 数据传输对象
│   │   │   ├── user.dto.ts
│   │   │   ├── recipe.dto.ts
│   │   │   └── preference.dto.ts
│   │   ├── enums/            # 枚举类型
│   │   │   ├── auth.enum.ts
│   │   │   └── recipe.enum.ts
│   │   └── index.ts          # 导出入口
│   └── package.json
│
├── api-contract/             # OpenAPI 生成的类型
│   ├── generated/           # 自动生成（不手动修改）
│   │   ├── api.ts
│   │   └── types.ts
│   └── package.json
│
├── ui/                      # UI 组件库和主题
│   ├── src/
│   │   ├── theme/          # Tamagui 主题配置
│   │   │   ├── tokens.ts
│   │   │   ├── themes.ts
│   │   │   └── animations.ts
│   │   ├── components/     # 共享 UI 组件
│   │   └── index.ts
│   └── package.json
│
└── eslint-config/          # ESLint 配置
    ├── index.js
    └── package.json
```

## 文件命名约定

### 通用规则
- **文件名:** 使用 kebab-case（短横线命名）或 PascalCase（组件文件）
- **目录名:** 使用 kebab-case（短横线命名）
- **避免:** 空格、中文字符、特殊符号

### Java 文件命名
```
Controller:     RecipeController.java
Service:        RecipeService.java, RecipeServiceImpl.java
Repository:     RecipeRepository.java
Entity:         RecipeEntity.java
DTO:           RecipeDto.java, CreateRecipeRequestDto.java
Configuration:  SecurityConfig.java
Exception:      RecipeNotFoundException.java
```

### TypeScript/React Native 文件命名
```
组件:           RecipeCard.tsx (PascalCase)
页面:           recipe-detail.tsx (kebab-case)
服务:           recipe.service.ts
Store:          recipe.store.ts
Hook:           useRecipe.ts (camelCase with 'use' prefix)
工具:           format.util.ts
类型:           recipe.types.ts
测试:           RecipeCard.test.tsx
```

## 组件放置规则

### 后端 (Spring Boot)
| 组件类型 | 放置位置 | 说明 |
|---------|---------|------|
| Controller | `{module}/controller/` | REST API 端点 |
| Service | `{module}/service/` | 业务逻辑 |
| Repository | `{module}/repository/` | 数据访问层 |
| Entity | `{module}/entity/` | 数据库实体 |
| DTO | `{module}/dto/` | 数据传输对象 |
| Exception | `common/exception/` | 自定义异常 |
| Config | `common/config/` | 配置类 |
| Util | `common/util/` | 工具类 |

### 前端 (React Native)
| 组件类型 | 放置位置 | 说明 |
|---------|---------|------|
| 页面组件 | `app/` | Expo Router 页面 |
| UI 组件 | `components/` | 可复用组件 |
| 服务层 | `services/` | API 调用 |
| 状态管理 | `stores/` | Zustand stores |
| 自定义 Hook | `hooks/` | React hooks |
| 工具函数 | `utils/` | 纯函数工具 |
| 类型定义 | `packages/shared-types/` | 共享类型 |

## 模块组织方式

### 模块边界定义
1. **按业务领域划分:** auth、recipe、user、saved
2. **单一职责原则:** 每个模块负责一个明确的业务领域
3. **最小依赖原则:** 模块间通过接口通信，避免直接依赖

### 模块间依赖关系
```
前端 (mobile)
    ↓ (HTTP/REST)
API Gateway
    ↓
后端模块:
- auth → common (认证基础设施)
- recipe → ai, common (食谱生成需要 AI 服务)
- user → auth, common (用户管理依赖认证)
- saved → user, recipe (收藏依赖用户和食谱)
```

### 共享代码原则
1. **类型定义:** 放在 `packages/shared-types/`
2. **UI 组件:** 放在 `packages/ui/`（如有多个前端应用）
3. **工具函数:** 各端独立维护，避免过度共享
4. **配置:** 通过 Nx workspace 配置统一管理

## 测试文件组织

### 后端测试
```
src/test/java/com/foodmagic/
├── {module}/
│   ├── controller/     # Controller 测试
│   ├── service/        # Service 测试
│   └── repository/     # Repository 测试
└── integration/        # 集成测试
```

### 前端测试
```
{component-folder}/
├── ComponentName.tsx
├── ComponentName.test.tsx    # 单元测试
└── ComponentName.stories.tsx  # Storybook (可选)
```

## 配置文件位置

| 配置类型 | 文件位置 |
|---------|---------|
| Nx 配置 | 根目录 `nx.json` |
| TypeScript | 根目录 `tsconfig.base.json` |
| ESLint | 各应用目录 `.eslintrc.json` |
| Spring Boot | `api/src/main/resources/application*.yml` |
| Expo | `mobile/app.json`, `mobile/app.config.js` |
| Docker | 根目录 `docker-compose.yml` |
| CI/CD | `.github/workflows/` |

## 重要规范提醒

### ⚠️ 必须遵守的规则
1. **永远不要** 在代码中硬编码密钥或敏感信息
2. **永远不要** 将生成的代码（如 `api-contract/generated/`）手动修改
3. **永远不要** 在 `apps/` 之间直接引用代码，必须通过 `packages/`
4. **始终** 将新的业务模块放在对应的模块目录下
5. **始终** 保持测试文件与源文件的对应关系
6. **始终** 使用相对导入时从 `packages/` 引用共享代码

### 📝 最佳实践
1. 保持目录层级不超过 4 层
2. 相关文件放在一起（如组件和其测试）
3. 按功能而非文件类型组织代码
4. 定期清理未使用的文件和依赖
5. 遵循 "约定优于配置" 原则

## 新增文件决策树

当需要添加新文件时，按以下流程决定位置：

1. **是否为共享代码？**
   - 是 → 放入 `packages/` 对应子包
   - 否 → 继续

2. **属于哪个应用？**
   - 后端 → `apps/api/`
   - 前端 → `apps/mobile/`

3. **属于哪个业务模块？**
   - 认证 → `auth/`
   - 食谱 → `recipe/`
   - 用户 → `user/`
   - 收藏 → `saved/`
   - 通用 → `common/`

4. **是什么类型的文件？**
   - 按照上述组件放置规则确定具体子目录

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|---------|------|
| 1.0 | 2025-01-12 | 初始版本，定义基础目录结构 | BMad Master |