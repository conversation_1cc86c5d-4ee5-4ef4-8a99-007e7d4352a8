# **2. 高阶架构 (High Level Architecture)**

## **技术摘要 (Technical Summary)**

“食光机”将作为一个采用**模块化单体架构**的全栈应用来构建。移动端优先的用户界面将使用 **React Native 和 Tamagui** 开发，后端服务则由 **Spring Boot** 强力驱动，并通过一个 **REST API** 与前端通信。整个项目将统一在一个 **Nx Monorepo** 中进行管理，并通过 **Docker** 容器化，部署在云平台（建议  **AWS** ）上。该架构旨在为用户提供一个快速、可靠且富有吸引力的AI食谱生成体验，同时保证系统在初期的高效开发和未来的平滑扩展能力。

## **平台与基础设施选择 (Platform and Infrastructure Choice)**

* **平台:** **阿里云** 或 **腾讯云** (待最终确定)
* **理由:** 为中国大陆的目标用户提供最佳访问性能，并与我们优先选择的本土AI大语言模型实现最低延迟的集成。
* **关键服务 (以阿里云为例):**
  * **计算:** ACK 或 SAE (用于运行Docker容器)
  * **数据库:** ApsaraDB RDS for PostgreSQL
  * **缓存:** ApsaraDB for Redis
  * **文件存储:** OSS
  * **搜索:** Elasticsearch Service
  * **网络:** SLB + API Gateway

## **代码仓库结构 (Repository Structure)**

* **结构:** **Nx Monorepo**
* **理由:** 正如我们之前深入讨论的，Nx为我们这种Java+JS的异构技术栈组合，提供了最成熟的集成、代码共享和CI/CD优化能力。

## **高阶架构图**

**代码段**

```
graph TD
    A[用户] --> B[移动应用 (React Native + Tamagui)]
    B --> C[SLB / API 网关]
    B --> J[Expo Updates CDN (EAS)]
    B --> K[图片/CDN (OSS + CDN 或等价)]
    C --> D[后端服务 (Spring Boot 模块化单体)]
    D --> E[数据库 (PostgreSQL on RDS)]
    D --> F[缓存 (Redis)]
    D --> G[文件存储 (OSS)]
    D --> H[搜索服务 (Elasticsearch 可选)]
    D --> I[第三方AI大语言模型 API]

    subgraph "云平台"
        C
        D
        E
        F
        G
        H
        K
    end

```

## **架构模式 (Architectural and Design Patterns)**

* **整体架构:****模块化单体 (Modular Monolith):**  在保证初期开发速度的同时，为未来的微服务化演进奠定基础。
* **后端模式:****仓库模式 (Repository Pattern):**  这是Spring Boot数据访问的最佳实践，它将数据访问逻辑与业务逻辑解耦，便于测试和维护。
* **前端模式:****组件化UI (Component-Based UI):**  采用可复用的UI组件来构建界面，提高开发效率和一致性。
* **集成模式:****API网关 (API Gateway):**  所有客户端请求都通过一个统一的入口点，便于实现统一的认证、限流和监控。
* **收藏交互（Toggle）的一致性与幂等：**
  - 服务端需要提供幂等接口（同一 user/recipe 多次收藏不会产生重复项；取消收藏安全可重复）。
  - 提供“查询是否已收藏”的轻量端点（GET/HEAD）。
* **事件外发（可选，后期）：**
  - 为 Cook Mode 使用行为与生成质量分析，可采用 **Transactional Outbox** 输出事件到队列/日志，再由分析作业汇聚（后期启用）。

