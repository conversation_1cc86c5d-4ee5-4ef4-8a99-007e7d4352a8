# **10. 统一项目结构 (Unified Project Structure)**

本节将提供一个可视化的文件目录树，它严格遵循了我们已确定的**Nx Monorepo**策略，并为我们 **异构的技术栈** （Java Spring Boot + TypeScript React Native）提供了清晰、合理的组织方式。

**Plaintext**

```
food-magic-app/
├── .github/
│   └── workflows/
│       └── ci.yml          # GitHub Actions CI/CD 流水线配置
├── apps/                   # 存放可独立运行的应用程序
│   ├── api/                # Spring Boot 后端应用 (Java)
│   │   ├── src/main/java/com/foodmagic/
│   │   │   ├── auth/       # 认证模块
│   │   │   ├── recipe/     # 食谱模块
│   │   │   ├── user/       # 用户模块
│   │   │   └── FoodMagicApplication.java
│   │   └── build.gradle    # 后端构建脚本
│   └── mobile/             # React Native (Expo) 前端应用 (TypeScript)
│       ├── app/            # 页面与导航
│       ├── assets/         # 静态资源 (图片, 字体)
│       ├── components/     # 可复用的UI组件
│       ├── services/       # 调用API的服务
│       └── package.json    # 前端依赖
├── packages/               # 存放可在应用之间共享的代码包
│   ├── shared-types/       #【关键】前后端共享的TypeScript DTOs/类型定义
│   │   └── src/index.ts
│   └── eslint-config/      # 共享的前端代码检查规则
│   ├── api-contract/       # 由 OpenAPI 生成的 TS 客户端与类型（单一事实来源）
│   └── ui/                 # 前端 UI 共享包（tamagui 主题/组件变体/图标适配）
├── docs/                   # 项目文档
│   ├── prd.md
│   └── architecture.md
├── nx.json                 # Nx Monorepo 的核心配置文件
└── package.json            # Monorepo 根目录的 package.json
```

