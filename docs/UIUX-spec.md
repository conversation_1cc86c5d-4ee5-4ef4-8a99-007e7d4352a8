# **食光机 (FoodMagic) UI/UX 规格文档**

**版本:** 1.0
**日期:** 2025年8月9日
**作者:** Sally, UX 专家

## **1. 简介**

**本文档定义了“食光机 (FoodMagic)”用户界面的用户体验目标、信息架构、用户流程和视觉设计规格 **。它将作为视觉设计和前端开发的基石，确保我们能创造出一个内聚的、以用户为中心的体验 。

**总体UX目标与原则**

**目标用户画像 (Target User Personas)**

基于我们的PRD，我们将主要为以下两类用户进行设计：

* **忙碌的专业人士:** 时间宝贵，注重健康，希望快速做出家庭烹饪的决定，但缺乏规划的时间和精力。
* **学生和预算有限的烹饪者:** 预算和食材种类都有限，需要有创造性地利用现有资源。

**可用性目标 (Usability Goals)**

* **易学性:** 新用户能在60秒内完成从打开应用到生成第一份食谱的全部操作 。
* **高效性:** 核心操作（添加食材、生成食谱）的步骤不超过3步 。
* **容错性:** 用户的误操作（如输入无法识别的食材）应有清晰、友好的反馈和指引。

**设计原则 (Design Principles)**

1. **清晰第一 (Clarity First):** 交互和信息展示必须清晰易懂，绝不为了美学而牺牲清晰度。
2. **减少认知负荷 (Reduce Cognitive Load):** 界面只展示当前任务所必需的信息和操作，通过“渐进式披露”来引导用户。
3. **提供满足感的反馈 (Provide Satisfying Feedback):** 用户的每一次点击、滑动都应有明确、愉悦的视觉或触觉反馈，让应用感觉“活”起来。

## **2. 信息架构 (Information Architecture - IA)**

本节旨在定义“食光机”应用的整体结构、内容组织和导航方式，确保用户可以轻松地在应用中找到他们需要的信息和功能。

### **站点地图 / 屏幕清单 (Site Map / Screen Inventory)**

下面的图表展示了我们MVP阶段需要设计和开发的所有核心屏幕及其相互之间的关系。

**代码段**

```
graph TD
    subgraph "核心体验区"
        A[食材输入页 (主页)] --> B(生成中...) --> C[食谱展示页]
    end

    subgraph "个人中心区"
        D[我的收藏夹]
        E[用户设置页] --> E1[个人资料管理]
        F[登录/注册页] --> G[忘记密码流程]
        F --> H[欢迎/引导页]
    end

    subgraph "主导航 (Tab Bar)"
        Nav1("开始创作") --> A
        Nav2("我的食谱") --> D
        Nav3(我的) --> E
    end

    C -- "点击收藏(游客)" --> F
    C -- "点击收藏(已登录)" --> D
    E -- "未登录时" --> F

    linkStyle 3,4,5,6,7 stroke-width:2px,stroke:grey,stroke-dasharray: 3 5;
```

### **导航结构 (Navigation Structure)**

基于上面的站点地图，我建议采用以下导航模式，这在移动端应用中是用户最熟悉、最高效的方式：

* **主导航 (Primary Navigation):**
  * **方式:** 采用屏幕底部的  **标签栏导航 (Bottom Tab Bar)** 。
  * **标签项:**
    1. **首页:** 对应“食材输入页”，是应用的核心入口。
    2. **收藏:** 对应“我的收藏夹”，方便用户快速访问已保存的食谱。
    3. **设置:** 对应“用户设置页”，用于管理账户和偏好。
* **次级导航 (Secondary Navigation):**
  * **方式:** 在应用内部的功能流程中，采用标准的 **“页面堆叠”** 导航模式。每个新页面会覆盖在旧页面之上，并在顶部提供清晰的标题和“返回”按钮，允许用户返回上一级。

**增设登录入口:** 对于未登录的用户，可以在“我的”页面顶部，提供一个清晰的“登录/注册”入口，这是一个非常符合用户习惯的设计。

## **3. 用户流程 (User Flows)**

本节将以可视化的方式，详细描述用户为了完成一个特定目标，在我们的应用中所需要经历的每一步。我们将从最核心的用户流程开始。

### **流程 1: 游客首次生成食谱**

这是我们应用最核心、最高频的流程，也是决定新用户是否愿意留下的关键。

* **用户目标:** 作为一个首次打开应用的游客，我希望能快速输入我手头的食材并获得一个食谱，以此来判断这个应用是否对我真的有用。
* **进入点:** 首次打开应用。
* **成功标准:** 用户从打开应用到成功看到生成的食谱，全程体验流畅，且时长不超过60秒。
* **1. “冷启动”问题 (The 'Cold Start' Problem)**
* **当前流程的不足:** 当一个新用户第一次打开应用时，流程图显示他们会直接看到一个“食材输入页”。一个空白的输入框可能会让用户感到些许茫然，缺乏引导。
* **改进建议:** 我们需要设计一个引人入胜的 **“空状态” (Empty State)** 界面。这个界面不仅仅是一个输入框，它应该包含：

  * **a) 一句欢迎语:** 例如，“你好！我是你的AI厨师‘食光机’，今天想吃点什么？”
  * **b) 清晰的行动指引:** 例如，“告诉我你家里有什么食材，让我为你创造魔法！”
  * **c) 可交互的示例:** 预置几个常见的食材标签（如“鸡蛋”、“番茄”、“鸡肉”），用户可以直接点击它们开始，而不是必须从打字开始。这能极大地降低初次使用的门槛。
* **2. “等待焦虑”问题 (The 'Waiting Anxiety' Problem)**
* **当前流程的不足:** 流程图中的“显示加载动画”是一个功能性描述，但它错失了一个与用户建立情感连接、管理用户等待情绪的关键时刻。一个普通的“菊花”加载动画是枯燥的。
* **改进建议:** 我们需要设计一个  **品牌化的、有意义的加载体验** 。

  * **a) 自定义动画:** 我们可以设计一个与“食光机”主题相关的动画，比如一个正在冒着热气的锅，或者食材飞入锅中的有趣动画。
  * **b) 显示有价值的信息:** 在等待时，我们可以轮播一些烹饪小贴士、食材趣闻或者鼓励性的话语，例如：“正在混合风味...”、“大厨正在思考...”、“小贴士：你知道辣椒富含维生素C吗？”。这能让等待的过程变得有趣，并且感觉时间更短。
* **3. “然后呢？”问题 (The 'What's Next?' Problem)**
* **当前流程的不足:** 流程在“用户获得食谱”后就结束了。用户看完了食谱，然后呢？我们没有给用户提供清晰的下一步指引，可能会导致用户在完成核心任务后就流失了。
* **改进建议:** 我们需要在“食谱展示页”提供清晰的  **行动号召 (Call to Action - CTA)** 。

  * **主要CTA (开始烹饪):** 应该是最醒目的（底部固定或 FAB）。
  * **次要CTA (换个口味, 返回修改):** 放在顶部导航或内容末尾的次级样式按钮；**收藏为常驻心形 Toggle**（权重低于主要CTA）。

#### **流程图**

**代码段**

```
graph TD
    A[开始: 用户打开应用] --> B[显示<br/>食材输入页<br/><i>含欢迎语和示例</i>];
    B --> C{用户添加食材};
    C --> D[用户点击“生成食谱”];
    D --> E[显示<br/><i>品牌化加载动画<br/>与烹饪小贴士</i>];
    E --> F{API调用成功?};
    F -- 是 --> G[显示食谱展示页];
    F -- 否 --> H[显示友好错误提示];
    G --> I{用户下一步操作?};
    I --> J[点击“收藏食谱”];
    I --> K[点击“换个口味”];
    I --> L[点击“返回修改”];
    H --> C;
```

#### **边界情况与错误处理 (Edge Cases & Error Handling):**

* **用户未输入任何食材:** “生成食谱”按钮应处于不可用状态。
* **API调用失败 (如网络问题):** 在加载动画处，应显示一个简短、友好的错误提示，例如“网络开小差了，请稍后再试”，并允许用户重试。
* **AI返回无法解析或不合理的结果:** 后端应进行校验。如果返回结果有问题，应向前端返回一个特定错误，前端同样显示友好提示，例如“AI厨师今天有点累，请换个食材组合试试？”。

#### **针对“游客首次生成食谱”优化后流程的潜在风险分析**

#### **1. 关于“带示例的空状态”的风险**

* **风险** : **“选择偏见”或“锚定效应”。** 我们在空状态下提供“鸡蛋”、“番茄”等可交互的示例标签，虽然降低了使用门槛，但也可能 **限制用户的思维** 。用户可能会倾向于点击这些简单的、现成的例子，而不是真正去思考他们自己冰箱里有什么独特的食材。这可能会让他们无法体验到应用真正的“魔法”，从而低估了产品的价值。
* **缓解策略** :
* **a) 示例动态化:** 示例标签不应该是固定的，每次用户打开应用时，我们可以**随机展示**3-4个不同的常见食材。
* **b) 强化引导:** 在示例标签上方，必须有一句清晰的、引导用户自主输入的文案，例如“试试点击下面的例子， **或者，直接输入你自己的食材…** ”

#### **2. 关于“品牌化加载体验”的风险**

* **风险 1: 增加应用体积和初始加载时间。** 自定义的加载动画和内置的“小贴士”文案库，都会增加应用的打包体积。这可能会微量增加用户首次安装后的打开时间，这是一个非常敏感的用户体验节点。
* **缓解策略** :
* **a) 极致优化资源:** 所有用于加载体验的资源都必须被高度优化。动画应采用轻量级格式（如Lottie文件），“小贴士”可以是一个极小的本地文本文件。我们需要为这些资源设定一个严格的大小预算（例如，总共不超过50kb）。
* **风险 2: 让应用感觉“更慢”。** 这是一个反直觉的风险：如果一个精美的加载动画最短也要播放3秒，但后端API在1秒内就返回了结果，那我们实际上是为了“体验”而人为地让用户多等待了2秒。
* **缓解策略** :
* **a) 动画必须可中断:** 加载动画必须能被API的返回结果立即中断。一旦接收到食谱数据，应用必须立刻切换到食谱展示页面，无论动画播放到哪一帧。

#### **3. 关于“下一步操作 (CTA)”的风险**

* **风险** : **界面信息过载。** 在食谱展示页，最重要的内容是“食谱本身”。如果我们加入了过多的行动号召按钮（收藏、换个口味、返回修改、分享…），可能会让界面显得杂乱，分散用户的注意力，反而让他们找不到重点。
* **缓解策略** :
* **a) 建立清晰的视觉层级:** 我们需要为这些按钮定义优先级。
  * **主要CTA (收藏食谱):** 应该是最醒目的，例如一个悬浮按钮或固定在底部的实心按钮。
  * **次要CTA (换个口味, 返回修改):** 应该不那么突出，例如放在顶部的导航栏，或作为次级样式的按钮放在食谱内容的末尾。

### **流程 2: 注册与收藏食谱**

这个流程处理的是当一个游客用户（Guest User）被产品核心价值打动后，如何将他们转化为正式注册用户的关键转化漏斗。

* **用户目标:** 作为一个游客，当我发现一个我非常喜欢的食谱时，我希望能方便地将它保存下来供以后使用。
* **进入点:** 在“食谱展示页”点击“收藏食谱”按钮。
* **成功标准:** 用户能顺畅地完成注册（或登录），并且在此操作之前想要收藏的那个食谱，能被自动地、成功地保存到他们的新账户中。

#### **增加: 模态窗口 (Modal)**

*模态窗口是一个浮动在当前页面之上的小窗口，背景页面通常会变暗或模糊。*

* **优点:**
  * **✅ 保持上下文 (Context Retention):** 这是最大的优点。用户在注册时，依然能隐约看到背景里的那份吸引他的食谱。这会不断地提醒他注册的 **动机** ——“我是为了收藏这份食谱才注册的”。这能极大地降低用户因“被打断”而放弃的概率。
  * **✅ 体验更轻量:** 模态窗口给人的感觉更像是一个“快速的临时步骤”，而不是一个“沉重的跳转”。这与我们为“忙碌的专业人士”打造的“高效”体验相符。
  * **✅ 减少认知负荷:** 用户的注意力被完全集中在弹出的窗口内，完成任务后窗口消失，直接回到原来的地方，流程非常顺滑。

#### **流程图**

**代码段**

```
graph TD
    A[开始: 用户在食谱页<br/>点击“收藏”按钮] --> B{系统检查<br/>用户登录状态};
    B -- 已登录 --> C[调用“收藏”API];
    B -- 未登录/游客 --> D[弹出<br/>“登录/注册”<br/><b>模态窗口</b>];
    D --> E{用户选择?};
    E -- 选择“注册” --> F[进入注册流程];
    E -- 选择“登录” --> G[进入登录流程];
    F --> H[用户提交注册信息];
    G --> I[用户提交登录信息];
    H --> J{注册成功?};
    I --> K{登录成功?};
    J -- 是 --> L[系统自动登录<br/>并自动收藏<br/>之前的食谱];
    K -- 是 --> L;
    J -- 否 --> M[在当前页显示<br/>注册错误提示<br/>(如“邮箱已存在”)]
    K -- 否 --> N[在当前页显示<br/>登录错误提示<br/>(如“密码错误”)]
    L --> O[显示“收藏成功!”<br/>的Toast提示];
    O --> P[跳转到<br/>“我的食谱”页<br/>(可选,或停留在当前页)];
    P --> Q[结束: 用户转化<br/>并完成收藏];

    M --> F;
    N --> G;

    style Q fill:#90EE90
```

#### **边界情况与错误处理 (Edge Cases & Error Handling):**

* **用户中途放弃:** 如果用户在“登录/注册”引导页选择关闭或返回，应直接回到之前的“食谱展示页”，食谱保持未收藏状态。
* **注册/登录失败:** 错误提示必须在当前页面内清晰地显示，而不是通过弹窗。例如，在注册页面提示“该邮箱已被注册，您是否希望直接登录？”
* **注册/登录后，自动收藏失败:** 这是一个罕见的边界情况。此时用户已经登录成功，我们应提示“账户已登录，但刚才的食谱收藏失败，请重试”，而不是让用户退出登录。

### **流程 3: 已登录用户生成并收藏食谱**

这个流程描述了我们希望用户重复体验的“核心习惯回路”，因此它的顺滑和无摩擦至关重要。

* **用户目标:** 作为一个已登录的用户，我希望能无缝地使用应用的核心功能，并方便地管理我的个人食谱收藏。
* **进入点:** 已登录用户打开应用。
* **成功标准:** 已登录用户可以顺畅地完成生成和收藏食谱的全过程，没有任何不必要的打断。

#### **流程图**

**代码段**

```
graph TD
    A[开始: 已登录用户打开应用] --> B[显示<br/>食材输入页 (主页)];
    B --> C{用户添加食材};
    C --> D[用户点击<br/>“生成食谱”按钮];
    D --> E[显示加载动画<br/>并调用API];
    E --> F[显示<br/>食谱展示页];
    F --> G[用户点击“收藏”按钮];
    G --> H{用户会话(Session)有效?};
    H -- 是 --> I[调用“收藏”API<br/>按钮变为“已收藏”状态];
    H -- 否 --> J[弹出“请重新登录”<br/>模态窗口];
    I --> K[结束: 收藏成功];
    J --> L[用户重新登录];
    L --> I;

    style K fill:#90EE90
```

#### **边界情况与错误处理 (Edge Cases & Error Handling):**

* **会话过期 (Session Expired):** 这是此流程中最关键的边界情况。如果用户在一段时间后回到应用，他们的登录状态可能在本地有效，但服务端的会话已过期。当他们点击“收藏”时，API会返回401/403错误。此时，应用不应直接报错，而应 **优雅地弹出“请重新登录”的模态窗口** 。用户成功重新登录后，应**自动**为他们完成之前的收藏操作。
* **重复收藏:** 采用“收藏/已收藏”**开关（Toggle）**方案：已收藏时显示“已收藏”（实心心形）；**再次点击触发确认对话**（确认后调用取消收藏 API），而不是禁用按钮。


#### **针对“已登录用户”流程的批判与改进**

#### **1. “欢迎回来”的体验 (The 'Welcome Back' Experience)**

* **当前流程的不足:** 目前的流程中，已登录用户和游客用户打开应用时，看到的都是同一个空白的“食材输入页”。这错失了一个与老用户建立联系、提供个性化价值的绝佳机会。
* **改进建议:** 我建议为**已登录用户**设计一个 **个性化的主页** 。这个主页依然以“食材输入”为核心功能，但会增加一些个性化模块，例如：
  * **a) 亲切的问候:** “你好, [用户名]！今天想创造什么美味？”
  * **b) 快捷入口:** 在页面上用小卡片的形式，展示用户“ **最近收藏的食谱** ”，方便他们快速回顾。
  * **c) 智能推荐:** 基于用户过去的收藏，我们可以推荐一些他们可能感兴趣的 **新食材组合** ，以激发灵感。
  * 这将主页从一个单纯的“工具”，升级为了一个更智能、更贴心的“个人厨房助手”。

#### **2. “收藏”按钮的交互细节 (The 'Save' Button Interaction Details)**

* **当前流程的不足:** 我们之前定义，对于已收藏的食谱，按钮会变为“禁用”状态。这是一个功能上的“死胡同”，用户无法进行“取消收藏”的操作。
* **改进建议:** 我建议将“收藏”按钮设计成一个 **开关 (Toggle)** ：
  * **a) 初始状态:** 显示为“收藏”，配一个空心图标。
  * **b) 点击后:** 调用收藏API，成功后按钮状态变为“已收藏”，配一个实心图标，并弹出一个短暂的成功提示（Toast）。
  * **c) 再次点击:** 弹出一个确认对话框：“您确定要从收藏夹中移除这道食谱吗？” 用户确认后，调用取消收藏API，按钮恢复初始状态。这是一个更完整、更符合用户习惯的交互模式。

#### **3. 会话过期的优雅处理 (Graceful Handling of Session Expiration)**

* **当前流程的不足:** 流程正确地识别了需要弹出“重新登录”的模态窗口，但突然弹出的登录框可能会让用户感到困惑和被打断。
* **改进建议:** 我们可以在模态窗口中增加 **解释性的文案** ，来安抚用户情绪，并将一次打断转化为一次体现安全关怀的互动。
  * **a) 标题:** “为了保护您的账户安全，请重新登录”。
  * **b) 说明:** “您的登录已过期，重新登录后即可继续收藏食谱。”

## **4. 线框图与原型 (Wireframes & Mockups)**

本节将定义应用关键页面的基础布局和元素。由于我们追求高度定制化和“人情味”的设计，我不会在这里绘制精确到像素的线框图，而是会通过**区块布局**和**元素清单**的方式，来勾勒每个核心页面的结构和功能，为后续的高保真视觉设计（通常在Figma等专业工具中完成）提供清晰的指引。

### **4.1 关键页面布局: 食材输入页 (主页)**

* **页面目的:** 作为应用的起点和核心交互界面，旨在让用户（无论是游客还是已登录用户）都能快速、轻松且愉快地输入食材。
* **关键元素与区块布局 (从上至下):**

  1. **顶部导航/欢迎区 (Header Area):**
     * 显示应用的Logo或名称“食光机”。
     * 为已登录用户显示个性化欢迎语（例如，“你好, [用户名]！”）。
  2. **核心输入区 (Core Input Area):**
     * **行动指引文案:** 一句清晰、有人情味的引导语（例如，“告诉我你家里有什么食材...”）。
     * **食材输入框:** 用户输入文本、触发自动补全的核心区域。
     * **已选食材标签列表:** 动态显示用户已添加的所有食材标签，每个标签都带有关闭按钮。
  3. **智能建议区 (Suggestion Area):**
     * 当输入框为空时，此区域会 **动态显示可交互的示例食材标签** ，以帮助用户启动。
  4. **个性化模块 (Personalized Modules - 仅限已登录用户):**
     * 一个 **水平滚动的卡片列表** ，用于展示用户“ **最近收藏的食谱** ”，提供快捷入口。此模块会异步加载，不阻塞核心输入功能。
  5. **主操作按钮 (Primary Action Button):**
     * 一个醒目的“ **生成食谱** ”按钮，建议固定在屏幕底部，方便用户单手操作。
* **核心交互说明 (Interaction Notes):**

  * “生成食谱”按钮在用户至少添加一个食材前，处于禁用状态。
  * 点击“智能建议区”的示例标签，会直接将其添加到“已选食材列表”中。
  * 整个页面的设计将遵循我们之前确定的“不对称”、“留白”等“反AI模板感”的设计策略。

  - 快捷动作（厨房语义）：
    - 「加点什么？」：鸡蛋 / 番茄 / 菠菜（胶囊，可多选，点即入“已选食材”）
    - 「清一清冰箱」：打开“我的食材柜”（最近使用 / 即将过期）
    - 「想吃什么感觉？」：热汤 / 清爽 / 高蛋白（影响生成提示）

### **4.2 关键页面布局: 食谱展示页 (Recipe Display Page)**

* **页面目的:** 以一种清晰、美观、引人入胜的方式，展示AI生成的食谱，激发用户的烹饪欲望，并引导他们开始烹饪，或收藏以便稍后使用。
* **关键元素与区块布局 (从上至下):**

  1. **顶部导航栏 (Top Navigation Bar):**

     * **返回按钮:** 允许用户返回到“食材输入页”以修改食材。
     * **次要操作:** 放置“换个口味 (重新生成)”等次要操作的图标按钮。
  2. **食谱主图 (Hero Image):**

     * 一个尺寸较大、吸引眼球的图片区域，用于展示菜品的美食照片。（在MVP阶段，我们可以根据核心食材，智能地匹配一个高质量的占位图或插图）。
     * 弱网优先使用 LQIP/骨架图，占位不抖动；图片懒加载并提供语义化 alt（读屏：菜名 + 主要食材）。
  3. **食谱标题:**

     * 位于主图下方或之上，使用 H1（24–28sp，LH 1.3–1.4），两行截断不省略关键词；正文不用手写体；对比度 ≥4.5:1。。
  4. **核心信息栏 (Metadata Bar):**

     * 一行水平排列的、带图标的关键信息摘要，方便用户快速评估。包括：“ **预估烹饪时间** ”、“ **难度等级** ”、“ **份量** ”、“**口味/风格标签（清爽/厚重/高蛋白）**” 、 “**能量（kcal/份）**”。
     * 读屏顺序：时间 → 难度 → 份量 → 口味 → 能量。
  5. **食材清单 (Ingredients List):**

     * 一个格式清晰的列表，包含所有必需的食材及其建议用量。建议采用 **清单（Checklist）样式** ，允许用户在准备时点击勾选。
     * 单位本地化（g/oz、°C/°F）；提供常见替代食材提示（过敏/缺货时的可替换项）。
  6. **烹饪步骤 (Instructions / Steps):**

     * 带编号的、分步的烹饪指南。每个步骤都应是简洁、易于理解的段落。
     * 涉及计时的步骤内置一键计时；支持语音朗读（逐步/整篇）。
  7. 主要操作与收藏：

     - 主操作按钮：开始烹饪（进入 Cook Mode），优先级最高；建议底部固定或 FAB，便于单手触达。
     - 收藏：心形 Toggle（标题区或工具栏），可撤销；成功时短 Toast，成功时触觉反馈（success），可选轻粒子（≤600ms；遵循系统‘减少动态’时自动关闭）。
* **核心交互说明 (Interaction Notes):**

  * 整个页面是可滚动的。
  * iOS 返回手势增加阻尼避免误触。
  * 加载动画可随数据就绪立即中断，避免拖延主内容出现。
  * “收藏食谱”按钮将是我们之前讨论过的“收藏/已收藏”开关(Toggle)。
  * 点击“食材清单”中的某一项，可以将其标记为“已准备”（例如，添加删除线样式），方便用户在备菜时跟踪。
* Cook Mode（烹饪模式）：

  - 大字号步骤 + 勾选清单 + 常亮屏幕
  - 计时器：读到定时步骤时提供一键计时
  - 手势：左右滑切步骤；返回手势增加阻尼避免误触
  - 可访问：读屏顺序 = 标题→元信息→食材→步骤
  - 朗读速度与单位（g/oz、°C/°F）随系统语言本地化。
  - 减少动态’开启时关闭粒子与大位移，仅保留渐显/高对比样式。

### **4.3 关键页面布局: 我的收藏夹 (My Cookbook)**

* **页面目的:** 为用户提供一个清晰、直观、且富有吸引力的方式，来浏览和管理他们所有收藏过的食谱。
* **关键元素与区块布局 (从上至下):**
  1. **顶部导航栏 (Top Navigation Bar):**
     * **页面标题:** 一个清晰的标题，例如“我的食谱”。
     * **(未来功能) 搜索/筛选:** 在未来的版本中，这里可以增加一个搜索框或筛选/排序按钮，但在MVP阶段，一个简单的标题即可。
  2. **内容区域 (Content Area):**
     * **A) 空状态 (Empty State):**
       * 当用户**还没有收藏任何食谱**时，这个状态的设计至关重要。它应该包含：
         * 一个符合我们“温暖治愈”风格的 **友好插图** 。
         * 一句鼓励性的文案，例如：“你的私人食谱本还是空的”。
         * 一个明确的 **行动号召 (CTA) 按钮** ：“去创作第一道菜吧！”，点击后会跳转到“食材输入页”。
     * **B) 食谱列表 (Recipe List):**
       * 当用户有收藏时，此区域会显示一个 **可垂直滚动的食谱卡片网格** 。
       * 每个**食谱卡片 (Recipe Card)** 包含：
         * **缩略图:** 菜品的图片，以提供视觉吸引力。
         * **食谱标题:** 清晰地显示菜品名称。
         * **核心信息:** 少量关键元数据（如烹饪时间），帮助用户快速决策。
* **核心交互说明 (Interaction Notes):**
  * **点击卡片:** 用户点击任意一个食谱卡片，都会跳转到该食谱的“食谱展示页”。
  * **滚动加载:** 当用户收藏的食谱数量很多时，列表应支持无限滚动或“懒加载”（Lazy Loading），以保证页面性能。
  * **MVP范围:** 在MVP阶段，此页面专注于“浏览”功能。更复杂的操作，如“删除”或“整理”，将从食谱展示页本身发起（通过我们之前设计的“取消收藏”开关）。

### **4.4 关键页面布局: 用户设置页 (User Settings Page)**

* **页面目的:** 为用户提供一个清晰、集中的位置，来管理他们的账户信息、个性化偏好，并查找应用相关的支持信息。
* **关键元素与区块布局 (采用分组列表样式):**
  1. **顶部导航栏 (Top Navigation Bar):**
     * **页面标题:** “我的”。
  2. **账户区 (Account Section):**
     * **游客状态:** 在页面顶部显示一个醒目的“ **登录 / 注册** ”按钮，引导用户转化。
     * **登录状态:** 显示用户的头像和昵称。点击这一区域，会跳转到“ **个人资料管理** ”子页面（用于修改密码等）。
  3. **个性化设置区 (Personalization Section):**
     * **列表项 1:** “ **饮食偏好** ”。点击后，跳转到一个新的子页面，用户可以在此统一管理他们的“核心偏好（如过敏原）”和“高级偏好（如饮食目标、菜系风味）”。
  4. **应用信息区 (App Information Section):**
     * **列表项 2:** “关于我们”。
     * **列表项 3:** “隐私政策”。
     * **列表项 4:** “服务条款”。
  5. **退出登录 (Logout):**
     * **登录状态:** 在列表的最底部，提供一个视觉上与其他列表项区分开的“ **退出登录** ”按钮。
* **核心交互说明 (Interaction Notes):**
  * 整个页面是一个标准的、可垂直滚动的列表。
  * 采用“ **渐进式披露** ”原则：复杂的设置项（如具体的偏好选择）被收纳在子页面中，以保持主设置页面的简洁和清晰。

## **5. 组件库 / 设计系统 (Component Library / Design System)**

本节将定义构成我们应用界面的核心UI组件。一个好的组件库是保证产品体验一致性、提升开发效率的关键。

### **设计系统方案 (Design System Approach)**

根据我们在PRD中的技术选型，我们将 **基于 `Tamagui` UI库，构建一个轻量级的、定制化的设计系统** 。我们不从零创造，而是在一个强大的基础上进行风格化和扩展，以实现我们独特的“温暖治愈”和“反AI模板感”的视觉目标。

### **核心组件 (Core Components)**

以下是我们在MVP阶段需要优先定义和设计的几个最基础的组件：

#### **1. 布局与导航 (Layout & Navigation)**

* **导航栏 (Header):**
  * **用途:** 显示在每个页面的顶部，承载页面标题、返回按钮和次要操作（如“换个口味”）。
* **标签栏 (Bottom Tab Bar):**
  * **用途:** 应用的全局主导航，固定在屏幕底部，包含“开始创作”、“我的食谱”和“我的”三个入口。

#### **2. 容器 (Containers)**

* **卡片 (Card):**
  * **用途:** 展示独立、聚合的信息单元，如“我的收藏夹”中的每一个食谱。
* **模态窗口 (Modal):**
  * **用途:** 用于需要中断主流程、吸引用户全部注意力的任务，例如我们已确定的“登录/注册”流程和“取消收藏”的确认对话框。

#### **3. 内容与控件 (Content & Controls)**

* **按钮 (Button):**

  * **用途:** 用户执行操作的核心元素。

  - 变体（Tamagui）：
    - variant: primary | secondary | quiet
    - size: sm | md | lg
  - 状态映射（用色阶，不写死 Hex）：
    - primary: apricot-600（默认）→ apricot-700（按压）→ apricot-200（禁用）
    - secondary: matcha-600 → matcha-700 → matcha-200
  - 按压反馈：scale 0.98 + 轻触觉（Impact.light）
  - 触达面积：≥ 44×44
* **食材输入/标签字段 (Ingredient Input / Tag Field):**

  * **用途:** 应用核心的食材输入交互控件。
* **列表项 (List Item):**

  * **用途:** 用于构建“用户设置页”这样的列表视图，通常包含图标、文本和指向新页面的箭头。
* 加载指示器 (Loading Indicator):

  - 可中断；弱网才显示“烹饪小贴士”轮播
  - 动效预算：单页动效资源 ≤ 50KB；尊重系统“减少动态”
* **排版组件 (Typography):**

  * **用途:** 这是设计系统的原子基石。我们需要预先定义好标题 (H1, H2, H3)、正文、说明文字等所有文本的样式组件，以确保整个应用的视觉一致性。

## **6. 品牌与风格指南 (Branding & Style Guide)**

本节将作为“食光机”视觉设计的唯一事实来源，确保整个应用在视觉上保持高度的一致性、专业性和品牌感。

### **视觉识别 (Visual Identity)**

* **品牌指南:** 本节内容即为我们MVP阶段的核心品牌视觉指南。

### **调色板 (Color Palette)**

*(注：以上Hex色号为初步示例，最终颜色将在高保真设计阶段精确定义)*

我们采用“食物语义 + 色阶”的方案（示例 Hex 便于落地，最终以 Token 为准）。

| 类型     | Token 示例       | Hex示例           | 用途          |
| -------- | ---------------- | ----------------- | ------------- |
| 主色     | apricot-600      | #FFB27A           | 主按钮/强调   |
| 主色(按) | apricot-700      | #F59D62           | 按压          |
| 次色     | matcha-600       | #8BC28A           | 成功/二级强调 |
| 次色(按) | matcha-700       | #76B877           | 按压          |
| 点缀     | berry-600        | #8E3B6E           | 空状态/微装饰 |
| 中性     | canvas / surface | #FAF7F3 / #EEE8E1 | 画布/容器     |
| 文本     | text-strong      | #2B2520           | 正文深色      |

注：正文/重要文本与背景对比度 ≥ 4.5:1；次要文本 ≥ 3:1。所有状态色从同一色系的色阶派生。

### **字体排版 (Typography)**

**字体家族**

* **主字体 (中/英文):** 思源黑体 (Source Han Sans) / Inter
* **备用/品牌字体:** 阿里巴巴普惠体 (Alibaba Puhuiti)

#### **字号层级**

| 元素 | (示例)字号 | (示例)字重 | 用途               | 使用场景（Usage）  |
| ---- | ---------- | ---------- | ------------------ | ------------------ |
| H1   | 28px       | Bold       | 页面主标题         | 严格用于页面主标题 |
| H2   | 22px       | Semi-bold  | 章节标题           | 页面内大区块标题   |
| H3   | 18px       | Medium     | 小节标题           |                    |
| 正文 | 16px       | Regular    | 主要阅读文本       |                    |
| 说明 | 14px       | Regular    | 辅助性、说明性文字 |                    |

### **图标 (Iconography)**

* 图标库：功能性图标统一采用同一风格（如 Lucide 或 Phosphor 的线性/duotone，stroke 1.75–2.0）；装饰性图标/插图可多样化（空状态可用 Open Peeps / Blush 等）。

### **间距与布局 (Spacing & Layout)**

* **栅格系统:** 建议采用业界标准的  **8pt 栅格系统** ，以确保所有元素之间的间距都保持一致和协调。
* **间距单位:** 所有边距(margin)、内边距(padding)都应是8的倍数（或在小尺寸上是4的倍数），例如 `8px, 16px, 24px, 32px`。

### **针对“品牌与风格指南”的批判与改进**

#### **1. 关于“调色板 (Color Palette)”**

* **可以更进一步思考的点:** 我们定义了颜色的“角色”（主色、次色等），但这还不够系统。一个专业的设计系统，需要为每种颜色提供一个完整的色阶，并明确定义它们在不同交互状态下的使用。
* **改进建议:** 我建议我们将每种核心颜色，都扩展为一个从浅到深的 **色阶 (Color Scale)** （例如，从100到900）。这将为我们提供巨大的设计灵活性，并能清晰地定义各种交互状态。例如：
  * **`主要按钮`:** 默认背景色 `green-500`，悬停/按下时背景色 `green-600`，禁用时背景色 `green-200`。
  * **`正文文本:`** `neutral-800`。
  * **`说明文字:`** `neutral-600`。
    这样，开发者就可以直接在代码中调用这些设计“令牌 (Token)”，而不是使用具体的Hex色号。

#### **2. 关于“字体排版 (Typography)”**

* **可以更进一步思考的点:** 当前的字号层级表虽然有示例，但缺乏明确的 **使用场景** 。开发者在面对一段文本时，可能不确定应该用H2还是H3。
* **改进建议:** 我建议在字号层级表的最后一列，明确增加一列“ **使用场景 (Usage)** ”：

| 元素 | (示例)字号 | (示例)字重 | **使用场景**                 |
| ---- | ---------- | ---------- | ---------------------------- |
| H1   | 28px       | Bold       | **严格用于页面的主标题**     |
| H2   | 22px       | Semi-bold  | **用于页面内部的大章节标题** |
| ...  | ...        | ...        | ...                          |

#### **3. 关于“图标 (Iconography)”**

* **可以更进一步思考的点:** “混合使用”的策略虽然能避免模板感，但如果没有规则，也可能导致视觉上的混乱。
* **改进建议:** 我们需要为此制定一条清晰的指导原则，以确保“混合”的和谐：
  * **“功能性图标 vs. 装饰性图标”原则:**
    * **功能性图标** （例如，返回、设置、收藏、关闭等需要用户直接交互的图标）应 **统一使用一个风格** （例如，全部使用 `Tabler Icons`的线条风格），以保证交互体验的一致性和可预测性。
    * **装饰性/说明性图标** （例如，在空状态页面或引导页中起辅助说明作用的图标）则可以 **更自由、更多样化** ，可以采用实色填充、手绘等不同风格，以增加“人情味”。

#### **4. 关于“间距与布局 (Spacing & Layout)”**

* **可以更进一步思考的点:** “8pt栅格系统”是一个很好的原则，但我们可以将它变得对开发者更友好、更易于执行。
* **改进建议:** 我建议我们直接在设计系统（和前端代码的主题文件中）中，定义一个具体的 **间距单位变量 (Spacing Scale)** 。例如：
  * `spacing-xs: 4px`
  * `spacing-sm: 8px`
  * `spacing-md: 16px`
  * `spacing-lg: 24px`
  * `spacing-xl: 32px`
  * 之后，所有的设计和开发都应 **强制使用这些预设的单位** ，而不是随意的像素值。例如，设计师交付的设计稿上会标注“卡片之间的间距为 `spacing-md`”，开发者可以直接在代码中使用这个变量。

## **7. 无障碍设计要求 (Accessibility Requirements)**

验收清单（核心流程必须通过）：

- 对比度：正文 ≥ 4.5:1；次要文本 ≥ 3:1
- 触达面积：交互元素 ≥ 44×44
- 读屏顺序：标题→元信息→食材→步骤
- 焦点管理：键盘/读屏可达，焦点环可见且跳转可预测
- 系统“减少动态”开启时：取消粒子与大位移，改用渐隐

本节旨在定义明确的无障碍设计标准，确保“食光机”对所有用户，包括有视觉、听觉或运动障碍的用户，都是友好和可用的。这是一个负责任的产品必须具备的品质。

### **合规目标 (Compliance Target)**

**我们的目标是在MVP阶段，核心用户流程上达到 **

**WCAG 2.1 AA** 级合规标准 。

**关键要求 (Key Requirements)**

#### **视觉方面 (Visual)**

* **色彩对比度:** 所有文本与背景的对比度必须至少达到 **4.5:1** (AA级)，以确保内容清晰可读 。
* **焦点指示器:** 所有可交互的元素（按钮、输入框、链接等）在获得键盘或辅助功能焦点时，必须有**清晰、可见的视觉样式变化** 。

#### **交互方面 (Interaction)**

* **键盘导航:** 用户必须能 **仅通过键盘** **（或等效的辅助功能输入设备），完成我们的全部三个核心用户流程（生成食谱、注册、收藏） **。
* **屏幕阅读器支持:** 所有控件、图片和重要的内容，都必须有 **清晰的文本标签或替代文本 (alt text)** **，以便屏幕阅读器能正确地为用户朗读和解释界面内容 **。
* **触摸目标尺寸:** 所有可点击的元素，其有效触摸区域尺寸建议 **不小于 44x44 像素** **，以方便用户在触摸屏上准确点击 **。

### **测试策略 (Testing Strategy)**

**我们将采用自动化和手动测试相结合的策略。在CI/CD流水线中集成**

 **自动化可访问性检查工具** **，并由QA团队根据检查清单，在真实设备上对核心流程进行****手动的屏幕阅读器和键盘导航专项测试** 。

### **针对“无障碍设计要求”的批判与改进**

#### **1. 在“全面性”方面 (Regarding 'Comprehensiveness')**

* **可以更进一步思考的点:** 我们当前的要求主要集中在视觉和交互层面，但忽略了同样重要的**内容结构**层面。一个结构清晰的内容，对于屏幕阅读器用户来说至关重要。
* **改进建议:** 我建议在“关键要求”中，增加一个**“内容与结构”**的子类别，并补充以下两点基础但高价值的要求：
  * **标题层级 (Heading Structure):** 应用内的文本内容必须遵循逻辑化的标题层级（H1, H2, H3...）。一个页面只应有一个H1，且标题层级不能跳跃（例如，H2下面直接跟H4）。
  * **表单标签 (Form Labels):** 所有的输入框都必须有关联的、可见的文本标签。不应仅使用占位符（placeholder）作为标签。

通过补充这两点，我们的无障碍设计要求就覆盖了**视觉、交互和内容**三大核心领域，变得更加全面。

#### **2. 在“严格性”方面 (Regarding 'Strictness')**

* **可以更进一步思考的点:** WCAG 2.1 AA是一个高标准，这对于一个追求速度的MVP来说，是否过于严格了？
* **我的判断与理由:** 我认为当前的严格程度是 **恰当且必要的** 。我们并没有要求在MVP阶段对应用的**每一个角落**都达到AA标准，而是明确地将范围限定在了“ **核心用户流程** ”上。
  * **这是战略投资，而非成本开销:** 将这些基础的无障碍要求从第一天就融入我们的开发流程，其成本远低于在产品后期再去“亡羊补牢”。这不仅是“政治正确”，从长远来看，这也是更经济、更高效的做法。
  * **扩大潜在市场:** 一个无障碍的应用，意味着残障人士、有临时性障碍（如手臂受伤）的人，甚至是在嘈杂环境中不方便听声音的老年用户，都能成为我们的潜在用户。这与我们实现10,000 MAU的业务目标是相符的。
  * **因此，我坚持认为，我们不应降低这些在核心流程上的基础标准。**

## **8. 响应式策略 (Responsiveness Strategy)**

本节旨在定义应用的响应式设计方法，确保“食光机”的界面能够在从小屏手机到大屏平板等不同设备上，都能优雅地适应并保持高度可用性。

### **核心原则：移动优先 (Mobile-First)**

我们的设计和开发将严格遵循“移动优先”的原则。这意味着我们会首先为最小的、最受约束的屏幕（手机）进行完美的设计，然后再逐步增强和调整布局以适应更大的屏幕（平板电脑等）。

### **断点 (Breakpoints)**

**我们将采用基于**

`<span class="citation-252">Tamagui</span>`框架内置的、令牌化（Token-based）的响应式系统。以下是我们为MVP阶段定义的标准断点 ：

| 断点名称 (Token)     | 最小宽度 (约) | 目标设备        |
| -------------------- | ------------- | --------------- |
| **$sm**(Small)       | -             | 手机 (主要目标) |
| **$md**(Medium)      | 768px         | 平板电脑 (垂直) |
| **$lg**(Large)       | 992px         | 平板电脑 (水平) |
| **$xl**(Extra Large) | 1200px        | (未来) 桌面端   |

### **适应模式 (Adaptation Patterns)**

**当屏幕尺寸变化时，我们的布局将采用以下模式进行适应 **：

* **布局变化 (Layout Changes):**
  * **手机 ($sm):** 严格采用单列布局，所有内容垂直排列，以优化滚动体验。
  * **平板 ($md, $lg):** 在空间允许的情况下，部分页面（如“我的收藏夹”）的卡片网格，可以从2列变为3列或4列，以更有效地利用屏幕宽度。
* **导航变化 (Navigation Changes):**
  * 屏幕底部的“标签栏导航”在手机和平板设备上都表现良好，我们将保持其一致性。
  * 对于未来可能的桌面版本，主导航可能会迁移到侧边栏。
* **内容优先级 (Content Priority):**
  * 无论屏幕大小如何，核心内容（如食材输入框、食谱步骤）和主要操作按钮，都将保持在最优先、最易于访问的位置。

### **针对“响应式策略”的批判与改进**

#### **1. 关于断点范围的思考 (Regarding the Scope of Breakagains)**

* **可以更进一步思考的点:** 我们定义的 `$sm`(手机)断点，其范围非常广，从最小的iPhone SE到最大的Pro Max型号。一个在超大屏手机上看起来舒适的布局，在小屏手机上可能会显得拥挤。
* **改进建议:**
  * **a) 明确“移动优先”的设计基准:** 我建议我们更精确地定义我们的“移动优先”原则。我们的设计应首先在 **最小的主流手机屏幕尺寸** （例如宽度为375px）上达到完美。
  * **b) 采用流式布局 (Fluid Layout):** 在 `$sm`这个大的手机范围内，我们不需再增加更细的断点。取而代之，我们应采用 **流式布局** 。这意味着我们的单列布局本身具有弹性，可以自动适应从375px到更大手机屏幕的宽度变化，而不会“断裂”。
  * **c) 技术澄清:** 从技术上讲，我们将在代码中为 **所有组件编写基础的、移动端（`$sm`）的样式** 。然后，仅在需要时，才为更大的断点（如 `$md`及以上）添加 **覆盖（override）样式** 。

#### **2. 关于屏幕方向的补充 (An Addition Regarding Screen Orientation)**

* **可以更进一步思考的点:** 我们目前只讨论了屏幕的宽度，但没有定义应用在**横屏（Landscape）**模式下的行为。
* **改进建议:**
  * **a) 优先优化竖屏:** 为了聚焦MVP的核心价值并控制开发范围，我建议我们明确规定：“在MVP阶段，‘食光机’将 **主要针对竖屏（Portrait）模式进行优化** 。” 这是绝大多数用户使用此类应用的主要方式。
  * **b) 保证横屏可用性:** 对于横屏模式，我们的要求是 **保证可用性，但不追求完美布局** 。即界面元素应能正常显示，不会发生重叠、遮挡或崩溃，但我们不投入额外的精力去为横屏专门设计一套新的布局。

## **9. 动画与微交互 (Animation & Micro-interactions)**

**本节旨在定义应用的动态设计原则和关键的交互效果。我们的核心理念是：**

 **动画是为功能服务的，而非单纯的装饰** **。它必须有明确的目的，同时要充分考虑性能和无障碍设计。 **

### **动态设计原则 (Motion Principles)**

1. **目的性 (Purposeful):** 所有动画都必须有明确的目的，例如：引导用户注意力、提供清晰的状态反馈、或增强交互的物理隐喻。我们将杜绝任何无意义的、纯装饰性的动画。
2. **快速响应 (Fast & Responsive):** 动画必须是快速且可中断的，绝不能阻碍或拖慢用户的操作流程。所有微交互的动画时长应严格控制在**300毫秒**以内。
3. **尊重无障碍设计 (Accessible):** 我们需要确保动画效果不会给特殊用户群体带来困扰。应用应尊重系统级的“减少动态效果”设置。

### **MVP阶段的关键动画 (Key Animations for MVP)**

根据我们之前“克制地使用动画”的风险缓解策略，在MVP阶段，我们将只聚焦于以下几个最高价值的微交互：

* **状态过渡 (State Transitions):**

  * **描述:** 按钮、输入框等可交互组件，在不同状态（如按下、聚焦、禁用）之间切换时，应有平滑的、不易察觉的过渡效果，而不是生硬的瞬变。
* **加载反馈 (Loading Feedback):**

  * **描述:** 即我们之前定义的、品牌化的、可被API响应立即中断的加载动画。
* **操作确认 (Action Confirmation):**

  * **描述:** 当用户完成一个关键操作后（例如“收藏食谱”成功），给予一个短暂、积极的视觉反馈。例如，心形图标会有一个微妙的“跳动”或“填满”效果。
* **内容呈现动画 (Content Entrance Animation):**

  * **描述:** 当食谱成功生成并展示时，页面上的元素（如标题、图片、食材列表）可以有一个**极其快速、微妙的、依次入场**的效果（例如 Staggered Fade-in）。
  * **理由:** 这个动画的时长必须极短（例如，总共不超过400-500毫秒）。它的目的不是为了炫技，而是为了 **引导用户的视线自然地从上到下扫过整个食谱** ，并极大地增强食谱“魔法般出现”的感觉。这是一个能显著提升我们产品核心魅力时刻的高性价比投资。

*(注：除此之外的任何复杂场景过渡动画或装饰性动画，在MVP阶段都将不被考虑。)*

## **10. 性能考量 (Performance Considerations)**

本节旨在定义明确的性能目标和策略，以确保“食光机”应用不仅美观、易用，而且快速、流畅、响应迅速。

### **性能目标 (Performance Goals)**

* **应用启动速度:** 首次安装后的加载时间应在**3秒**以内，后续的“热”启动应在**1.5秒**以内。
* **交互响应速度:** 所有用户交互（如点击按钮）的视觉反馈，必须在**100毫秒**内发生，以创造“即时”的感受。
* **动画流畅度:** 所有动画必须稳定在 **60帧/秒 (FPS)** ，不能出现可感知的掉帧或卡顿。
* **核心API响应:** 95%的食谱生成请求的后端响应时间，应在**5秒**以内。

### **达成性能目标的设计策略 (Design Strategies)**

我们将采用以下策略，来确保设计和开发能够满足上述性能目标：

* **异步加载:** 应用主页将采用异步加载策略，优先渲染核心的“食材输入”功能，然后再加载“最近收藏”等非核心的个性化模块。
* **资源优化:** 严格控制应用的打包体积。所有图片、Lottie动画等资源文件都必须经过极致压缩。
* **动画克制:** 严格遵循第9节中定义的动画范围和时长限制，并强制使用高性能的动画库（如 `<span class="citation-255">React Native Reanimated</span>`）。
* **后端缓存:** 积极利用后端缓存策略，以减少不必要的、耗时较长的AI API调用。
