# **项目简报: AI食谱生成器**

**版本:** 1.0
**日期:** 2025年8月8日
**作者:** Mary, 商业分析师

### **1. 执行摘要**

本项目旨在创建一款名为“AI食谱生成器”的移动端优先应用程序。其核心功能是利用人工智能，根据用户输入的现有食材，动态生成创意食谱。该应用旨在解决“我用现有的东西能做什么菜？”这一日常难题，通过提供即时、个性化的烹饪建议，帮助用户减少食物浪费、激发烹饪灵感并节省决策时间。应用将使用Tamagui框架构建，确保在iOS和Android设备上提供统一且响应迅速的用户体验。

### **2. 问题陈述**

许多人在打开冰箱时都面临着同样的困境：里面有各种各样的食材，但却不知道如何将它们组合成一顿美味的饭菜。这种情况导致了以下几个问题：

* **决策疲劳** : 在一天工作后，还要花精力去思考和搜索食谱，这让人感到疲惫。
* **食物浪费** : 由于不知道如何使用某些食材，它们最终被丢弃，造成了经济和环境上的浪费。
* **烹饪单调** : 人们倾向于一遍又一遍地做自己熟悉的几道菜，缺乏烹饪的多样性和乐趣。
* **现有方案的不足** : 传统的食谱网站和应用大多基于搜索，用户需要先有明确的目标（比如“搜索番茄炒蛋”），而不能很好地解决从已有食材出发的探索性问题。

### **3. 提议的解决方案**

我们将开发一个直观的移动应用，让用户可以轻松输入他们拥有的食材。应用的人工智能引擎将分析这些食材，并立即生成一份或多份包含烹饪步骤、所需时间和风味描述的完整食谱。

* **核心理念** : 从“库存”到“餐桌”的无缝转换。
* **关键差异化** :
* **AI驱动的创造力** : 不再是固定的数据库查询，而是能够创造出独特、甚至是用户从未想过的食谱组合。
* **个性化** : 应用将允许用户设置饮食偏好（如素食、无麸质）和技能水平，以生成更合适的食谱。
* **减少浪费** : 通过优先利用用户已有的食材，直接为减少食物浪费做出贡献。

### **4. 目标用户**

* **主要用户群体：忙碌的专业人士**
  * **特点** : 时间宝贵，注重健康，希望快速做出家庭烹饪的决定，但缺乏规划的时间和精力。
  * **痛点** : 经常因不知道做什么而点外卖，希望更有效地利用家中食材。
* **次要用户群体：学生和预算有限的烹饪者**
  * **特点** : 预算有限，食材种类不多，需要有创造性地利用现有资源。
  * **痛点** : 食谱选择受限，希望用有限的食材做出多样化的饭菜。

### **5. 目标与成功指标**

* **业务目标** :
* 在产品发布后的6个月内，实现10,000名月度活跃用户（MAU）。
* 在第一年内，通过可选的订阅服务（例如，高级食谱功能）实现初步盈利。
* **用户成功指标** :
* 普通用户每周至少生成并保存3个食谱。
* 用户报告称，自使用该应用以来，每周的食物浪费平均减少20%。
* **关键绩效指标 (KPIs)** :
* 日活跃用户 (DAU) / 月活跃用户 (MAU)
* 用户留存率（第1天，第7天，第30天）
* 每次会话的食谱生成次数
* 食谱保存率

### **6. MVP 范围**

为了尽快验证核心价值并收集用户反馈，第一个版本（MVP）将专注于以下功能：

* **核心功能 (包含在MVP内)** :
* **手动食材输入** : 用户可以通过文本输入或选择标签来添加食材。
* **核心AI食谱生成** : 基于输入的食材生成包含名称、配料表和步骤的单个食谱。
* **简单的用户偏好设置** : 允许用户选择一些基本的饮食偏好（例如，素食、不吃辣）。
* **保存食谱** : 用户可以将喜欢的食谱保存到个人收藏夹。
* **范围之外的功能 (MVP之后考虑)** :
* 通过摄像头扫描食材的图像识别功能。
* 自动生成购物清单。
* pantry（食品储藏室）管理功能。
* 食谱的社交分享和社区功能。
* 高级搜索和过滤（例如，按烹饪时间、卡路里等）。

### **7. 后MVP阶段的愿景**

* **第二阶段** : 引入通过照片识别食材的功能，集成购物清单，并提供更高级的个性化选项（例如，排除不喜欢的食材）。
* **长期愿景** : 发展成为一个完整的厨房助手，包括膳食规划、智能食品储藏室管理，以及与智能厨房电器的集成。

### **8. 技术考量 (已根据你的决策最终确定)**

* **平台要求** : 专注于移动端 MVP，支持 iOS 和 Android。
* **技术栈 (MVP阶段)** :
* **前端** : `React Native` (使用 `Tamagui` UI库)。
* **后端** : `Spring Boot`。
* **数据库** : `PostgreSQL` (主数据库) + `Redis` (缓存)。
* **AI引擎** : 支持调用第三方大语言模型API，优先考虑中国主流模型（如智谱AI GLM、阿里通义千问、DeepSeek），同时兼容Google Gemini和OpenAI GPT系列作为补充。
* **部署** : `Docker` + `Nginx`。
* **架构考量** :
* **初期架构** : 采用  **模块化的单体架构** ，为未来向微服务迁移预留接口。
* **演进策略** : 待产品进入成长期（用户量10万+，团队10人+），再启动微服务迁移。
* **限制** :
* 初始预算有限，团队规模较小。
* MVP的开发周期目标为3-4个月。
* **关键假设** :
* 第三方AI模型的生成质量足以满足用户对食谱准确性和创造性的基本要求。
* 用户愿意手动输入食材以换取高质量的食谱建议。
* 应用的核心功能对用户具有足够的吸引力，能够实现自发的口碑传播。

### **10. 风险与开放性问题**

* **主要风险** :
* **AI食谱质量** : AI生成的食谱可能不准确、不安全或难以理解。
  * **缓解措施** : 在应用中加入明确的免责声明，并设立用户反馈机制，允许用户标记有问题的食谱。
* **API成本** : AI模型API的调用成本可能随着用户增长而迅速增加。
  * **缓解措施** : 在MVP阶段对用户进行速率限制，优化API调用提示以降低成本。
* **开放性问题** :
* 如何设计AI提示（Prompt Engineering），才能确保生成既安全又富有创意的食谱？
* 如何处理食材的同义词和不同形态（例如，“番茄” vs “圣女果”）？
* 用户隐私，特别是关于他们饮食习惯的数据，将如何得到保护？

### **11. 后续步骤**

* **立即行动** :

1. 与产品经理（PM）一起审查并完善这份项目简报。
2. 基于此简报，开始创建详细的产品需求文档（PRD）。

* **产品经理交接** :
  这份项目简报为“AI食谱生成器”项目提供了全面的背景和基础。请产品经理（PM）进入“PRD生成模式”，彻底审查本简报，并与用户一起逐节创建PRD，澄清任何必要的问题或提出改进建议。

### **12. 设计与用户体验哲学**

为了确保产品具有独特的品牌感和优秀的用户体验，避免当前AI应用普遍存在的“模板化”和“廉价感”，我们确立以下核心设计原则：

#### **A. 核心策略：规避“一眼AI”**

1. **拒绝模板布局** : 引入留白、不对称布局、浮动卡片等真实设计手法，打破AI偏好的严格对称栅格。
2. **打造品牌视觉** :

* **字体** : 采用符合品牌语境的字体体系（如“阿里巴巴普惠体”或“思源”系列），而非系统默认字体。
* **色彩** : 制定一套独特的5-7色主题色板，体现“食物”和“健康”的主题（例如：浅米白、植物绿、健康橙、番茄红、木色调），而不是使用 `Tailwind`的默认色。

1. **增加人工痕迹** :

* **图标** : 混合使用来自不同库（如 `Tabler Icons` + `Line Awesome`）的图标，并加入品牌定制或插图式元素，避免单一图标库带来的廉价感。
* **插图** : 在引导页、空状态等位置使用 `Open Peeps`、`Blush` 等风格化插图，营造“手工”和“治愈”的氛围。

1. **注重交互细节** :

* **文案** : 按钮和提示文案需结合业务语境，例如使用“开始烹饪”而非“提交”。
* **动效** : 增加精细的微动画、hover效果和点击反馈，模拟真实的用户交互行为。
* **加载体验** : 使用模仿产品真实加载过程的自定义骨架屏（Skeleton Screen）。

#### **B. 推荐UI技术栈 (React Native)**

* **UI库** : `Tamagui` (风格温和、可定制性强)。
* **样式体系** : `Tailwind CSS` (通过 `NativeWind` 实现)。
* **动画** : `Framer Motion` 或 `React Native Reanimated`。
