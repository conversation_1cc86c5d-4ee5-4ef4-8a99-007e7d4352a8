# **AI食谱生成器 产品需求文档 (PRD)**

## **1. 目标与背景上下文**

### **目标**

* 开发一款移动端优先的应用，利用AI根据用户现有食材生成食谱。
* 通过提供即时、富有创意的烹饪建议，帮助用户减少食物浪费。
* 提供个性化的用户体验，允许用户根据饮食偏好定制食谱。
* 打造一款具有独特“人情味”设计风格的应用，避免通用AI应用的廉价感。

### **背景上下文**

**本项目旨在解决一个普遍的日常难题：消费者常常面对冰箱里零散的食材，却因不知如何烹饪而感到困扰，最终导致食物浪费和单调的饮食。现有食谱应用多为搜索导向，无法有效利用用户已有的资源。 **^^

**我们的解决方案是一款AI驱动的应用，它能将用户手头的“库存”智能地转化为“餐桌”上的美味。用户输入食材后，AI会生成包含详细步骤的完整食谱。这不仅节省了用户的决策时间，激发了烹饪乐趣，还直接解决了食物浪费的问题，为用户创造了实实在在的价值。 **^^

## **2. 需求**

### **功能性需求 (Functional Requirements)**

* **FR1** : 用户能够通过文本输入界面手动添加他们拥有的食材。
* **FR1.1** : 提供一个文本输入框，用户可以输入单个或多个食材，应用需能智能识别（例如，通过逗号、空格或换行符进行分割）。
* **FR1.2** : 在输入框下方提供一个动态的食材列表，清晰地展示用户已添加的食材，并允许用户移除单个食材。
* **FR2** : 应用能够基于用户输入的食材，调用AI大语言模型，生成一份包含标题、所需食材列表和烹饪步骤的完整食谱。
* **FR2.1** : 生成的食谱必须包含：预估的烹饪时间、难度等级（如：简单/中等/困难）、详细的食材用量建议和分步的烹饪指南。
* **FR2.2** : 如果输入的食材组合无法生成合理的食谱，应用应返回友好提示，如‘这些食材太有挑战性啦，换一换或者多加点试试？’，而不是返回错误或空白。
* **FR3** : 用户能够将生成的食谱保存到个人的“收藏夹”中以便将来查看。
* **FR3.1** : 用户可以将生成的食谱保存到其个人账户的‘收藏夹’中。**（依赖：FR6 - 核心用户账户系统）**
* **FR4** : 用户能够在设置中选择并保存其饮食偏好（例如：素食、无麸质、不吃辣），AI在生成食谱时会考虑这些偏好。
* **FR5** : 在MVP阶段，应用将**优先集成一种**经过验证的主力大语言模型API（例如，智谱GLM-4.5）。后端需设计成可扩展的模块化接口，以便未来能平滑地接入或切换到其他模型。
* **FR6** ：用户能够通过手机号或社交媒体账号完成注册和登录。

### **非功能性需求 (Non-Functional Requirements)**

* **NFR1** : 应用必须采用移动端优先的设计，确保在主流iOS和Android设备上提供流畅的响应式体验。
* **NFR2** : 应用的UI/UX必须遵循我们已确立的“设计与用户体验哲学”，规避“一眼AI”的模板感，营造“人情味”和品牌感。
* **NFR3** : 后端服务需基于Spring Boot框架构建，并使用PostgreSQL作为主数据库，Redis作为缓存。
* **NFR4** : 对AI模型的API调用必须进行优化，以平衡生成质量、响应时间和运营成本。
* **NFR4.1** : 95%的食谱生成请求的后端处理时间应在5秒以内。
* **NFR4.2** : 应用需设计缓存策略，对于完全相同的食材组合，在短时间内（例如1小时内）的重复请求应直接返回缓存结果，以降低API成本。
* **NFR5** : 应用需要考虑用户的便捷操作，交互流程应尽可能简化、直观。
* **NFR5.1** : 新用户从打开应用到成功生成第一份食谱的平均操作时间应少于60秒。
* **NFR5.2** : 核心操作（添加食材、生成食谱）的步骤不应超过3步。

## **3. 用户界面设计目标 (User Interface Design Goals)**

本节旨在捕捉产品的宏观UI/UX愿景，以指导后续的设计和开发工作。

### **整体UX愿景 (Overall UX Vision)**

我们的目标是创造一个温暖、治愈、以人为中心的界面，让“食光机”感觉像一个富有创造力的厨房伴侣，而非一个冰冷、无趣的技术工具。核心是 **规避“一眼AI”的模板感** ，打造独特的品牌体验。我们可以将这个愿景分解为几个可执行的  **核心设计原则** ，让它变得可衡量、可落地：

1. **清晰第一 (Clarity First):** 交互和信息展示必须清晰易懂，绝不为了美学而牺牲清晰度。
2. **减少认知负荷 (Reduce Cognitive Load):** 界面只展示当前任务所必需的信息和操作，通过“渐进式披露”来引导用户。
3. **提供满足感的反馈 (Provide Satisfying Feedback):** 用户的每一次点击、滑动都应有明确、愉悦的视觉或触觉反馈，让应用感觉“活”起来。

### 反‘AI模板感’的设计策略

* **增加人工痕迹:** 通过混合搭配图标库、使用风格化插图来增加界面的“手工感”。
* **细节打破规范:** 按钮文案、输入框提示等都将结合真实业务语境，体现“人用过”的感觉。
* **提升真实感:** 通过自定义加载动画（骨架屏）、微动画、hover效果和点击反馈，模拟真实、流畅的用户交互行为。
* **打破对称:** 引入留白、不对称布局、错位层级等设计手法，减少“完美=AI生成”的既视感。

### **核心屏幕与视图 (Core Screens and Views)**

为了实现核心用户流程，MVP阶段将至少包含以下几个核心界面：

* **食材输入页:** 用户旅程的起点，旨在实现快速、无错的食材录入。**（此为应用启动后的主界面）**
* **食谱展示页:** 清晰、美观地展示AI生成的食谱，并引导用户进行下一步操作（如收藏或返回）
* “我的收藏夹”页
* 用户设置页（用于管理饮食偏好等）
* 注册/登录页

### **无障碍设计 (Accessibility)**

* **标准:** 默认以 **WCAG AA** 作为我们的可访问性标准，确保应用对所有用户都友好。
* 我们可以定义MVP阶段的 **核心无障碍目标** ：在MVP阶段，我们将优先确保在以下三个方面达到WCAG AA标准：
* 1) **色彩对比度** ，确保所有文本都清晰可读；
* 2) **核心流程的键盘导航** ，用户可以只通过键盘完成一次食谱生成；
* 3) **核心流程的屏幕阅读器支持** 。

### **品牌与风格 (Branding)**

* **字体:** 采用符合品牌语境的字体体系（如“阿里巴巴普惠体”或“思源”系列）。
* **色彩:** 制定一套结合“食物感”的主题色板，以浅米白、植物绿、健康橙、番茄红、木色调为主。

### **目标设备与平台 (Target Device and Platforms)**

* **平台:** 跨平台移动应用，支持主流的 **iOS** 和 **Android** 设备。

## 4. 技术假设

### **代码仓库结构 (Repository Structure)**

* **已确定:** **Monorepo (单一代码库)**
* **理由:** 你已确认这是合理的，因为它非常适合前后端需要紧密协作、同频发布的项目。通过在单一代码库中共享代码（如类型定义、API契约）和统一CI/CD流程，可以显著提高开发效率和一致性，特别适合我们当前的团队规模和项目耦合度。为有效管理此Monorepo，我们将采用如 `Nx` 或 `Turborepo` 这样的现代化构建系统。

### **服务架构 (Service Architecture)**

* **已确定:** **模块化单体架构 (Modular Monolith)**
* **理由:** 在MVP阶段，这能保证快速的开发和部署效率，同时清晰的模块化设计为未来可能向微服务的演进做好了准备。模块之间必须通过定义好的API接口进行通信，严禁跨模块直接调用内部实现或共享数据库表。

### **测试要求 (Testing Requirements)**

* **已确定:** **单元测试 + 集成测试 (Unit + Integration)**
* **理由:** 你已确认这是MVP阶段性价比最高的组合。它遵循了“测试金字塔”的最佳实践：大量的单元测试保证快速反馈，而关键路径的集成测试则确保整个系统能够协同工作。我们同意在MVP阶段不引入复杂的端到端（E2E）测试，在MVP之后的版本中引入E2E自动化测试框架（如Detox或Maestro）。MVP阶段的集成测试，应专注于验证从API端点到数据库的、无需真实AI调用的用户核心流程（例如：用户注册、获取食材列表、保存食谱等）。对AI模型的API调用应在测试中被模拟(Mock)。

### **其他技术假设与选型 (Additional Technical Assumptions and Requests)**

* **前端:** React Native + Tamagui
* **后端:** Spring Boot
* **数据库:** PostgreSQL + Redis
* **AI引擎:** 优先集成中国主流大语言模型（智谱GLM等），并保持对其他模型（Gemini, GPT）的兼容性。
* **部署:** Docker + Nginx

## **5. 史诗列表 (Epic List)**

以下是我们为“食光机”MVP规划的史诗，它们严格遵循了逻辑上的先后顺序：

* **Epic 1: 项目地基与功能“冒烟测试” (Foundation & Feature "Smoke Test")**

  * **目标:** 搭建起完整的项目基础、工具链和用户服务，并实现一个最简化的“单食材输入 -> 简单食谱输出”的端到端流程，以验证整个技术链路的可行性。

  **风险** : **“冒烟测试”范围蠕变 (Scope Creep)。** “冒烟测试”是一个技术术语，但团队可能会不自觉地想让这个“用户可见”的第一个功能变得更完善、更美观，从而投入过多精力。这会让它从一个简单的技术验证，膨胀成一个“迷你MVP”，最终导致我们最初担心的“地基”过大的问题重现，延误了核心价值（Epic 2）的开发启动。

  * **缓解策略** :
  * **a) 设定极其严格的验收标准:** 我们必须在为这个“冒烟测试”功能编写用户故事时，明确其验收标准。例如：“1. 界面可以是未经美化的纯功能性UI；2. 只需支持输入一个预设的食材（如‘番茄’）；3. 必须完成一次真实的前后端及AI服务的完整调用；4. 只需在界面上展示返回的原始文本即可。” 这将明确其**技术验证**的目标，而非用户体验目标。
  * **b) 使用特性开关 (Feature Flag):** 我们可以将这个“冒烟测试”功能置于一个特性开关之后，使其只对内部团队可见。这样就避免了过早向用户展示一个不完善功能的压力。

  **风险** : **“冒烟测试”的代码被废弃。** 存在这样一种风险：团队在Epic 1中为“冒烟测试”编写的前后端代码，在Epic 2开发完整功能时，被完全推翻和重写，造成了不必要的浪费。

  * **缓解策略** :
  * **a) 架构先行，逻辑简化:** 这是最重要的缓解措施。我们在Epic 1中构建的 **技术架构** （如AI服务封装、后端API接口、前端组件结构）必须是**最终版本**的架构。Epic 1中的“冒烟测试”只是让一个非常简单的“测试数据包”在这个最终的“管道”里跑一遍。
  * **b) 明确为“增强”而非“重写”:** 在Epic 2的故事描述中，应明确指出是在Epic 1已有代码的 **基础之上进行增强** ，而不是重写。例如，“增强食材输入组件，使其支持多食材输入” 而不是 “创建一个食材输入组件”。
* **Epic 2: 完整核心体验与收藏功能 (Full Core Experience & Save Feature)**

  * **目标:** 在“冒烟测试”的基础上，扩展为功能完善的“多食材输入”核心流程，提升AI生成质量，并提供关键的“收藏食谱”功能，形成完整的用户价值闭环。

  **风险** : **工作量被低估。** 这个Epic现在变得非常巨大和关键。它包含了完整的“多食材输入”UI、真正的“提示工程(Prompt Engineering)”、精美的“食谱展示”UI，以及完整的“收藏到账户”逻辑。如果我们低估了其中任何一个环节的复杂性，都可能导致这个Epic的开发周期远超预期，迟迟无法向用户交付一个完整的价值闭环。

  * **缓解策略** :
  * **a) 进一步拆分用户故事 (User Stories):** 在进入Epic 2的开发前，我们必须将其仔细地拆分为更小的、可管理的用户故事。例如，“收藏食谱”本身就可以是一个独立的故事，“多食材输入UI”是另一个。
  * **b) 对AI提示工程进行时间盒 (Time-box):** 我们可以为AI提示的初步优化工作设定一个明确的时间限制（例如，不超过X天）。达到“足够好”就应该先上线，而不是追求“完美”。
* **Epic 3: 高级个性化 (Advanced Personalization)**

  * **目标:** 通过引入“饮食偏好设置”等高级功能，为用户提供更深度的个性化体验，提升长期用户粘性。

## **6. 史诗详情 (Epic Details)**

我们将从第一个史诗开始，再次确认其内部的用户故事和验收标准。

### **Epic 1: 项目地基、流程工具与核心用户服务**

**目标:** 搭建起整个应用的前后端项目骨架，配置好包括CI/CD和自动化检查在内的关键开发工具，并实现完整的用户账户系统（含游客模式）。最后，通过一个“冒烟测试”功能，我们将首次端到端地验证从用户界面到AI模型的完整技术链路，并确立我们产品的设计基调。

---

以下是构成 Epic 1 的、经过我们详细审议的用户故事：

#### **Story 1.1: 项目初始化与Monorepo环境搭建**

* **作为一个** 开发者, **我想要** 一个配置好的、包含前后端独立应用的Nx Monorepo环境, **以便于** 我能在一个结构清晰、统一管理的环境中高效地开始编码。
* **验收标准:**
  1. 使用Nx工具成功初始化Monorepo。
  2. 在 `apps/`目录下，成功创建 `api` (Spring Boot) 和 `mobile` (React Native) 两个独立的应用程序骨架。
  3. 根目录下的脚本必须包含并能成功运行 `start:api`, `start:mobile`, `lint` 和 `test`。
  4. `README.md` 文件必须包含一个新开发者从零开始就能在本地成功运行前后端应用的完整操作指南。
  5. 所有核心依赖的版本都必须与PRD第4节中已确定的版本保持一致。

#### **Story 1.2: CI/CD流水线与模块化规则建立**

* **作为一个** 开发者, **我想要** 一条基础的、能自动运行测试并强制执行模块边界规则的CI/CD流水线, **以便于** 我提交的每一份代码都能自动地被验证其正确性和架构合规性。
* **验收标准:**
  1. 已配置一个基础的CI/CD流水线文件（例如，GitHub Actions的 `.github/workflows/ci.yml`）。
  2. 每次向代码仓库推送代码时，该流水线会自动执行代码检查 (`lint`) 和单元测试 (`test`)。
  3. 已在Monorepo中配置模块边界规则，以防止产生非法的交叉引用。
  4. 如果代码违反了模块边界规则，CI/CD流水线必须失败并报错。

#### **Story 1.3: 核心用户服务与数据库搭建**

* **作为一个** 系统, **我需要** 一个能够支持游客会话、用户注册和登录的认证服务，并配备相应的数据库支持, **以便于** 应用能够安全地管理用户身份和与用户关联的数据。
* **验收标准:**
  1. 已在PostgreSQL数据库中创建并迁移了 `users` 表的核心结构。
  2. 后端API提供了 `/register`, `/login`, 和 `/session` 的端点。
  3. 系统能够为未登录的用户创建和管理一个临时的游客会话。
  4. QA可以通过一个测试脚本来验证游客会话可以被平滑地转换为正式的用户账户。
  5. 所有API端点都必须以OpenAPI的格式进行文档化，明确请求和响应的数据结构。

#### **Story 1.4: “冒烟测试”功能的设计与端到端实现**

* **作为一个** 产品团队, **我想要** 一个最简化的、能反映我们基础设计原则的端到端“冒烟测试”功能, **以便于** 我们可以首次验证整个技术栈是通畅的，并确立产品的设计基调。
* **验收标准:**
  1. **前置条件:** UX专家必须完成此功能的“迷你设计规范”。
  2. 移动端有一个包含一个文本输入框和一个按钮的界面。
  3. 用户可以在输入框中输入一个预设的食材（例如“番茄”）。
  4. 点击按钮后，前端会调用后端API，后端再调用AI服务封装层。
  5. 界面必须正确地显示从AI返回的完整的、未经解析的原始文本，以验证端到端数据流。
  6. AI API密钥必须通过环境变量加载，绝不能被硬编码，且 `.env`文件必须在 `.gitignore`中。

### **Epic 2: 完整核心体验与收藏功能**

**扩展目标:** 这个史诗的目标是实现产品的核心价值闭环。我们将基于Epic 1的“冒烟测试”，构建一个功能完善、体验流畅的多食材输入界面，并升级后端AI逻辑以生成更高质量的、融入用户基础个性化偏好的食谱。最终，用户将能将他们喜爱的食谱一键收藏到个人账户中，完成一次完整、满意且有粘性的用户旅程。

---

以下是构成 Epic 2 的用户故事：

#### **Story 2.1: 基础个性化功能的后端支持**

* **作为一个** 系统,
* **我需要** 提供API来管理用户的核心饮食偏好（如过敏原），并将这些偏好整合进AI指令中,**以便于** 我能为有特殊饮食需求的用户生成更安全、更相关的食谱。注：产品经理需要提供一份明确的、MVP范围内的‘核心饮食偏好’列表（例如：`过敏原:花生`, `过敏原:海鲜`, `饮食类型:素食`），并定义每种偏好如何转化为具体的AI指令。
* **验收标准:**
  1. 在数据库中创建 `user_preferences`表，并与 `users`表正确关联。
  2. 后端提供API端点（例如 `/api/users/me/preferences`），允许获取和更新用户的偏好设置。
  3. 核心食谱生成逻辑在构建AI指令(Prompt)时，会获取并包含用户的饮食偏好（例如，明确指示AI“不要包含以下成分：花生、海鲜”）。
  4. 后端保存用户偏好时，应使用预定义的键值对格式（例如 `allergy_peanuts: true`, `diet_vegetarian: true`），以确保数据结构的一致性。

#### **Story 2.2: 多食材输入界面的实现**

* **作为一个** 用户,
* **我想要** 一个带自动补全的、标签式的输入界面来添加多种食材,**以便于** 我能快速、准确地告诉应用我厨房里都有些什么。
* **验收标准:**
  1. 应用主界面从“冒烟测试”的单一输入框，升级为标签式（Tag-based）输入字段。
  2. 当用户输入时，下方会根据食材API返回的数据，显示一个自动补全的建议列表。
  3. 用户可以选择或输入多个食材，每个食材会显示为一个可随时移除的标签。
  4. 只有当至少有一个食材被添加后，“生成食谱”按钮才变为可用状态。
  5. 如果用户输入的文本在自动补全列表中不存在，用户**依然可以**将其作为一个自定义标签添加到输入列表中。
* 注：对于用户输入的自定义食材，后端在首次调用AI时会直接使用。同时，系统应在后台记录这些自定义食材，以便产品团队未来考虑是否将其加入标准化食材数据库。

#### **Story 2.3: 完整食谱的生成与精美展示**

* **作为一个** 用户,
* **我想要** 提交我的食材列表后，能看到一个排版精美、易于阅读的食谱,**以便于** 我能获得烹饪灵感，并能立刻开始动手制作。
* **验收标准:**
  1. 点击“生成食谱”按钮后，会带着标准化的食材列表调用后端API。
  2. 在等待API返回时，界面会显示一个符合我们设计哲学的加载动画。
  3. 从API返回的食谱数据会被解析，并以结构化的格式（标题、食材列表、步骤）清晰地展示出来。
  4. 整个界面的视觉风格（字体、颜色、布局）严格遵循我们已确立的“人情味”设计原则。

#### **Story 2.4: 实现核心的“收藏食谱”功能**

* **作为一个** 用户,
* **我想要** 在食谱页面上有一个“收藏”按钮，可以把当前的食谱加入我的个人收藏夹,**以便于** 我可以方便地找到并再次使用我喜欢的食谱。
* **验收标准:**
  1. 在食谱展示界面有一个清晰的“收藏”或心形图标按钮。
  2. 如果用户是游客，点击“收藏”会友好地提示并引导其登录或注册。
  3. 对于已登录用户，点击“收藏”会向后端发送请求，将食谱与该用户账户关联。
  4. 按钮的视觉状态会提供明确的反馈（例如，从空心变为实心，或从“收藏”变为“已收藏”）。
  5. 用户可以在一个新的“我的收藏”页面（MVP阶段可以是一个简单的列表）看到所有已收藏的食谱。

### **Epic 3: 高级个性化 (Advanced Personalization)**

**扩展目标:** 这个史诗的目标是在用户已经能体验到产品核心价值的基础上，提供更深度的个性化选项，让“食光机”从一个“有用的工具”向一个“懂我的智能伙伴”转变。我们将通过引入高级饮食偏好，让食谱推荐更贴合用户的健康和生活方式目标，从而进一步提升用户粘性和长期价值。

---

以下是构成 Epic 3 的用户故事：

#### **Story 3.1: 高级偏好设置的用户界面**

* **作为一个** 用户,
* **我想要** 在我的设置中有一个专门的区域，可以指定我的饮食目标（如低碳水）或口味偏好（如喜欢川菜）,**以便于** 应用生成的食谱能更符合我的生活方式和口味。
* 注：产品经理需要提供一份明确的、MVP范围内的‘高级饮食偏好’和‘菜系偏好’的 **初始列表** 。在MVP阶段，这个列表可以在前端硬编码，但后端API的设计应支持未来能从服务器动态获取该列表，以实现灵活配置。
* **验收标准:**

  1. 在用户的个人设置页面，增加一个名为“高级饮食偏好”的新区域。
  2. 该界面提供一个允许用户从**产品经理提供的**预设列表中选择的饮食目标列表（例如：`低碳水`, `高蛋白`, `生酮饮食`），用户可以单选。
  3. 该界面提供一个允许用户从**产品经理提供的**预设列表中选择的菜系偏好列表（例如：`川菜`, `意大利菜`, `墨西哥菜`），用户可以多选。
  4. 用户的选择可以通过API调用，成功地保存到后端。
* **a) 采用渐进式披露 (Progressive Disclosure) 设计:** 我们必须明确要求UX专家在设计此页面时，采用渐进式披露的原则。例如，“高级饮食偏好”可以先显示为一个收起的选项，用户点击后才展开具体的饮食目标列表。重要的选项在前，次要的在后，而不是将所有东西一股脑地平铺给用户。
* **b) 提供清晰的解释:** 每个偏好选项旁边，都应该有一个小的提示图标或简短的说明文字，解释这个选项将如何影响食谱的生成结果。

#### **Story 3.2: 在AI逻辑中集成高级偏好**

* **作为一个** 系统,
* **我需要** 将用户选择的高级饮食偏好，整合进发送给AI模型的指令(Prompt)中,**以便于** 我生成的食谱能够真正体现用户的个人选择。
* 注：当用户的偏好可能存在冲突时，AI指令(Prompt)中应包含引导性文本，例如：‘请尽力结合这些偏好，如果存在冲突， **优先满足[饮食目标]的要求** 。’ 我们在此明确， **健康/饮食目标的优先级高于风味/菜系偏好** 。
* **验收标准:**

  1. 后端的 `user_preferences`数据结构已扩展，可以存储这些高级偏好。
  2. 核心食谱生成逻辑在执行时，会获取用户选择的这些高级偏好。
  3. 发送给AI模型的指令(Prompt)中，被明确地加入了描述用户偏好的文本（例如：“ **请注意：用户正在进行‘低碳水’饮食，并且偏好‘意大利’风味的菜肴。** ”）。
  4. 自动化测试应能验证：当选择特定偏好（如‘生酮饮食’）时，返回食谱中 **高碳水成分** （如米、面、糖）出现的概率，显著低于未选择该偏好时的结果。QA将进行手动抽样检查以验证食谱的整体相关性。
* **a) 区分“硬约束”和“软偏好”:** 我们的AI指令逻辑需要能区分不同优先级的偏好。例如，**过敏原**是必须100%遵守的“硬约束”，而**菜系**则可以是一个“软偏好”。指令可以这样构造：“`必须严格遵守：[过敏原]`。`尽量满足饮食目标：[低碳水]`。`如果可能，尝试融合以下风味：[意大利菜]`。”
* **b) 允许用户调整“创意度”:** 这是一个可以考虑在未来版本中加入的功能。允许用户自己调节一个“创意度”的滑块，来决定他们想要一个“稳妥安全”的食谱，还是一个“天马行空”的惊喜。
