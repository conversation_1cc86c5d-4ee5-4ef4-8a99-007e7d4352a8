# **Epic 3: 高级个性化 (Advanced Personalization)**

**扩展目标:** 这个史诗的目标是在用户已经能体验到产品核心价值的基础上，提供更深度的个性化选项，让“食光机”从一个“有用的工具”向一个“懂我的智能伙伴”转变。我们将通过引入高级饮食偏好，让食谱推荐更贴合用户的健康和生活方式目标，从而进一步提升用户粘性和长期价值。

---

以下是构成 Epic 3 的用户故事：

## **Story 3.1: 高级偏好设置的用户界面**

* **作为一个** 用户,
* **我想要** 在我的设置中有一个专门的区域，可以指定我的饮食目标（如低碳水）或口味偏好（如喜欢川菜）,**以便于** 应用生成的食谱能更符合我的生活方式和口味。
* 注：产品经理需要提供一份明确的、MVP范围内的‘高级饮食偏好’和‘菜系偏好’的 **初始列表** 。在MVP阶段，这个列表可以在前端硬编码，但后端API的设计应支持未来能从服务器动态获取该列表，以实现灵活配置。
* **验收标准:**

  1. 在用户的个人设置页面，增加一个名为“高级饮食偏好”的新区域。
  2. 该界面提供一个允许用户从**产品经理提供的**预设列表中选择的饮食目标列表（例如：`低碳水`, `高蛋白`, `生酮饮食`），用户可以单选。
  3. 该界面提供一个允许用户从**产品经理提供的**预设列表中选择的菜系偏好列表（例如：`川菜`, `意大利菜`, `墨西哥菜`），用户可以多选。
  4. 用户的选择可以通过API调用，成功地保存到后端。
* **a) 采用渐进式披露 (Progressive Disclosure) 设计:** 我们必须明确要求UX专家在设计此页面时，采用渐进式披露的原则。例如，“高级饮食偏好”可以先显示为一个收起的选项，用户点击后才展开具体的饮食目标列表。重要的选项在前，次要的在后，而不是将所有东西一股脑地平铺给用户。
* **b) 提供清晰的解释:** 每个偏好选项旁边，都应该有一个小的提示图标或简短的说明文字，解释这个选项将如何影响食谱的生成结果。

## **Story 3.2: 在AI逻辑中集成高级偏好**

* **作为一个** 系统,
* **我需要** 将用户选择的高级饮食偏好，整合进发送给AI模型的指令(Prompt)中,**以便于** 我生成的食谱能够真正体现用户的个人选择。
* 注：当用户的偏好可能存在冲突时，AI指令(Prompt)中应包含引导性文本，例如：‘请尽力结合这些偏好，如果存在冲突， **优先满足[饮食目标]的要求** 。’ 我们在此明确， **健康/饮食目标的优先级高于风味/菜系偏好** 。
* **验收标准:**

  1. 后端的 `user_preferences`数据结构已扩展，可以存储这些高级偏好。
  2. 核心食谱生成逻辑在执行时，会获取用户选择的这些高级偏好。
  3. 发送给AI模型的指令(Prompt)中，被明确地加入了描述用户偏好的文本（例如：“ **请注意：用户正在进行‘低碳水’饮食，并且偏好‘意大利’风味的菜肴。** ”）。
  4. 自动化测试应能验证：当选择特定偏好（如‘生酮饮食’）时，返回食谱中 **高碳水成分** （如米、面、糖）出现的概率，显著低于未选择该偏好时的结果。QA将进行手动抽样检查以验证食谱的整体相关性。
* **a) 区分“硬约束”和“软偏好”:** 我们的AI指令逻辑需要能区分不同优先级的偏好。例如，**过敏原**是必须100%遵守的“硬约束”，而**菜系**则可以是一个“软偏好”。指令可以这样构造：“`必须严格遵守：[过敏原]`。`尽量满足饮食目标：[低碳水]`。`如果可能，尝试融合以下风味：[意大利菜]`。”
* **b) 允许用户调整“创意度”:** 这是一个可以考虑在未来版本中加入的功能。允许用户自己调节一个“创意度”的滑块，来决定他们想要一个“稳妥安全”的食谱，还是一个“天马行空”的惊喜。
