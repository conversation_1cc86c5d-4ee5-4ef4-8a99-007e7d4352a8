# **3. 用户界面设计目标 (User Interface Design Goals)**

本节旨在捕捉产品的宏观UI/UX愿景，以指导后续的设计和开发工作。

## **整体UX愿景 (Overall UX Vision)**

我们的目标是创造一个温暖、治愈、以人为中心的界面，让“食光机”感觉像一个富有创造力的厨房伴侣，而非一个冰冷、无趣的技术工具。核心是 **规避“一眼AI”的模板感** ，打造独特的品牌体验。我们可以将这个愿景分解为几个可执行的  **核心设计原则** ，让它变得可衡量、可落地：

1. **清晰第一 (Clarity First):** 交互和信息展示必须清晰易懂，绝不为了美学而牺牲清晰度。
2. **减少认知负荷 (Reduce Cognitive Load):** 界面只展示当前任务所必需的信息和操作，通过“渐进式披露”来引导用户。
3. **提供满足感的反馈 (Provide Satisfying Feedback):** 用户的每一次点击、滑动都应有明确、愉悦的视觉或触觉反馈，让应用感觉“活”起来。

## 反‘AI模板感’的设计策略

* **增加人工痕迹:** 通过混合搭配图标库、使用风格化插图来增加界面的“手工感”。
* **细节打破规范:** 按钮文案、输入框提示等都将结合真实业务语境，体现“人用过”的感觉。
* **提升真实感:** 通过自定义加载动画（骨架屏）、微动画、hover效果和点击反馈，模拟真实、流畅的用户交互行为。
* **打破对称:** 引入留白、不对称布局、错位层级等设计手法，减少“完美=AI生成”的既视感。

## **核心屏幕与视图 (Core Screens and Views)**

为了实现核心用户流程，MVP阶段将至少包含以下几个核心界面：

* **食材输入页:** 用户旅程的起点，旨在实现快速、无错的食材录入。**（此为应用启动后的主界面）**
* **食谱展示页:** 清晰、美观地展示AI生成的食谱，并引导用户进行下一步操作（如收藏或返回）
* “我的收藏夹”页
* 用户设置页（用于管理饮食偏好等）
* 注册/登录页

## **无障碍设计 (Accessibility)**

* **标准:** 默认以 **WCAG AA** 作为我们的可访问性标准，确保应用对所有用户都友好。
* 我们可以定义MVP阶段的 **核心无障碍目标** ：在MVP阶段，我们将优先确保在以下三个方面达到WCAG AA标准：
* 1) **色彩对比度** ，确保所有文本都清晰可读；
* 2) **核心流程的键盘导航** ，用户可以只通过键盘完成一次食谱生成；
* 3) **核心流程的屏幕阅读器支持** 。

## **品牌与风格 (Branding)**

* **字体:** 采用符合品牌语境的字体体系（如“阿里巴巴普惠体”或“思源”系列）。
* **色彩:** 制定一套结合“食物感”的主题色板，以浅米白、植物绿、健康橙、番茄红、木色调为主。

## **目标设备与平台 (Target Device and Platforms)**

* **平台:** 跨平台移动应用，支持主流的 **iOS** 和 **Android** 设备。
