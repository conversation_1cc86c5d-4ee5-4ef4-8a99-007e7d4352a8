# **5. 史诗列表 (Epic List)**

以下是我们为“食光机”MVP规划的史诗，它们严格遵循了逻辑上的先后顺序：

* **Epic 1: 项目地基与功能“冒烟测试” (Foundation & Feature "Smoke Test")**

  * **目标:** 搭建起完整的项目基础、工具链和用户服务，并实现一个最简化的“单食材输入 -> 简单食谱输出”的端到端流程，以验证整个技术链路的可行性。

  **风险** : **“冒烟测试”范围蠕变 (Scope Creep)。** “冒烟测试”是一个技术术语，但团队可能会不自觉地想让这个“用户可见”的第一个功能变得更完善、更美观，从而投入过多精力。这会让它从一个简单的技术验证，膨胀成一个“迷你MVP”，最终导致我们最初担心的“地基”过大的问题重现，延误了核心价值（Epic 2）的开发启动。

  * **缓解策略** :
  * **a) 设定极其严格的验收标准:** 我们必须在为这个“冒烟测试”功能编写用户故事时，明确其验收标准。例如：“1. 界面可以是未经美化的纯功能性UI；2. 只需支持输入一个预设的食材（如‘番茄’）；3. 必须完成一次真实的前后端及AI服务的完整调用；4. 只需在界面上展示返回的原始文本即可。” 这将明确其**技术验证**的目标，而非用户体验目标。
  * **b) 使用特性开关 (Feature Flag):** 我们可以将这个“冒烟测试”功能置于一个特性开关之后，使其只对内部团队可见。这样就避免了过早向用户展示一个不完善功能的压力。

  **风险** : **“冒烟测试”的代码被废弃。** 存在这样一种风险：团队在Epic 1中为“冒烟测试”编写的前后端代码，在Epic 2开发完整功能时，被完全推翻和重写，造成了不必要的浪费。

  * **缓解策略** :
  * **a) 架构先行，逻辑简化:** 这是最重要的缓解措施。我们在Epic 1中构建的 **技术架构** （如AI服务封装、后端API接口、前端组件结构）必须是**最终版本**的架构。Epic 1中的“冒烟测试”只是让一个非常简单的“测试数据包”在这个最终的“管道”里跑一遍。
  * **b) 明确为“增强”而非“重写”:** 在Epic 2的故事描述中，应明确指出是在Epic 1已有代码的 **基础之上进行增强** ，而不是重写。例如，“增强食材输入组件，使其支持多食材输入” 而不是 “创建一个食材输入组件”。
* **Epic 2: 完整核心体验与收藏功能 (Full Core Experience & Save Feature)**

  * **目标:** 在“冒烟测试”的基础上，扩展为功能完善的“多食材输入”核心流程，提升AI生成质量，并提供关键的“收藏食谱”功能，形成完整的用户价值闭环。

  **风险** : **工作量被低估。** 这个Epic现在变得非常巨大和关键。它包含了完整的“多食材输入”UI、真正的“提示工程(Prompt Engineering)”、精美的“食谱展示”UI，以及完整的“收藏到账户”逻辑。如果我们低估了其中任何一个环节的复杂性，都可能导致这个Epic的开发周期远超预期，迟迟无法向用户交付一个完整的价值闭环。

  * **缓解策略** :
  * **a) 进一步拆分用户故事 (User Stories):** 在进入Epic 2的开发前，我们必须将其仔细地拆分为更小的、可管理的用户故事。例如，“收藏食谱”本身就可以是一个独立的故事，“多食材输入UI”是另一个。
  * **b) 对AI提示工程进行时间盒 (Time-box):** 我们可以为AI提示的初步优化工作设定一个明确的时间限制（例如，不超过X天）。达到“足够好”就应该先上线，而不是追求“完美”。
* **Epic 3: 高级个性化 (Advanced Personalization)**

  * **目标:** 通过引入“饮食偏好设置”等高级功能，为用户提供更深度的个性化体验，提升长期用户粘性。
