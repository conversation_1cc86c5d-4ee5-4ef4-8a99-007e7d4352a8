# **2. 需求**

## **功能性需求 (Functional Requirements)**

* **FR1** : 用户能够通过文本输入界面手动添加他们拥有的食材。
* **FR1.1** : 提供一个文本输入框，用户可以输入单个或多个食材，应用需能智能识别（例如，通过逗号、空格或换行符进行分割）。
* **FR1.2** : 在输入框下方提供一个动态的食材列表，清晰地展示用户已添加的食材，并允许用户移除单个食材。
* **FR2** : 应用能够基于用户输入的食材，调用AI大语言模型，生成一份包含标题、所需食材列表和烹饪步骤的完整食谱。
* **FR2.1** : 生成的食谱必须包含：预估的烹饪时间、难度等级（如：简单/中等/困难）、详细的食材用量建议和分步的烹饪指南。
* **FR2.2** : 如果输入的食材组合无法生成合理的食谱，应用应返回友好提示，如‘这些食材太有挑战性啦，换一换或者多加点试试？’，而不是返回错误或空白。
* **FR3** : 用户能够将生成的食谱保存到个人的“收藏夹”中以便将来查看。
* **FR3.1** : 用户可以将生成的食谱保存到其个人账户的‘收藏夹’中。**（依赖：FR6 - 核心用户账户系统）**
* **FR4** : 用户能够在设置中选择并保存其饮食偏好（例如：素食、无麸质、不吃辣），AI在生成食谱时会考虑这些偏好。
* **FR5** : 在MVP阶段，应用将**优先集成一种**经过验证的主力大语言模型API（例如，智谱GLM-4.5）。后端需设计成可扩展的模块化接口，以便未来能平滑地接入或切换到其他模型。
* **FR6** ：用户能够通过手机号或社交媒体账号完成注册和登录。

## **非功能性需求 (Non-Functional Requirements)**

* **NFR1** : 应用必须采用移动端优先的设计，确保在主流iOS和Android设备上提供流畅的响应式体验。
* **NFR2** : 应用的UI/UX必须遵循我们已确立的“设计与用户体验哲学”，规避“一眼AI”的模板感，营造“人情味”和品牌感。
* **NFR3** : 后端服务需基于Spring Boot框架构建，并使用PostgreSQL作为主数据库，Redis作为缓存。
* **NFR4** : 对AI模型的API调用必须进行优化，以平衡生成质量、响应时间和运营成本。
* **NFR4.1** : 95%的食谱生成请求的后端处理时间应在5秒以内。
* **NFR4.2** : 应用需设计缓存策略，对于完全相同的食材组合，在短时间内（例如1小时内）的重复请求应直接返回缓存结果，以降低API成本。
* **NFR5** : 应用需要考虑用户的便捷操作，交互流程应尽可能简化、直观。
* **NFR5.1** : 新用户从打开应用到成功生成第一份食谱的平均操作时间应少于60秒。
* **NFR5.2** : 核心操作（添加食材、生成食谱）的步骤不应超过3步。
