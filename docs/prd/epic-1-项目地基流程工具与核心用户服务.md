# **Epic 1: 项目地基、流程工具与核心用户服务**

**目标:** 搭建起整个应用的前后端项目骨架，配置好包括CI/CD和自动化检查在内的关键开发工具，并实现完整的用户账户系统（含游客模式）。最后，通过一个“冒烟测试”功能，我们将首次端到端地验证从用户界面到AI模型的完整技术链路，并确立我们产品的设计基调。

---

以下是构成 Epic 1 的、经过我们详细审议的用户故事：

## **Story 1.1: 项目初始化与Monorepo环境搭建**

* **作为一个** 开发者, **我想要** 一个配置好的、包含前后端独立应用的Nx Monorepo环境, **以便于** 我能在一个结构清晰、统一管理的环境中高效地开始编码。
* **验收标准:**
  1. 使用Nx工具成功初始化Monorepo。
  2. 在 `apps/`目录下，成功创建 `api` (Spring Boot) 和 `mobile` (React Native) 两个独立的应用程序骨架。
  3. 根目录下的脚本必须包含并能成功运行 `start:api`, `start:mobile`, `lint` 和 `test`。
  4. `README.md` 文件必须包含一个新开发者从零开始就能在本地成功运行前后端应用的完整操作指南。
  5. 所有核心依赖的版本都必须与PRD第4节中已确定的版本保持一致。

## **Story 1.2: CI/CD流水线与模块化规则建立**

* **作为一个** 开发者, **我想要** 一条基础的、能自动运行测试并强制执行模块边界规则的CI/CD流水线, **以便于** 我提交的每一份代码都能自动地被验证其正确性和架构合规性。
* **验收标准:**
  1. 已配置一个基础的CI/CD流水线文件（例如，GitHub Actions的 `.github/workflows/ci.yml`）。
  2. 每次向代码仓库推送代码时，该流水线会自动执行代码检查 (`lint`) 和单元测试 (`test`)。
  3. 已在Monorepo中配置模块边界规则，以防止产生非法的交叉引用。
  4. 如果代码违反了模块边界规则，CI/CD流水线必须失败并报错。

## **Story 1.3: 核心用户服务与数据库搭建**

* **作为一个** 系统, **我需要** 一个能够支持游客会话、用户注册和登录的认证服务，并配备相应的数据库支持, **以便于** 应用能够安全地管理用户身份和与用户关联的数据。
* **验收标准:**
  1. 已在PostgreSQL数据库中创建并迁移了 `users` 表的核心结构。
  2. 后端API提供了 `/register`, `/login`, 和 `/session` 的端点。
  3. 系统能够为未登录的用户创建和管理一个临时的游客会话。
  4. QA可以通过一个测试脚本来验证游客会话可以被平滑地转换为正式的用户账户。
  5. 所有API端点都必须以OpenAPI的格式进行文档化，明确请求和响应的数据结构。

## **Story 1.4: “冒烟测试”功能的设计与端到端实现**

* **作为一个** 产品团队, **我想要** 一个最简化的、能反映我们基础设计原则的端到端“冒烟测试”功能, **以便于** 我们可以首次验证整个技术栈是通畅的，并确立产品的设计基调。
* **验收标准:**
  1. **前置条件:** UX专家必须完成此功能的“迷你设计规范”。
  2. 移动端有一个包含一个文本输入框和一个按钮的界面。
  3. 用户可以在输入框中输入一个预设的食材（例如“番茄”）。
  4. 点击按钮后，前端会调用后端API，后端再调用AI服务封装层。
  5. 界面必须正确地显示从AI返回的完整的、未经解析的原始文本，以验证端到端数据流。
  6. AI API密钥必须通过环境变量加载，绝不能被硬编码，且 `.env`文件必须在 `.gitignore`中。
