# **Epic 2: 完整核心体验与收藏功能**

**扩展目标:** 这个史诗的目标是实现产品的核心价值闭环。我们将基于Epic 1的“冒烟测试”，构建一个功能完善、体验流畅的多食材输入界面，并升级后端AI逻辑以生成更高质量的、融入用户基础个性化偏好的食谱。最终，用户将能将他们喜爱的食谱一键收藏到个人账户中，完成一次完整、满意且有粘性的用户旅程。

---

以下是构成 Epic 2 的用户故事：

## **Story 2.1: 基础个性化功能的后端支持**

* **作为一个** 系统,
* **我需要** 提供API来管理用户的核心饮食偏好（如过敏原），并将这些偏好整合进AI指令中,**以便于** 我能为有特殊饮食需求的用户生成更安全、更相关的食谱。注：产品经理需要提供一份明确的、MVP范围内的‘核心饮食偏好’列表（例如：`过敏原:花生`, `过敏原:海鲜`, `饮食类型:素食`），并定义每种偏好如何转化为具体的AI指令。
* **验收标准:**
  1. 在数据库中创建 `user_preferences`表，并与 `users`表正确关联。
  2. 后端提供API端点（例如 `/api/users/me/preferences`），允许获取和更新用户的偏好设置。
  3. 核心食谱生成逻辑在构建AI指令(Prompt)时，会获取并包含用户的饮食偏好（例如，明确指示AI“不要包含以下成分：花生、海鲜”）。
  4. 后端保存用户偏好时，应使用预定义的键值对格式（例如 `allergy_peanuts: true`, `diet_vegetarian: true`），以确保数据结构的一致性。

## **Story 2.2: 多食材输入界面的实现**

* **作为一个** 用户,
* **我想要** 一个带自动补全的、标签式的输入界面来添加多种食材,**以便于** 我能快速、准确地告诉应用我厨房里都有些什么。
* **验收标准:**
  1. 应用主界面从“冒烟测试”的单一输入框，升级为标签式（Tag-based）输入字段。
  2. 当用户输入时，下方会根据食材API返回的数据，显示一个自动补全的建议列表。
  3. 用户可以选择或输入多个食材，每个食材会显示为一个可随时移除的标签。
  4. 只有当至少有一个食材被添加后，“生成食谱”按钮才变为可用状态。
  5. 如果用户输入的文本在自动补全列表中不存在，用户**依然可以**将其作为一个自定义标签添加到输入列表中。
* 注：对于用户输入的自定义食材，后端在首次调用AI时会直接使用。同时，系统应在后台记录这些自定义食材，以便产品团队未来考虑是否将其加入标准化食材数据库。

## **Story 2.3: 完整食谱的生成与精美展示**

* **作为一个** 用户,
* **我想要** 提交我的食材列表后，能看到一个排版精美、易于阅读的食谱,**以便于** 我能获得烹饪灵感，并能立刻开始动手制作。
* **验收标准:**
  1. 点击“生成食谱”按钮后，会带着标准化的食材列表调用后端API。
  2. 在等待API返回时，界面会显示一个符合我们设计哲学的加载动画。
  3. 从API返回的食谱数据会被解析，并以结构化的格式（标题、食材列表、步骤）清晰地展示出来。
  4. 整个界面的视觉风格（字体、颜色、布局）严格遵循我们已确立的“人情味”设计原则。

## **Story 2.4: 实现核心的“收藏食谱”功能**

* **作为一个** 用户,
* **我想要** 在食谱页面上有一个“收藏”按钮，可以把当前的食谱加入我的个人收藏夹,**以便于** 我可以方便地找到并再次使用我喜欢的食谱。
* **验收标准:**
  1. 在食谱展示界面有一个清晰的“收藏”或心形图标按钮。
  2. 如果用户是游客，点击“收藏”会友好地提示并引导其登录或注册。
  3. 对于已登录用户，点击“收藏”会向后端发送请求，将食谱与该用户账户关联。
  4. 按钮的视觉状态会提供明确的反馈（例如，从空心变为实心，或从“收藏”变为“已收藏”）。
  5. 用户可以在一个新的“我的收藏”页面（MVP阶段可以是一个简单的列表）看到所有已收藏的食谱。
