# 4. 技术假设

## **代码仓库结构 (Repository Structure)**

* **已确定:** **Monorepo (单一代码库)**
* **理由:** 你已确认这是合理的，因为它非常适合前后端需要紧密协作、同频发布的项目。通过在单一代码库中共享代码（如类型定义、API契约）和统一CI/CD流程，可以显著提高开发效率和一致性，特别适合我们当前的团队规模和项目耦合度。为有效管理此Monorepo，我们将采用如 `Nx` 或 `Turborepo` 这样的现代化构建系统。

## **服务架构 (Service Architecture)**

* **已确定:** **模块化单体架构 (Modular Monolith)**
* **理由:** 在MVP阶段，这能保证快速的开发和部署效率，同时清晰的模块化设计为未来可能向微服务的演进做好了准备。模块之间必须通过定义好的API接口进行通信，严禁跨模块直接调用内部实现或共享数据库表。

## **测试要求 (Testing Requirements)**

* **已确定:** **单元测试 + 集成测试 (Unit + Integration)**
* **理由:** 你已确认这是MVP阶段性价比最高的组合。它遵循了“测试金字塔”的最佳实践：大量的单元测试保证快速反馈，而关键路径的集成测试则确保整个系统能够协同工作。我们同意在MVP阶段不引入复杂的端到端（E2E）测试，在MVP之后的版本中引入E2E自动化测试框架（如Detox或Maestro）。MVP阶段的集成测试，应专注于验证从API端点到数据库的、无需真实AI调用的用户核心流程（例如：用户注册、获取食材列表、保存食谱等）。对AI模型的API调用应在测试中被模拟(Mock)。

## **其他技术假设与选型 (Additional Technical Assumptions and Requests)**

* **前端:** React Native + Tamagui
* **后端:** Spring Boot
* **数据库:** PostgreSQL + Redis
* **AI引擎:** 优先集成中国主流大语言模型（智谱GLM等），并保持对其他模型（Gemini, GPT）的兼容性。
* **部署:** Docker + Nginx
