# FoodMagic 环境变量配置模板
# 复制此文件为 .env 并填入实际的配置值

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=foodmagic
DB_USERNAME=foodmagic
DB_PASSWORD=your_database_password_here

# JWT 配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# AI 服务配置
# 选择AI提供商: zhipu (智谱AI) 或 qwen (阿里通义千问)
AI_PROVIDER=zhipu

# AI API密钥（必须配置）
AI_API_KEY=your_ai_api_key_here

# AI API URL（必须配置）
# 智谱AI: https://open.bigmodel.cn/api/paas/v4/chat/completions
# 阿里通义千问: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
AI_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions

# AI 模型配置
# 智谱AI模型: glm-4, glm-3-turbo
# 阿里通义千问模型: qwen-turbo, qwen-plus
AI_MODEL=glm-4

# AI 服务其他配置（可选）
AI_MAX_TOKENS=2000
AI_TEMPERATURE=0.7
AI_TIMEOUT=30000
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1000

# 服务器配置
SERVER_PORT=8080

# CORS 配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,http://localhost:19006

# 管理员账号（开发环境）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin

# 应用环境
APP_ENV=development