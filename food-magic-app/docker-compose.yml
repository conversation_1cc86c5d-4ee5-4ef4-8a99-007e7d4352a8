version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: foodmagic-postgres
    environment:
      POSTGRES_DB: foodmagic
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - foodmagic-network

  redis:
    image: redis:7.2-alpine
    container_name: foodmagic-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis_password
    volumes:
      - redis_data:/data
    networks:
      - foodmagic-network

volumes:
  postgres_data:
  redis_data:

networks:
  foodmagic-network:
    driver: bridge