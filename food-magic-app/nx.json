{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["build"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore"]}}, "workspaceLayout": {"appsDir": "apps", "libsDir": "packages"}, "defaultProject": "mobile"}