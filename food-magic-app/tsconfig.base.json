{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@food-magic-app/shared-types": ["packages/shared-types/src"], "@food-magic-app/api-contract": ["packages/api-contract/generated"], "@food-magic-app/ui": ["packages/ui/src"], "@food-magic-app/eslint-config": ["packages/eslint-config"]}}, "exclude": ["node_modules", "dist", "build", "coverage"]}