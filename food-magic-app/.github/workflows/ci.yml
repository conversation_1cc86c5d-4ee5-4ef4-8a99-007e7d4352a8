name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '20.x'
  JAVA_VERSION: '17'
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

jobs:
  setup-and-lint:
    name: Setup and <PERSON><PERSON>
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: 'gradle'
      
      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            node_modules
            */node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-
      
      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
      
      - name: Install dependencies
        run: npm ci
      
      - name: Lint affected projects
        run: npx nx affected --target=lint --base=origin/main --head=HEAD --parallel=3
      
      - name: Check module boundaries
        run: npx nx affected --target=lint --base=origin/main --head=HEAD --configuration=boundaries

  test:
    name: Test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: setup-and-lint
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: 'gradle'
      
      - name: Restore npm cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            node_modules
            */node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-
      
      - name: Restore Gradle cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run affected tests
        run: npx nx affected --target=test --base=origin/main --head=HEAD --parallel=3 --coverage
      
      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-report
          path: |
            coverage/
            apps/*/coverage/
            packages/*/coverage/
          retention-days: 7

  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [setup-and-lint, test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          cache: 'gradle'
      
      - name: Restore npm cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            node_modules
            */node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-
      
      - name: Restore Gradle cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build affected projects
        run: npx nx affected --target=build --base=origin/main --head=HEAD --parallel=3
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        if: success()
        with:
          name: build-artifacts
          path: |
            dist/
            apps/*/dist/
            packages/*/dist/
            apps/api/build/
          retention-days: 7

  report-status:
    name: Report Status
    runs-on: ubuntu-latest
    if: always()
    needs: [setup-and-lint, test, build]
    
    steps:
      - name: Report pipeline status
        run: |
          if [ "${{ needs.setup-and-lint.result }}" == "failure" ] || \
             [ "${{ needs.test.result }}" == "failure" ] || \
             [ "${{ needs.build.result }}" == "failure" ]; then
            echo "Pipeline failed!"
            echo "Lint status: ${{ needs.setup-and-lint.result }}"
            echo "Test status: ${{ needs.test.result }}"
            echo "Build status: ${{ needs.build.result }}"
            exit 1
          else
            echo "Pipeline succeeded!"
            echo "All checks passed successfully."
          fi