#!/bin/bash

# 冒烟测试端到端验证脚本
# 用于验证完整的端到端数据流

echo "==================================="
echo "   冒烟测试 - 端到端验证脚本"
echo "==================================="
echo ""

# 配置
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/api/v1}"
SMOKE_TEST_ENDPOINT="${API_BASE_URL}/smoke-test"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}✗${NC} $1"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# 函数：执行健康检查
health_check() {
    echo "1. 执行健康检查..."
    response=$(curl -s -w "\n%{http_code}" "${SMOKE_TEST_ENDPOINT}/health")
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        print_success "健康检查通过: $body"
        return 0
    else
        print_error "健康检查失败: HTTP $http_code"
        return 1
    fi
}

# 函数：测试AI响应生成
test_ai_generation() {
    local ingredient="$1"
    echo ""
    echo "2. 测试AI响应生成 (食材: $ingredient)..."
    
    # 准备请求数据
    request_data=$(cat <<EOF
{
    "ingredient": "$ingredient"
}
EOF
)
    
    # 发送请求
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "${SMOKE_TEST_ENDPOINT}/generate")
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        print_success "AI响应生成成功"
        
        # 解析响应
        if command -v jq &> /dev/null; then
            echo ""
            echo "响应详情:"
            echo "$body" | jq '.'
            
            # 提取处理时间
            processing_time=$(echo "$body" | jq -r '.processingTimeMs')
            print_success "处理时间: ${processing_time}ms"
        else
            echo ""
            echo "响应内容 (安装jq以获得格式化输出):"
            echo "$body"
        fi
        return 0
    elif [ "$http_code" = "503" ]; then
        print_warning "AI服务不可用 (可能未配置API密钥)"
        echo "响应: $body"
        return 2
    else
        print_error "请求失败: HTTP $http_code"
        echo "响应: $body"
        return 1
    fi
}

# 函数：测试输入验证
test_validation() {
    echo ""
    echo "3. 测试输入验证..."
    
    # 测试空输入
    echo "   - 测试空输入..."
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"ingredient": ""}' \
        "${SMOKE_TEST_ENDPOINT}/generate")
    
    http_code=$(echo "$response" | tail -n 1)
    
    if [ "$http_code" = "400" ]; then
        print_success "空输入验证通过 (返回400)"
    else
        print_error "空输入验证失败 (期望400，实际$http_code)"
    fi
    
    # 测试超长输入
    echo "   - 测试超长输入..."
    long_ingredient=$(printf 'a%.0s' {1..101})
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "{\"ingredient\": \"$long_ingredient\"}" \
        "${SMOKE_TEST_ENDPOINT}/generate")
    
    http_code=$(echo "$response" | tail -n 1)
    
    if [ "$http_code" = "400" ]; then
        print_success "超长输入验证通过 (返回400)"
    else
        print_error "超长输入验证失败 (期望400，实际$http_code)"
    fi
}

# 函数：测试不同场景
test_scenarios() {
    echo ""
    echo "4. 测试不同输入场景..."
    
    scenarios=("番茄" "土豆" "鸡肉" "牛肉和洋葱" "西红柿、鸡蛋")
    
    for scenario in "${scenarios[@]}"; do
        echo ""
        echo "   测试食材: $scenario"
        
        response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Content-Type: application/json" \
            -d "{\"ingredient\": \"$scenario\"}" \
            "${SMOKE_TEST_ENDPOINT}/generate")
        
        http_code=$(echo "$response" | tail -n 1)
        
        if [ "$http_code" = "200" ] || [ "$http_code" = "503" ]; then
            print_success "场景 '$scenario' 测试通过"
        else
            print_error "场景 '$scenario' 测试失败 (HTTP $http_code)"
        fi
    done
}

# 主执行流程
main() {
    echo "API端点: $API_BASE_URL"
    echo ""
    
    # 检查服务是否运行
    if ! curl -s --head "$API_BASE_URL" > /dev/null; then
        print_error "无法连接到API服务器，请确保服务正在运行"
        exit 1
    fi
    
    # 执行测试
    health_check
    if [ $? -eq 0 ]; then
        test_ai_generation "番茄"
        test_validation
        test_scenarios
    fi
    
    echo ""
    echo "==================================="
    echo "        测试完成"
    echo "==================================="
}

# 执行主函数
main