/**
 * 冒烟测试相关的数据传输对象类型定义
 */

/**
 * 冒烟测试请求DTO
 */
export interface SmokeTestRequestDto {
  /**
   * 食材名称
   * 长度限制: 1-100个字符
   */
  ingredient: string;
}

/**
 * 冒烟测试响应DTO
 */
export interface SmokeTestResponseDto {
  /**
   * 用户输入的食材
   */
  ingredient: string;
  
  /**
   * AI生成的原始响应文本
   */
  aiResponse: string;
  
  /**
   * 响应生成的时间戳
   */
  timestamp: string;
  
  /**
   * 处理时间（毫秒）
   */
  processingTimeMs: number;
}