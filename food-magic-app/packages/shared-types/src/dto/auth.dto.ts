export interface RegisterRequestDto {
  email: string;
  password: string;
  displayName?: string;
}

export interface LoginRequestDto {
  email: string;
  password: string;
}

export interface AuthResponseDto {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: UserDto;
}

export interface CreateGuestSessionRequestDto {
  deviceId?: string;
}

export interface ConvertGuestRequestDto {
  email: string;
  password: string;
  displayName?: string;
}

export interface RefreshTokenRequestDto {
  refreshToken: string;
}

export interface RefreshTokenResponseDto {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
}

export interface ErrorResponseDto {
  status: number;
  error: string;
  message: string;
  timestamp: string;
  path: string;
}