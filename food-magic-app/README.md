# FoodMagic - AI食谱生成应用

![CI/CD Pipeline](https://github.com/your-username/food-magic-app/actions/workflows/ci.yml/badge.svg)

一个基于AI技术的智能食谱生成应用，采用Nx Monorepo架构，包含React Native前端和Spring Boot后端。

## 前置条件

在开始之前，请确保您的开发环境已安装以下工具：

- **Node.js**: v20.x LTS 或更高版本
- **Java JDK**: 17 LTS
- **Docker Desktop**: 用于运行PostgreSQL和Redis
- **Git**: 版本控制
- **IDE推荐**: 
  - IntelliJ IDEA (后端开发)
  - VS Code (前端开发)

## 快速开始

### 1. 克隆仓库

```bash
git clone <repository-url>
cd food-magic-app
```

### 2. 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装前端依赖
cd apps/mobile
npm install
cd ../..

# 后端依赖由Gradle自动管理
```

### 3. 配置环境变量

#### 后端配置
复制环境变量模板并配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置以下必要变量：
```env
# 数据库连接
DB_HOST=localhost
DB_PORT=5432
DB_NAME=foodmagic
DB_USERNAME=foodmagic
DB_PASSWORD=your_database_password_here

# JWT 安全密钥
JWT_SECRET=your_jwt_secret_key_here

# AI 服务配置（必须配置）
AI_PROVIDER=zhipu  # 或 qwen
AI_API_KEY=your_ai_api_key_here
AI_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions  # 智谱AI
# 或者使用阿里通义千问:
# AI_API_URL=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
AI_MODEL=glm-4  # 或 qwen-turbo
```

**重要**: 
- AI_API_KEY 必须配置，可以从相应的AI服务提供商获取
- 智谱AI: https://open.bigmodel.cn/
- 阿里通义千问: https://dashscope.aliyun.com/

#### 前端配置
复制环境变量模板并配置：
```bash
cp apps/mobile/.env.example apps/mobile/.env
```

编辑 `apps/mobile/.env` 文件：
```env
# 后端API的基础URL
API_BASE_URL=http://localhost:8080/api/v1
```

### 4. 启动数据库服务

使用Docker Compose启动PostgreSQL和Redis：
```bash
docker-compose up -d
```

### 5. 启动应用

#### 启动后端服务
```bash
npm run start:api
# 或者
cd apps/api && ./gradlew bootRun
```
后端服务将在 http://localhost:8080 运行

#### 启动前端应用
```bash
npm run start:mobile
# 或者
cd apps/mobile && npm start
```
按照Expo提示，选择在iOS模拟器、Android模拟器或物理设备上运行

## 项目结构

```
food-magic-app/
├── apps/                   # 可独立运行的应用程序
│   ├── api/                # Spring Boot 后端应用
│   │   ├── src/main/java/com/foodmagic/
│   │   │   ├── auth/       # 认证授权模块
│   │   │   ├── recipe/     # 食谱生成模块
│   │   │   ├── user/       # 用户管理模块
│   │   │   ├── saved/      # 收藏管理模块
│   │   │   ├── ai/         # AI集成模块
│   │   │   └── common/     # 通用功能
│   │   └── build.gradle
│   └── mobile/             # React Native 前端应用
│       ├── app/            # Expo Router页面
│       ├── components/     # UI组件
│       ├── services/       # API服务
│       ├── stores/         # 状态管理
│       └── package.json
├── packages/               # 共享代码包
│   ├── shared-types/       # 前后端共享TypeScript类型
│   ├── eslint-config/      # ESLint配置
│   ├── api-contract/       # OpenAPI生成的类型
│   └── ui/                 # 共享UI组件和主题
├── nx.json                 # Nx配置
├── package.json            # 根目录package.json
└── tsconfig.base.json      # TypeScript基础配置
```

## 冒烟测试功能

### 功能说明
冒烟测试功能用于验证端到端技术栈的连通性，包括前端、后端和AI服务的集成。用户可以输入食材名称，系统会调用AI服务生成相关的美食建议。

### 访问方式
- **前端页面**: 在移动应用中访问 `/smoke-test` 页面
- **API端点**: `POST /api/v1/smoke-test/generate`
- **健康检查**: `GET /api/v1/smoke-test/health`

### 测试方法

#### 1. 使用测试脚本
```bash
# 运行端到端测试脚本
./test-smoke-api.sh
```

#### 2. 手动测试API
```bash
# 健康检查
curl http://localhost:8080/api/v1/smoke-test/health

# 生成AI响应
curl -X POST http://localhost:8080/api/v1/smoke-test/generate \
  -H "Content-Type: application/json" \
  -d '{"ingredient": "番茄"}'
```

#### 3. 运行单元测试
```bash
# 后端测试
cd apps/api && ./gradlew test

# 前端测试
npm run test:mobile
```

### 故障排除

#### AI服务配置错误
如果遇到"AI服务配置错误"或"AI服务暂时不可用"：
1. 确认已在 `.env` 文件中配置 `AI_API_KEY`
2. 检查 `AI_API_URL` 是否正确
3. 验证AI服务提供商的API密钥是否有效

#### 网络连接失败
1. 确保后端服务正在运行 (`npm run start:api`)
2. 检查防火墙设置
3. 确认前端的API_BASE_URL配置正确

## 常用命令

### 开发命令
```bash
# 启动前端
npm run start:mobile

# 启动后端
npm run start:api

# 同时启动前后端
nx run-many --target=serve --all

# 运行代码检查
npm run lint

# 运行测试
npm run test

# 构建后端
npm run build:api

# 构建前端
npm run build:mobile
```

### Nx命令
```bash
# 查看项目依赖图
nx graph

# 运行特定项目的任务
nx run api:build
nx run mobile:test

# 运行受影响的项目
nx affected:test
nx affected:lint
```

## 技术栈

### 前端
- **框架**: React Native (Expo) 0.74.x
- **语言**: TypeScript 5.4.x
- **UI库**: Tamagui 1.95.x
- **状态管理**: Zustand 4.5.x
- **测试**: Jest 29.x.x

### 后端
- **框架**: Spring Boot 3.2.x
- **语言**: Java 17 LTS
- **数据库**: PostgreSQL 16.x
- **缓存**: Redis 7.2.x
- **构建工具**: Gradle 8.7.x
- **测试**: JUnit 5, Mockito

## CI/CD流水线

本项目使用GitHub Actions实现持续集成和持续部署。

### 流水线触发条件
- 推送到 `main` 或 `develop` 分支
- 创建或更新Pull Request

### 流水线阶段

1. **Setup and Lint** - 设置环境并执行代码检查
   - 安装Node.js 20.x和Java 17
   - 缓存npm和Gradle依赖
   - 运行 `nx affected:lint` 检查受影响项目的代码规范
   - 检查模块边界规则违规

2. **Test** - 运行自动化测试
   - 运行 `nx affected:test` 执行受影响项目的单元测试
   - 生成测试覆盖率报告
   - 上传测试结果作为构建产物

3. **Build** - 构建应用
   - 运行 `nx affected:build` 构建受影响的项目
   - 上传构建产物供后续部署使用

4. **Report Status** - 报告流水线状态
   - 汇总各阶段执行结果
   - 失败时提供详细错误信息

### 本地运行CI检查

在提交代码前，可以在本地运行相同的检查：

```bash
# 运行代码检查
npm run lint:affected

# 运行测试
npm run test:affected

# 构建项目
npm run build:affected
```

### 模块边界规则

项目配置了严格的模块边界规则，确保架构的清晰性：

- **前端模块** (`scope:frontend`) 只能依赖前端和共享模块
- **后端模块** (`scope:backend`) 只能依赖后端和共享模块
- **应用** (`type:app`) 可以依赖UI、类型、契约和配置库
- **移动平台** (`platform:mobile`) 禁止导入服务器端库
- **服务器平台** (`platform:server`) 禁止导入React Native相关库

违反模块边界规则将导致CI流水线失败。

## 开发规范

### 命名约定
- **前端文件**: PascalCase (组件), camelCase (函数/变量)
- **后端文件**: PascalCase (类), camelCase (方法/变量)
- **API端点**: kebab-case
- **数据库**: snake_case

### 代码规范
1. 所有前后端共享的数据结构必须在 `packages/shared-types` 中定义
2. 禁止在代码中硬编码密钥或敏感信息
3. 后端模块间通过API接口通信，禁止直接调用内部实现

## 故障排除

### 常见问题

#### 1. 端口冲突
如果8080或3000端口被占用，可以修改配置：
- 后端: 编辑 `apps/api/src/main/resources/application.yml`
- 前端: 编辑 `apps/mobile/package.json` 中的启动脚本

#### 2. 数据库连接失败
确保Docker正在运行并且PostgreSQL容器已启动：
```bash
docker ps
docker-compose restart postgres
```

#### 3. 依赖安装失败
清理缓存并重新安装：
```bash
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## 贡献指南

1. 创建功能分支: `git checkout -b feature/your-feature`
2. 提交更改: `git commit -m 'Add some feature'`
3. 推送分支: `git push origin feature/your-feature`
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。