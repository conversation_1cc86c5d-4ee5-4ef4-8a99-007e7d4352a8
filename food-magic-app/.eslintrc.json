{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "scope:frontend", "onlyDependOnLibsWithTags": ["scope:frontend", "scope:shared"]}, {"sourceTag": "scope:backend", "onlyDependOnLibsWithTags": ["scope:backend", "scope:shared"]}, {"sourceTag": "type:app", "onlyDependOnLibsWithTags": ["type:ui", "type:types", "type:contract", "type:config"]}, {"sourceTag": "platform:mobile", "bannedExternalImports": ["express", "fastify", "spring"]}, {"sourceTag": "platform:server", "bannedExternalImports": ["react-native", "expo"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}], "extends": ["plugin:@nx/react"]}