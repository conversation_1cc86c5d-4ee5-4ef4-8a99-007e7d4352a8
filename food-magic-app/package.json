{"name": "@food-magic-app/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:api": "cd apps/api && ./gradlew bootRun", "start:mobile": "cd apps/mobile && npm start", "lint": "nx run-many --target=lint --all", "lint:affected": "nx affected --target=lint", "test": "nx run-many --target=test --all", "test:affected": "nx affected --target=test", "build": "nx run-many --target=build --all", "build:affected": "nx affected --target=build", "build:api": "cd apps/api && ./gradlew build", "build:mobile": "cd apps/mobile && npm run build", "affected:lint": "nx affected --target=lint", "affected:test": "nx affected --target=test --coverage", "affected:build": "nx affected --target=build"}, "private": true, "dependencies": {}, "devDependencies": {"@nx/js": "21.3.11", "@nx/workspace": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/react": "21.3.11", "@typescript-eslint/eslint-plugin": "^8.21.2", "@typescript-eslint/parser": "^8.21.2", "eslint": "^9.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.27.0", "eslint-plugin-react-hooks": "^5.0.0", "nx": "21.3.11"}}