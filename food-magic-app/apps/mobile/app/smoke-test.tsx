import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Stack,
  Text,
  Input,
  <PERSON>ton,
  <PERSON>rollView,
  Card,
  Spinner,
  H1,
  H3,
  Paragraph,
  Theme,
} from 'tamagui';
import { KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import smokeTestService from '../services/smokeTest.service';
import { ApiError } from '../services/api';

export default function SmokeTestScreen() {
  const insets = useSafeAreaInsets();
  const [ingredient, setIngredient] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [aiResponse, setAiResponse] = useState('');
  const [error, setError] = useState('');
  const [processingTime, setProcessingTime] = useState<number | null>(null);

  const validateInput = (): boolean => {
    if (!ingredient.trim()) {
      setError('请输入食材名称');
      return false;
    }
    if (ingredient.length > 100) {
      setError('食材名称不能超过100个字符');
      return false;
    }
    setError('');
    return true;
  };

  const handleSubmit = async () => {
    if (!validateInput()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setAiResponse('');
    setProcessingTime(null);

    try {
      // 调用实际的API
      const response = await smokeTestService.generateAiResponse(ingredient);
      
      // 设置响应数据
      setAiResponse(response.aiResponse);
      setProcessingTime(response.processingTimeMs);
    } catch (err) {
      if (err instanceof ApiError) {
        if (err.statusCode === 503) {
          setError('AI服务暂时不可用，请稍后重试');
        } else if (err.statusCode === 400) {
          setError('输入的食材无效，请重新输入');
        } else if (err.statusCode === 0) {
          setError('网络连接失败，请检查网络设置');
        } else {
          setError(`获取AI响应失败: ${err.message}`);
        }
      } else {
        setError('获取AI响应失败，请稍后重试');
      }
      console.error('Error fetching AI response:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setIngredient('');
    setAiResponse('');
    setError('');
    setProcessingTime(null);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingTop: insets.top + 20,
          paddingBottom: insets.bottom + 20,
        }}
      >
        <YStack flex={1} padding="$4" space="$4">
          {/* 标题区域 */}
          <YStack alignItems="center" space="$2">
            <H1 color="$green10" textAlign="center">
              🧪 冒烟测试
            </H1>
            <Paragraph textAlign="center" color="$gray11">
              端到端技术栈验证
            </Paragraph>
          </YStack>

          {/* 输入区域 */}
          <Card elevate padding="$4" space="$3">
            <YStack space="$2">
              <Text fontSize="$4" fontWeight="600" color="$gray12">
                食材输入
              </Text>
              <Input
                size="$4"
                placeholder="请输入食材，如：番茄"
                value={ingredient}
                onChangeText={(text) => {
                  setIngredient(text);
                  if (error) setError('');
                }}
                disabled={isLoading}
                borderColor={error ? '$red9' : '$borderColor'}
                focusStyle={{
                  borderColor: error ? '$red9' : '$green9',
                  borderWidth: 2,
                }}
                returnKeyType="done"
                onSubmitEditing={handleSubmit}
              />
              {ingredient.length > 0 && (
                <Text fontSize="$2" color="$gray10">
                  {ingredient.length}/100 字符
                </Text>
              )}
              {error && (
                <Text fontSize="$3" color="$red10">
                  {error}
                </Text>
              )}
            </YStack>
          </Card>

          {/* 操作按钮 */}
          <XStack space="$3" justifyContent="center">
            <Button
              size="$4"
              theme="green"
              onPress={handleSubmit}
              disabled={isLoading || !ingredient.trim()}
              icon={isLoading ? <Spinner size="small" color="white" /> : undefined}
              minWidth={150}
            >
              {isLoading ? '获取中...' : '获取AI推荐'}
            </Button>
            <Button
              size="$4"
              theme="gray"
              onPress={handleClear}
              disabled={isLoading}
              variant="outlined"
            >
              清除
            </Button>
          </XStack>

          {/* 加载提示 */}
          {isLoading && (
            <Card padding="$4" backgroundColor="$blue2">
              <YStack alignItems="center" space="$2">
                <Spinner size="large" color="$blue10" />
                <Text color="$blue10">正在调用AI服务，通常需要3-5秒...</Text>
              </YStack>
            </Card>
          )}

          {/* 结果显示区域 */}
          {aiResponse && !isLoading && (
            <Card elevate padding="$4" space="$3">
              <YStack space="$2">
                <XStack justifyContent="space-between" alignItems="center">
                  <H3 color="$green10">AI响应</H3>
                  {processingTime && (
                    <Text fontSize="$2" color="$gray10">
                      耗时: {processingTime}ms
                    </Text>
                  )}
                </XStack>
                <ScrollView
                  style={{ maxHeight: 300 }}
                  showsVerticalScrollIndicator={true}
                >
                  <Card padding="$3" backgroundColor="$gray2">
                    <Text fontSize="$3" color="$gray12" selectable>
                      {aiResponse}
                    </Text>
                  </Card>
                </ScrollView>
              </YStack>
            </Card>
          )}

          {/* 空状态提示 */}
          {!aiResponse && !isLoading && !error && (
            <Card padding="$4" backgroundColor="$gray2">
              <YStack alignItems="center" space="$2">
                <Text fontSize="$6">🍅</Text>
                <Text color="$gray11" textAlign="center">
                  输入食材名称，点击按钮获取AI推荐
                </Text>
              </YStack>
            </Card>
          )}
        </YStack>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}