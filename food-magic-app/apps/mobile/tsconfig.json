{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "module": "esnext", "moduleResolution": "node", "target": "ESNext", "lib": ["ES2022"], "jsx": "react-native", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@services/*": ["services/*"], "@stores/*": ["stores/*"], "@utils/*": ["utils/*"], "@hooks/*": ["hooks/*"], "@assets/*": ["assets/*"], "@shared-types": ["../../packages/shared-types/src"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}