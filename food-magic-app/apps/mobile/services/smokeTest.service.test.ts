import { smokeTestService } from './smokeTest.service';
import * as api from './api';
import { SmokeTestResponseDto } from '@food-magic/shared-types';

// Mock the api module
jest.mock('./api');

describe('SmokeTestService', () => {
  const mockPost = api.post as jest.MockedFunction<typeof api.post>;
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('generateAiResponse', () => {
    it('should successfully generate AI response', async () => {
      // Arrange
      const ingredient = '番茄';
      const mockResponse: SmokeTestResponseDto = {
        ingredient: '番茄',
        aiResponse: '这是一个关于番茄的食谱建议...',
        timestamp: '2025-01-13T10:00:00.000Z',
        processingTimeMs: 1500,
      };
      
      mockPost.mockResolvedValueOnce(mockResponse);
      
      // Act
      const result = await smokeTestService.generateAiResponse(ingredient);
      
      // Assert
      expect(result).toEqual(mockResponse);
      expect(mockPost).toHaveBeenCalledWith('/smoke-test/generate', {
        ingredient: '番茄',
      });
      expect(mockPost).toHaveBeenCalledTimes(1);
    });
    
    it('should trim ingredient before sending', async () => {
      // Arrange
      const ingredient = '  番茄  ';
      const mockResponse: SmokeTestResponseDto = {
        ingredient: '番茄',
        aiResponse: 'Response',
        timestamp: '2025-01-13T10:00:00.000Z',
        processingTimeMs: 1000,
      };
      
      mockPost.mockResolvedValueOnce(mockResponse);
      
      // Act
      await smokeTestService.generateAiResponse(ingredient);
      
      // Assert
      expect(mockPost).toHaveBeenCalledWith('/smoke-test/generate', {
        ingredient: '番茄',
      });
    });
    
    it('should handle API errors', async () => {
      // Arrange
      const ingredient = '番茄';
      const error = new Error('Network error');
      mockPost.mockRejectedValueOnce(error);
      
      // Act & Assert
      await expect(smokeTestService.generateAiResponse(ingredient))
        .rejects.toThrow('Network error');
      
      expect(mockPost).toHaveBeenCalledTimes(1);
    });
    
    it('should handle empty ingredient', async () => {
      // Arrange
      const ingredient = '';
      const mockResponse: SmokeTestResponseDto = {
        ingredient: '',
        aiResponse: 'Error response',
        timestamp: '2025-01-13T10:00:00.000Z',
        processingTimeMs: 0,
      };
      
      mockPost.mockResolvedValueOnce(mockResponse);
      
      // Act
      const result = await smokeTestService.generateAiResponse(ingredient);
      
      // Assert
      expect(result).toEqual(mockResponse);
      expect(mockPost).toHaveBeenCalledWith('/smoke-test/generate', {
        ingredient: '',
      });
    });
    
    it('should handle long ingredient names', async () => {
      // Arrange
      const longIngredient = 'a'.repeat(100);
      const mockResponse: SmokeTestResponseDto = {
        ingredient: longIngredient,
        aiResponse: 'Response for long ingredient',
        timestamp: '2025-01-13T10:00:00.000Z',
        processingTimeMs: 2000,
      };
      
      mockPost.mockResolvedValueOnce(mockResponse);
      
      // Act
      const result = await smokeTestService.generateAiResponse(longIngredient);
      
      // Assert
      expect(result).toEqual(mockResponse);
      expect(mockPost).toHaveBeenCalledWith('/smoke-test/generate', {
        ingredient: longIngredient,
      });
    });
  });
  
  describe('healthCheck', () => {
    it('should successfully perform health check', async () => {
      // Arrange
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      const mockResponse = {
        text: jest.fn().mockResolvedValue('Smoke test endpoint is healthy'),
      } as any;
      
      (global.fetch as jest.Mock) = jest.fn().mockResolvedValue(mockResponse);
      
      // Act
      const result = await smokeTestService.healthCheck();
      
      // Assert
      expect(result).toBe('Smoke test endpoint is healthy');
      expect(global.fetch).toHaveBeenCalledWith('/smoke-test/health');
    });
    
    it('should handle health check errors', async () => {
      // Arrange
      const error = new Error('Connection failed');
      (global.fetch as jest.Mock) = jest.fn().mockRejectedValue(error);
      
      // Act & Assert
      await expect(smokeTestService.healthCheck())
        .rejects.toThrow('Connection failed');
    });
  });
});