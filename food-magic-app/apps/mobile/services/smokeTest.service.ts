import { post } from './api';
import { SmokeTestRequestDto, SmokeTestResponseDto } from '@food-magic/shared-types';

/**
 * 冒烟测试服务
 */
class SmokeTestService {
  /**
   * 生成AI响应
   * @param ingredient 食材名称
   * @returns AI响应数据
   */
  async generateAiResponse(ingredient: string): Promise<SmokeTestResponseDto> {
    try {
      const request: SmokeTestRequestDto = {
        ingredient: ingredient.trim(),
      };
      
      const response = await post<SmokeTestResponseDto>('/smoke-test/generate', request);
      
      return response;
    } catch (error) {
      console.error('Error generating AI response:', error);
      throw error;
    }
  }
  
  /**
   * 健康检查
   * @returns 健康状态字符串
   */
  async healthCheck(): Promise<string> {
    try {
      const response = await fetch('/smoke-test/health');
      return await response.text();
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }
}

export const smokeTestService = new SmokeTestService();
export default smokeTestService;