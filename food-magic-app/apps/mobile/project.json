{"name": "mobile", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile", "projectType": "application", "tags": ["scope:frontend", "type:app", "platform:mobile"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/mobile/**/*.{ts,tsx,js,jsx}"]}, "configurations": {"boundaries": {"lintFilePatterns": ["apps/mobile/**/*.{ts,tsx,js,jsx}"]}}}}, "implicitDependencies": []}