#!/bin/bash

# QA Test Script for Authentication API
# This script tests the complete authentication flow including guest session creation and conversion

set -e

# Configuration
BASE_URL="${API_BASE_URL:-http://localhost:8080/api/v1}"
AUTH_URL="${BASE_URL}/auth"
TEST_EMAIL="qatest_$(date +%s)@example.com"
TEST_PASSWORD="TestPassword123"
TEST_DISPLAY_NAME="QA Test User"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
print_test() {
    echo -e "${YELLOW}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

# Function to validate JSON response
validate_json() {
    if ! echo "$1" | jq empty 2>/dev/null; then
        return 1
    fi
    return 0
}

# Function to extract value from JSON
extract_json_value() {
    echo "$1" | jq -r "$2"
}

echo "======================================"
echo "Authentication API Test Suite"
echo "Testing against: ${BASE_URL}"
echo "======================================"
echo ""

# Test 1: Health Check
print_test "1. Checking API health..."
HEALTH_RESPONSE=$(curl -s -w "\n%{http_code}" "${BASE_URL}/actuator/health" 2>/dev/null || echo "000")
HTTP_CODE=$(echo "$HEALTH_RESPONSE" | tail -n 1)
if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "404" ]; then
    print_success "API is reachable"
else
    print_error "API is not reachable (HTTP $HTTP_CODE)"
    exit 1
fi

# Test 2: Register New User
print_test "2. Testing user registration..."
REGISTER_RESPONSE=$(curl -s -X POST "${AUTH_URL}/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "'"${TEST_EMAIL}"'",
        "password": "'"${TEST_PASSWORD}"'",
        "displayName": "'"${TEST_DISPLAY_NAME}"'"
    }' 2>/dev/null)

if validate_json "$REGISTER_RESPONSE"; then
    ACCESS_TOKEN=$(extract_json_value "$REGISTER_RESPONSE" ".accessToken")
    REFRESH_TOKEN=$(extract_json_value "$REGISTER_RESPONSE" ".refreshToken")
    USER_ID=$(extract_json_value "$REGISTER_RESPONSE" ".user.id")
    
    if [ -n "$ACCESS_TOKEN" ] && [ "$ACCESS_TOKEN" != "null" ]; then
        print_success "User registered successfully (ID: $USER_ID)"
    else
        print_error "Registration failed - no access token received"
        echo "Response: $REGISTER_RESPONSE"
    fi
else
    print_error "Registration failed - invalid JSON response"
    echo "Response: $REGISTER_RESPONSE"
fi

# Test 3: Login with Registered User
print_test "3. Testing user login..."
LOGIN_RESPONSE=$(curl -s -X POST "${AUTH_URL}/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "'"${TEST_EMAIL}"'",
        "password": "'"${TEST_PASSWORD}"'"
    }' 2>/dev/null)

if validate_json "$LOGIN_RESPONSE"; then
    LOGIN_TOKEN=$(extract_json_value "$LOGIN_RESPONSE" ".accessToken")
    
    if [ -n "$LOGIN_TOKEN" ] && [ "$LOGIN_TOKEN" != "null" ]; then
        print_success "Login successful"
        ACCESS_TOKEN=$LOGIN_TOKEN
    else
        print_error "Login failed - no access token received"
        echo "Response: $LOGIN_RESPONSE"
    fi
else
    print_error "Login failed - invalid JSON response"
    echo "Response: $LOGIN_RESPONSE"
fi

# Test 4: Get Current User
print_test "4. Testing get current user..."
CURRENT_USER_RESPONSE=$(curl -s -X GET "${AUTH_URL}/me" \
    -H "Authorization: Bearer ${ACCESS_TOKEN}" 2>/dev/null)

if validate_json "$CURRENT_USER_RESPONSE"; then
    EMAIL=$(extract_json_value "$CURRENT_USER_RESPONSE" ".email")
    
    if [ "$EMAIL" = "$TEST_EMAIL" ]; then
        print_success "Current user retrieved successfully"
    else
        print_error "Current user email mismatch"
        echo "Expected: $TEST_EMAIL, Got: $EMAIL"
    fi
else
    print_error "Get current user failed"
    echo "Response: $CURRENT_USER_RESPONSE"
fi

# Test 5: Create Guest Session
print_test "5. Testing guest session creation..."
GUEST_RESPONSE=$(curl -s -X POST "${AUTH_URL}/session" \
    -H "Content-Type: application/json" \
    -d '{"deviceId": "qa-test-device"}' 2>/dev/null)

if validate_json "$GUEST_RESPONSE"; then
    GUEST_TOKEN=$(extract_json_value "$GUEST_RESPONSE" ".accessToken")
    GUEST_USER_ID=$(extract_json_value "$GUEST_RESPONSE" ".user.id")
    GUEST_AUTH_PROVIDER=$(extract_json_value "$GUEST_RESPONSE" ".user.authProvider")
    
    if [ -n "$GUEST_TOKEN" ] && [ "$GUEST_TOKEN" != "null" ] && [ "$GUEST_AUTH_PROVIDER" = "GUEST" ]; then
        print_success "Guest session created (ID: $GUEST_USER_ID)"
    else
        print_error "Guest session creation failed"
        echo "Response: $GUEST_RESPONSE"
    fi
else
    print_error "Guest session creation failed - invalid JSON"
    echo "Response: $GUEST_RESPONSE"
fi

# Test 6: Convert Guest to Registered User
print_test "6. Testing guest to user conversion..."
CONVERT_EMAIL="qaconvert_$(date +%s)@example.com"
CONVERT_RESPONSE=$(curl -s -X POST "${AUTH_URL}/session/convert" \
    -H "Authorization: Bearer ${GUEST_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "'"${CONVERT_EMAIL}"'",
        "password": "ConvertedPassword123",
        "displayName": "Converted User"
    }' 2>/dev/null)

if validate_json "$CONVERT_RESPONSE"; then
    CONVERTED_TOKEN=$(extract_json_value "$CONVERT_RESPONSE" ".accessToken")
    CONVERTED_EMAIL=$(extract_json_value "$CONVERT_RESPONSE" ".user.email")
    CONVERTED_PROVIDER=$(extract_json_value "$CONVERT_RESPONSE" ".user.authProvider")
    
    if [ "$CONVERTED_EMAIL" = "$CONVERT_EMAIL" ] && [ "$CONVERTED_PROVIDER" = "LOCAL" ]; then
        print_success "Guest converted to registered user successfully"
    else
        print_error "Guest conversion failed"
        echo "Response: $CONVERT_RESPONSE"
    fi
else
    print_error "Guest conversion failed - invalid JSON"
    echo "Response: $CONVERT_RESPONSE"
fi

# Test 7: Refresh Token
print_test "7. Testing token refresh..."
REFRESH_RESPONSE=$(curl -s -X POST "${AUTH_URL}/refresh" \
    -H "Content-Type: application/json" \
    -d '{
        "refreshToken": "'"${REFRESH_TOKEN}"'"
    }' 2>/dev/null)

if validate_json "$REFRESH_RESPONSE"; then
    NEW_ACCESS_TOKEN=$(extract_json_value "$REFRESH_RESPONSE" ".accessToken")
    
    if [ -n "$NEW_ACCESS_TOKEN" ] && [ "$NEW_ACCESS_TOKEN" != "null" ] && [ "$NEW_ACCESS_TOKEN" != "$ACCESS_TOKEN" ]; then
        print_success "Token refreshed successfully"
    else
        print_error "Token refresh failed"
        echo "Response: $REFRESH_RESPONSE"
    fi
else
    print_error "Token refresh failed - invalid JSON"
    echo "Response: $REFRESH_RESPONSE"
fi

# Test 8: Validate Email
print_test "8. Testing email validation..."
VALIDATE_RESPONSE=$(curl -s -X GET "${AUTH_URL}/validate-email?email=<EMAIL>" 2>/dev/null)

if validate_json "$VALIDATE_RESPONSE"; then
    IS_VALID=$(extract_json_value "$VALIDATE_RESPONSE" ".valid")
    
    if [ "$IS_VALID" = "true" ]; then
        print_success "Email validation working correctly"
    else
        print_error "Email validation failed"
        echo "Response: $VALIDATE_RESPONSE"
    fi
else
    print_error "Email validation failed - invalid JSON"
    echo "Response: $VALIDATE_RESPONSE"
fi

# Test 9: Invalid Login
print_test "9. Testing invalid login (negative test)..."
INVALID_LOGIN_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "${AUTH_URL}/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }' 2>/dev/null)

HTTP_CODE=$(echo "$INVALID_LOGIN_RESPONSE" | tail -n 1)
if [ "$HTTP_CODE" = "401" ]; then
    print_success "Invalid login correctly rejected"
else
    print_error "Invalid login not properly rejected (HTTP $HTTP_CODE)"
fi

# Test 10: Duplicate Registration
print_test "10. Testing duplicate registration (negative test)..."
DUPLICATE_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "${AUTH_URL}/register" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "'"${TEST_EMAIL}"'",
        "password": "'"${TEST_PASSWORD}"'"
    }' 2>/dev/null)

HTTP_CODE=$(echo "$DUPLICATE_RESPONSE" | tail -n 1)
if [ "$HTTP_CODE" = "409" ]; then
    print_success "Duplicate registration correctly rejected"
else
    print_error "Duplicate registration not properly rejected (HTTP $HTTP_CODE)"
fi

# Test Summary
echo ""
echo "======================================"
echo "Test Summary"
echo "======================================"
echo -e "${GREEN}Passed:${NC} $TESTS_PASSED"
echo -e "${RED}Failed:${NC} $TESTS_FAILED"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}All tests passed successfully!${NC}"
    exit 0
else
    echo -e "${RED}Some tests failed. Please review the output above.${NC}"
    exit 1
fi