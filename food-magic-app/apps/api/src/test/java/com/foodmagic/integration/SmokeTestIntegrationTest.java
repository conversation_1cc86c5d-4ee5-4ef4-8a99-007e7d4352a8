package com.foodmagic.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.recipe.dto.SmokeTestRequestDto;
import com.foodmagic.recipe.dto.SmokeTestResponseDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 冒烟测试集成测试
 * 注意：需要配置AI API密钥才能运行完整测试
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
public class SmokeTestIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    public void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/v1/smoke-test/health"))
            .andExpect(status().isOk())
            .andExpect(content().string("Smoke test endpoint is healthy"));
    }
    
    @Test
    public void testGenerateAiResponse_ValidationError() throws Exception {
        // Test with empty ingredient
        SmokeTestRequestDto request = new SmokeTestRequestDto("");
        
        mockMvc.perform(post("/api/v1/smoke-test/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isBadRequest());
    }
    
    @Test
    public void testGenerateAiResponse_LongIngredient() throws Exception {
        // Test with ingredient exceeding max length
        String longIngredient = "a".repeat(101);
        SmokeTestRequestDto request = new SmokeTestRequestDto(longIngredient);
        
        mockMvc.perform(post("/api/v1/smoke-test/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isBadRequest());
    }
    
    @Test
    public void testGenerateAiResponse_SpecialCharacters() throws Exception {
        // Test with special characters in ingredient
        SmokeTestRequestDto request = new SmokeTestRequestDto("番茄@#$%");
        
        // Without API key configured, this will return 503
        MvcResult result = mockMvc.perform(post("/api/v1/smoke-test/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isServiceUnavailable())
            .andReturn();
        
        String responseBody = result.getResponse().getContentAsString();
        SmokeTestResponseDto response = objectMapper.readValue(responseBody, SmokeTestResponseDto.class);
        
        assertNotNull(response);
        assertEquals("番茄@#$%", response.getIngredient());
        assertTrue(response.getAiResponse().contains("错误") || response.getAiResponse().contains("AI"));
    }
    
    @Test
    @Disabled("需要配置真实的AI API密钥才能运行此测试")
    public void testGenerateAiResponse_RealApiCall() throws Exception {
        // This test requires actual AI API key to be configured
        SmokeTestRequestDto request = new SmokeTestRequestDto("番茄");
        
        MvcResult result = mockMvc.perform(post("/api/v1/smoke-test/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.ingredient").value("番茄"))
            .andExpect(jsonPath("$.aiResponse").isNotEmpty())
            .andExpect(jsonPath("$.timestamp").isNotEmpty())
            .andExpect(jsonPath("$.processingTimeMs").isNumber())
            .andReturn();
        
        String responseBody = result.getResponse().getContentAsString();
        SmokeTestResponseDto response = objectMapper.readValue(responseBody, SmokeTestResponseDto.class);
        
        assertNotNull(response);
        assertEquals("番茄", response.getIngredient());
        assertNotNull(response.getAiResponse());
        assertFalse(response.getAiResponse().isEmpty());
        assertTrue(response.getProcessingTimeMs() > 0);
    }
    
    @Test
    public void testGenerateAiResponse_NoApiKeyConfigured() throws Exception {
        // When API key is not configured, should return 503
        SmokeTestRequestDto request = new SmokeTestRequestDto("番茄");
        
        MvcResult result = mockMvc.perform(post("/api/v1/smoke-test/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isServiceUnavailable())
            .andReturn();
        
        String responseBody = result.getResponse().getContentAsString();
        SmokeTestResponseDto response = objectMapper.readValue(responseBody, SmokeTestResponseDto.class);
        
        assertNotNull(response);
        assertEquals("番茄", response.getIngredient());
        assertTrue(response.getAiResponse().contains("AI服务") || response.getAiResponse().contains("错误"));
    }
}