package com.foodmagic.auth.repository;

import com.foodmagic.auth.entity.UserEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
class UserRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private UserRepository userRepository;
    
    private UserEntity testUser;
    
    @BeforeEach
    void setUp() {
        testUser = UserEntity.builder()
                .email("<EMAIL>")
                .passwordHash("hashedPassword")
                .displayName("Test User")
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
    }
    
    @Test
    void save_CreatesNewUser() {
        UserEntity savedUser = userRepository.save(testUser);
        
        assertNotNull(savedUser.getId());
        assertEquals(testUser.getEmail(), savedUser.getEmail());
        assertNotNull(savedUser.getCreatedAt());
        assertNotNull(savedUser.getUpdatedAt());
    }
    
    @Test
    void findByEmail_ExistingUser_ReturnsUser() {
        entityManager.persistAndFlush(testUser);
        
        Optional<UserEntity> found = userRepository.findByEmail("<EMAIL>");
        
        assertTrue(found.isPresent());
        assertEquals(testUser.getEmail(), found.get().getEmail());
    }
    
    @Test
    void findByEmail_NonExistingUser_ReturnsEmpty() {
        Optional<UserEntity> found = userRepository.findByEmail("<EMAIL>");
        
        assertFalse(found.isPresent());
    }
    
    @Test
    void findByAuthProviderAndProviderId_ExistingUser_ReturnsUser() {
        UserEntity oauthUser = UserEntity.builder()
                .authProvider(UserEntity.AuthProvider.GOOGLE)
                .providerId("google-123")
                .displayName("OAuth User")
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        entityManager.persistAndFlush(oauthUser);
        
        Optional<UserEntity> found = userRepository.findByAuthProviderAndProviderId(
                UserEntity.AuthProvider.GOOGLE, "google-123"
        );
        
        assertTrue(found.isPresent());
        assertEquals("google-123", found.get().getProviderId());
    }
    
    @Test
    void existsByEmail_ExistingEmail_ReturnsTrue() {
        entityManager.persistAndFlush(testUser);
        
        boolean exists = userRepository.existsByEmail("<EMAIL>");
        
        assertTrue(exists);
    }
    
    @Test
    void existsByEmail_NonExistingEmail_ReturnsFalse() {
        boolean exists = userRepository.existsByEmail("<EMAIL>");
        
        assertFalse(exists);
    }
    
    @Test
    void countByAuthProvider_ReturnsCorrectCount() {
        UserEntity localUser1 = UserEntity.builder()
                .email("<EMAIL>")
                .passwordHash("hash1")
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        UserEntity localUser2 = UserEntity.builder()
                .email("<EMAIL>")
                .passwordHash("hash2")
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        UserEntity guestUser = UserEntity.builder()
                .authProvider(UserEntity.AuthProvider.GUEST)
                .providerId("guest-123")
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        entityManager.persistAndFlush(localUser1);
        entityManager.persistAndFlush(localUser2);
        entityManager.persistAndFlush(guestUser);
        
        long localCount = userRepository.countByAuthProvider(UserEntity.AuthProvider.LOCAL);
        long guestCount = userRepository.countByAuthProvider(UserEntity.AuthProvider.GUEST);
        
        assertEquals(2, localCount);
        assertEquals(1, guestCount);
    }
    
    @Test
    void findExpiredGuestUsers_ReturnsExpiredGuests() {
        Instant now = Instant.now();
        
        UserEntity expiredGuest = UserEntity.builder()
                .authProvider(UserEntity.AuthProvider.GUEST)
                .providerId("expired-guest")
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        UserEntity activeGuest = UserEntity.builder()
                .authProvider(UserEntity.AuthProvider.GUEST)
                .providerId("active-guest")
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        expiredGuest = entityManager.persistAndFlush(expiredGuest);
        activeGuest = entityManager.persistAndFlush(activeGuest);
        
        // Manually set created_at for expired guest (2 days ago)
        entityManager.getEntityManager()
                .createNativeQuery("UPDATE users SET created_at = :createdAt WHERE id = :id")
                .setParameter("createdAt", now.minus(2, ChronoUnit.DAYS))
                .setParameter("id", expiredGuest.getId())
                .executeUpdate();
        
        entityManager.clear();
        
        // Find guests older than 1 day
        List<UserEntity> expiredUsers = userRepository.findExpiredGuestUsers(now.minus(1, ChronoUnit.DAYS));
        
        assertEquals(1, expiredUsers.size());
        assertEquals(expiredGuest.getId(), expiredUsers.get(0).getId());
    }
}