package com.foodmagic.ai.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.ai.config.AiConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AiServiceClientImplTest {
    
    @Mock
    private AiConfig aiConfig;
    
    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private AiServiceClientImpl aiServiceClient;
    
    @BeforeEach
    void setUp() {
        when(aiConfig.getApiKey()).thenReturn("test-api-key");
        when(aiConfig.getApiUrl()).thenReturn("https://api.test.com/v1/chat");
        when(aiConfig.getProvider()).thenReturn("zhipu");
        when(aiConfig.getModel()).thenReturn("glm-4");
        when(aiConfig.getMaxTokens()).thenReturn(2000);
        when(aiConfig.getTemperature()).thenReturn(0.7);
        when(aiConfig.getMaxRetries()).thenReturn(3);
        when(aiConfig.getRetryDelay()).thenReturn(100L);
    }
    
    @Test
    void testGenerateResponse_Success() {
        // Arrange
        String prompt = "Test prompt";
        String expectedResponse = "This is the AI response";
        String jsonResponse = "{\"choices\":[{\"message\":{\"content\":\"" + expectedResponse + "\"}}]}";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(jsonResponse, HttpStatus.OK);
        when(restTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        )).thenReturn(responseEntity);
        
        try {
            when(objectMapper.readTree(jsonResponse)).thenReturn(
                new ObjectMapper().readTree(jsonResponse)
            );
        } catch (Exception e) {
            fail("Failed to setup mock");
        }
        
        // Act
        String result = aiServiceClient.generateResponse(prompt);
        
        // Assert
        assertEquals(expectedResponse, result);
        verify(restTemplate, times(1)).exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        );
    }
    
    @Test
    void testGenerateResponse_MissingApiKey() {
        // Arrange
        when(aiConfig.getApiKey()).thenReturn(null);
        
        // Act & Assert
        assertThrows(IllegalStateException.class, () -> {
            aiServiceClient.generateResponse("Test prompt");
        });
    }
    
    @Test
    void testGenerateResponse_HttpClientError() {
        // Arrange
        String prompt = "Test prompt";
        HttpClientErrorException exception = new HttpClientErrorException(
            HttpStatus.BAD_REQUEST,
            "Bad Request",
            "Error body".getBytes(),
            null
        );
        
        when(restTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        )).thenThrow(exception);
        
        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            aiServiceClient.generateResponse(prompt);
        });
    }
    
    @Test
    void testGenerateResponse_RetryOnFailure() {
        // Arrange
        String prompt = "Test prompt";
        String expectedResponse = "Success after retry";
        String jsonResponse = "{\"choices\":[{\"message\":{\"content\":\"" + expectedResponse + "\"}}]}";
        
        HttpServerErrorException serverError = new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR);
        ResponseEntity<String> successResponse = new ResponseEntity<>(jsonResponse, HttpStatus.OK);
        
        // First call fails, second call succeeds
        when(restTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        )).thenThrow(serverError).thenReturn(successResponse);
        
        try {
            when(objectMapper.readTree(jsonResponse)).thenReturn(
                new ObjectMapper().readTree(jsonResponse)
            );
        } catch (Exception e) {
            fail("Failed to setup mock");
        }
        
        // Act
        String result = aiServiceClient.generateResponse(prompt);
        
        // Assert
        assertEquals(expectedResponse, result);
        verify(restTemplate, times(2)).exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        );
    }
    
    @Test
    void testGenerateResponseAsync() throws Exception {
        // Arrange
        String prompt = "Test prompt";
        String expectedResponse = "Async response";
        String jsonResponse = "{\"choices\":[{\"message\":{\"content\":\"" + expectedResponse + "\"}}]}";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(jsonResponse, HttpStatus.OK);
        when(restTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        )).thenReturn(responseEntity);
        
        when(objectMapper.readTree(jsonResponse)).thenReturn(
            new ObjectMapper().readTree(jsonResponse)
        );
        
        // Act
        CompletableFuture<String> future = aiServiceClient.generateResponseAsync(prompt);
        String result = future.get();
        
        // Assert
        assertEquals(expectedResponse, result);
    }
    
    @Test
    void testGenerateRecipeSuggestion() {
        // Arrange
        String ingredient = "番茄";
        String expectedResponse = "Recipe suggestion for tomato";
        String jsonResponse = "{\"choices\":[{\"message\":{\"content\":\"" + expectedResponse + "\"}}]}";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(jsonResponse, HttpStatus.OK);
        when(restTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        )).thenReturn(responseEntity);
        
        try {
            when(objectMapper.readTree(jsonResponse)).thenReturn(
                new ObjectMapper().readTree(jsonResponse)
            );
        } catch (Exception e) {
            fail("Failed to setup mock");
        }
        
        // Act
        String result = aiServiceClient.generateRecipeSuggestion(ingredient);
        
        // Assert
        assertEquals(expectedResponse, result);
    }
    
    @Test
    void testQwenProviderResponse() {
        // Arrange
        when(aiConfig.getProvider()).thenReturn("qwen");
        String prompt = "Test prompt";
        String expectedResponse = "Qwen AI response";
        String jsonResponse = "{\"output\":{\"text\":\"" + expectedResponse + "\"}}";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(jsonResponse, HttpStatus.OK);
        when(restTemplate.exchange(
            anyString(),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
        )).thenReturn(responseEntity);
        
        try {
            when(objectMapper.readTree(jsonResponse)).thenReturn(
                new ObjectMapper().readTree(jsonResponse)
            );
        } catch (Exception e) {
            fail("Failed to setup mock");
        }
        
        // Act
        String result = aiServiceClient.generateResponse(prompt);
        
        // Assert
        assertEquals(expectedResponse, result);
    }
}