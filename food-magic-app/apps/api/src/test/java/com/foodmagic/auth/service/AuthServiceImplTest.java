package com.foodmagic.auth.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.auth.dto.*;
import com.foodmagic.auth.entity.UserEntity;
import com.foodmagic.auth.entity.UserPreferencesEntity;
import com.foodmagic.auth.repository.UserPreferencesRepository;
import com.foodmagic.auth.repository.UserRepository;
import com.foodmagic.common.exception.BadRequestException;
import com.foodmagic.common.exception.ConflictException;
import com.foodmagic.common.exception.UnauthorizedException;
import com.foodmagic.common.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceImplTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private UserPreferencesRepository userPreferencesRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private JwtUtil jwtUtil;
    
    @Mock
    private AuthenticationManager authenticationManager;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private AuthServiceImpl authService;
    
    private UUID testUserId;
    private UserEntity testUser;
    
    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testUser = UserEntity.builder()
                .id(testUserId)
                .email("<EMAIL>")
                .passwordHash("hashedPassword")
                .displayName("Test User")
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        ReflectionTestUtils.setField(authService, "jwtExpiration", 3600000L);
    }
    
    @Test
    void register_Success() {
        RegisterRequestDto request = RegisterRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .displayName("New User")
                .build();
        
        when(userRepository.existsByEmail(request.getEmail())).thenReturn(false);
        when(passwordEncoder.encode(request.getPassword())).thenReturn("hashedPassword");
        when(userRepository.save(any(UserEntity.class))).thenAnswer(invocation -> {
            UserEntity user = invocation.getArgument(0);
            user.setId(testUserId);
            return user;
        });
        when(userPreferencesRepository.save(any(UserPreferencesEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(jwtUtil.generateToken(anyString(), any(UUID.class))).thenReturn("accessToken");
        when(jwtUtil.generateRefreshToken(anyString(), any(UUID.class))).thenReturn("refreshToken");
        
        AuthResponseDto response = authService.register(request);
        
        assertNotNull(response);
        assertEquals("accessToken", response.getAccessToken());
        assertEquals("refreshToken", response.getRefreshToken());
        assertEquals("Bearer", response.getTokenType());
        assertNotNull(response.getUser());
        
        verify(userRepository).save(any(UserEntity.class));
        verify(userPreferencesRepository).save(any(UserPreferencesEntity.class));
    }
    
    @Test
    void register_EmailAlreadyExists_ThrowsConflictException() {
        RegisterRequestDto request = RegisterRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .build();
        
        when(userRepository.existsByEmail(request.getEmail())).thenReturn(true);
        
        assertThrows(ConflictException.class, () -> authService.register(request));
        
        verify(userRepository, never()).save(any(UserEntity.class));
    }
    
    @Test
    void register_InvalidEmail_ThrowsBadRequestException() {
        RegisterRequestDto request = RegisterRequestDto.builder()
                .email("invalid-email")
                .password("password123")
                .build();
        
        assertThrows(BadRequestException.class, () -> authService.register(request));
        
        verify(userRepository, never()).save(any(UserEntity.class));
    }
    
    @Test
    void login_Success() {
        LoginRequestDto request = LoginRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .build();
        
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenReturn(mock(UsernamePasswordAuthenticationToken.class));
        when(userRepository.findByEmail(request.getEmail())).thenReturn(Optional.of(testUser));
        when(jwtUtil.generateToken(anyString(), any(UUID.class))).thenReturn("accessToken");
        when(jwtUtil.generateRefreshToken(anyString(), any(UUID.class))).thenReturn("refreshToken");
        
        AuthResponseDto response = authService.login(request);
        
        assertNotNull(response);
        assertEquals("accessToken", response.getAccessToken());
        assertEquals("refreshToken", response.getRefreshToken());
        assertNotNull(response.getUser());
    }
    
    @Test
    void login_InvalidCredentials_ThrowsUnauthorizedException() {
        LoginRequestDto request = LoginRequestDto.builder()
                .email("<EMAIL>")
                .password("wrongpassword")
                .build();
        
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenThrow(new BadCredentialsException("Bad credentials"));
        
        assertThrows(UnauthorizedException.class, () -> authService.login(request));
    }
    
    @Test
    void createGuestSession_Success() {
        CreateGuestSessionRequestDto request = new CreateGuestSessionRequestDto();
        
        when(userRepository.save(any(UserEntity.class))).thenAnswer(invocation -> {
            UserEntity user = invocation.getArgument(0);
            user.setId(testUserId);
            return user;
        });
        when(userPreferencesRepository.save(any(UserPreferencesEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(jwtUtil.generateToken(anyString(), any(UUID.class))).thenReturn("accessToken");
        when(jwtUtil.generateRefreshToken(anyString(), any(UUID.class))).thenReturn("refreshToken");
        
        AuthResponseDto response = authService.createGuestSession(request);
        
        assertNotNull(response);
        assertEquals("accessToken", response.getAccessToken());
        assertEquals("refreshToken", response.getRefreshToken());
        assertNotNull(response.getUser());
        
        verify(userRepository).save(argThat(user -> 
            user.getAuthProvider() == UserEntity.AuthProvider.GUEST
        ));
    }
    
    @Test
    void convertGuestToUser_Success() {
        UserEntity guestUser = UserEntity.builder()
                .id(testUserId)
                .authProvider(UserEntity.AuthProvider.GUEST)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        ConvertGuestRequestDto request = ConvertGuestRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .displayName("New User")
                .build();
        
        when(userRepository.findById(testUserId)).thenReturn(Optional.of(guestUser));
        when(userRepository.existsByEmail(request.getEmail())).thenReturn(false);
        when(passwordEncoder.encode(request.getPassword())).thenReturn("hashedPassword");
        when(userRepository.save(any(UserEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(jwtUtil.generateToken(anyString(), any(UUID.class))).thenReturn("accessToken");
        when(jwtUtil.generateRefreshToken(anyString(), any(UUID.class))).thenReturn("refreshToken");
        
        AuthResponseDto response = authService.convertGuestToUser(testUserId, request);
        
        assertNotNull(response);
        assertEquals("accessToken", response.getAccessToken());
        assertEquals("refreshToken", response.getRefreshToken());
        
        verify(userRepository).save(argThat(user -> 
            user.getAuthProvider() == UserEntity.AuthProvider.LOCAL &&
            user.getEmail().equals(request.getEmail())
        ));
    }
    
    @Test
    void convertGuestToUser_NotGuestUser_ThrowsBadRequestException() {
        ConvertGuestRequestDto request = ConvertGuestRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .build();
        
        when(userRepository.findById(testUserId)).thenReturn(Optional.of(testUser));
        
        assertThrows(BadRequestException.class, () -> authService.convertGuestToUser(testUserId, request));
    }
    
    @Test
    void refreshToken_Success() {
        String refreshToken = "validRefreshToken";
        
        when(jwtUtil.validateToken(refreshToken)).thenReturn(true);
        when(jwtUtil.extractUserId(refreshToken)).thenReturn(testUserId);
        when(jwtUtil.extractUsername(refreshToken)).thenReturn("<EMAIL>");
        when(userRepository.findById(testUserId)).thenReturn(Optional.of(testUser));
        when(jwtUtil.generateToken(anyString(), any(UUID.class))).thenReturn("newAccessToken");
        when(jwtUtil.generateRefreshToken(anyString(), any(UUID.class))).thenReturn("newRefreshToken");
        
        AuthResponseDto response = authService.refreshToken(refreshToken);
        
        assertNotNull(response);
        assertEquals("newAccessToken", response.getAccessToken());
        assertEquals("newRefreshToken", response.getRefreshToken());
    }
    
    @Test
    void refreshToken_InvalidToken_ThrowsUnauthorizedException() {
        String refreshToken = "invalidRefreshToken";
        
        when(jwtUtil.validateToken(refreshToken)).thenReturn(false);
        
        assertThrows(UnauthorizedException.class, () -> authService.refreshToken(refreshToken));
    }
    
    @Test
    void getCurrentUser_Success() {
        when(userRepository.findById(testUserId)).thenReturn(Optional.of(testUser));
        
        UserDto userDto = authService.getCurrentUser(testUserId);
        
        assertNotNull(userDto);
        assertEquals(testUserId.toString(), userDto.getId());
        assertEquals(testUser.getEmail(), userDto.getEmail());
    }
    
    @Test
    void validateEmail_ValidEmail_ReturnsTrue() {
        assertTrue(authService.validateEmail("<EMAIL>"));
        assertTrue(authService.validateEmail("<EMAIL>"));
    }
    
    @Test
    void validateEmail_InvalidEmail_ReturnsFalse() {
        assertFalse(authService.validateEmail("invalid"));
        assertFalse(authService.validateEmail("@example.com"));
        assertFalse(authService.validateEmail("user@"));
        assertFalse(authService.validateEmail(null));
    }
}