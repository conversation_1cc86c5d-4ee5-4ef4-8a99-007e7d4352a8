package com.foodmagic.recipe.controller;

import com.foodmagic.ai.service.AiServiceClient;
import com.foodmagic.recipe.dto.SmokeTestRequestDto;
import com.foodmagic.recipe.dto.SmokeTestResponseDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SmokeTestControllerTest {
    
    @Mock
    private AiServiceClient aiServiceClient;
    
    @InjectMocks
    private SmokeTestController smokeTestController;
    
    private SmokeTestRequestDto validRequest;
    
    @BeforeEach
    void setUp() {
        validRequest = new SmokeTestRequestDto("番茄");
    }
    
    @Test
    void testGenerateAiResponse_Success() {
        // Arrange
        String expectedAiResponse = "这是一个关于番茄的食谱建议...";
        when(aiServiceClient.generateRecipeSuggestion(anyString()))
            .thenReturn(expectedAiResponse);
        
        // Act
        ResponseEntity<SmokeTestResponseDto> response = 
            smokeTestController.generateAiResponse(validRequest);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("番茄", response.getBody().getIngredient());
        assertEquals(expectedAiResponse, response.getBody().getAiResponse());
        assertNotNull(response.getBody().getTimestamp());
        assertTrue(response.getBody().getProcessingTimeMs() >= 0);
        
        verify(aiServiceClient, times(1)).generateRecipeSuggestion("番茄");
    }
    
    @Test
    void testGenerateAiResponse_ConfigurationError() {
        // Arrange
        when(aiServiceClient.generateRecipeSuggestion(anyString()))
            .thenThrow(new IllegalStateException("AI API key is not configured"));
        
        // Act
        ResponseEntity<SmokeTestResponseDto> response = 
            smokeTestController.generateAiResponse(validRequest);
        
        // Assert
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("番茄", response.getBody().getIngredient());
        assertTrue(response.getBody().getAiResponse().contains("AI服务配置错误"));
    }
    
    @Test
    void testGenerateAiResponse_RuntimeError() {
        // Arrange
        when(aiServiceClient.generateRecipeSuggestion(anyString()))
            .thenThrow(new RuntimeException("Network error"));
        
        // Act
        ResponseEntity<SmokeTestResponseDto> response = 
            smokeTestController.generateAiResponse(validRequest);
        
        // Assert
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("番茄", response.getBody().getIngredient());
        assertTrue(response.getBody().getAiResponse().contains("AI服务暂时不可用"));
    }
    
    @Test
    void testGenerateAiResponse_UnexpectedError() {
        // Arrange
        when(aiServiceClient.generateRecipeSuggestion(anyString()))
            .thenThrow(new NullPointerException("Unexpected null"));
        
        // Act
        ResponseEntity<SmokeTestResponseDto> response = 
            smokeTestController.generateAiResponse(validRequest);
        
        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("番茄", response.getBody().getIngredient());
        assertTrue(response.getBody().getAiResponse().contains("服务器内部错误"));
    }
    
    @Test
    void testHealthCheck() {
        // Act
        ResponseEntity<String> response = smokeTestController.healthCheck();
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Smoke test endpoint is healthy", response.getBody());
    }
    
    @Test
    void testGenerateAiResponse_EmptyIngredient() {
        // Arrange
        SmokeTestRequestDto emptyRequest = new SmokeTestRequestDto("");
        
        // Note: In a real scenario, this would be caught by validation
        // but we'll test the controller's behavior anyway
        when(aiServiceClient.generateRecipeSuggestion(anyString()))
            .thenThrow(new IllegalArgumentException("食材不能为空"));
        
        // Act
        ResponseEntity<SmokeTestResponseDto> response = 
            smokeTestController.generateAiResponse(emptyRequest);
        
        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getAiResponse().contains("错误"));
    }
    
    @Test
    void testGenerateAiResponse_LongProcessingTime() throws InterruptedException {
        // Arrange
        String expectedAiResponse = "长时间处理的响应";
        when(aiServiceClient.generateRecipeSuggestion(anyString()))
            .thenAnswer(invocation -> {
                Thread.sleep(100); // Simulate processing time
                return expectedAiResponse;
            });
        
        // Act
        ResponseEntity<SmokeTestResponseDto> response = 
            smokeTestController.generateAiResponse(validRequest);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getProcessingTimeMs() >= 100);
        assertEquals(expectedAiResponse, response.getBody().getAiResponse());
    }
}