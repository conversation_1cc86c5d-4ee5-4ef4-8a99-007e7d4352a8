package com.foodmagic.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.auth.dto.*;
import com.foodmagic.auth.entity.UserEntity;
import com.foodmagic.auth.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Map;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Testcontainers
@ActiveProfiles("test")
@Transactional
class AuthControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
    }
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @BeforeEach
    void setUp() {
        userRepository.deleteAll();
    }
    
    @Test
    void register_Success() throws Exception {
        RegisterRequestDto request = RegisterRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .displayName("New User")
                .build();
        
        MvcResult result = mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.accessToken").isNotEmpty())
                .andExpect(jsonPath("$.refreshToken").isNotEmpty())
                .andExpect(jsonPath("$.tokenType").value("Bearer"))
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.user.displayName").value("New User"))
                .andReturn();
        
        // Verify user was created in database
        assertTrue(userRepository.existsByEmail("<EMAIL>"));
    }
    
    @Test
    void register_DuplicateEmail_ReturnsConflict() throws Exception {
        // Create existing user
        UserEntity existingUser = UserEntity.builder()
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("password"))
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        userRepository.save(existingUser);
        
        RegisterRequestDto request = RegisterRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .build();
        
        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.message").value("Email already registered"));
    }
    
    @Test
    void register_InvalidEmail_ReturnsBadRequest() throws Exception {
        RegisterRequestDto request = RegisterRequestDto.builder()
                .email("invalid-email")
                .password("password123")
                .build();
        
        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void login_Success() throws Exception {
        // Create user first
        UserEntity user = UserEntity.builder()
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("password123"))
                .displayName("Test User")
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        userRepository.save(user);
        
        LoginRequestDto request = LoginRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .build();
        
        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").isNotEmpty())
                .andExpect(jsonPath("$.refreshToken").isNotEmpty())
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"));
    }
    
    @Test
    void login_InvalidCredentials_ReturnsUnauthorized() throws Exception {
        LoginRequestDto request = LoginRequestDto.builder()
                .email("<EMAIL>")
                .password("wrongpassword")
                .build();
        
        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isUnauthorized());
    }
    
    @Test
    void createGuestSession_Success() throws Exception {
        CreateGuestSessionRequestDto request = new CreateGuestSessionRequestDto();
        request.setDeviceId("test-device-123");
        
        mockMvc.perform(post("/api/v1/auth/session")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.accessToken").isNotEmpty())
                .andExpect(jsonPath("$.refreshToken").isNotEmpty())
                .andExpect(jsonPath("$.user.authProvider").value("GUEST"));
        
        // Verify guest user was created
        assertEquals(1, userRepository.countByAuthProvider(UserEntity.AuthProvider.GUEST));
    }
    
    @Test
    void convertGuestToUser_Success() throws Exception {
        // First create a guest session
        CreateGuestSessionRequestDto guestRequest = new CreateGuestSessionRequestDto();
        
        MvcResult guestResult = mockMvc.perform(post("/api/v1/auth/session")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(guestRequest)))
                .andExpect(status().isCreated())
                .andReturn();
        
        AuthResponseDto guestResponse = objectMapper.readValue(
                guestResult.getResponse().getContentAsString(), 
                AuthResponseDto.class
        );
        
        // Convert guest to registered user
        ConvertGuestRequestDto convertRequest = ConvertGuestRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .displayName("Converted User")
                .build();
        
        mockMvc.perform(post("/api/v1/auth/session/convert")
                .header("Authorization", "Bearer " + guestResponse.getAccessToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(convertRequest)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").isNotEmpty())
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.user.authProvider").value("LOCAL"));
        
        // Verify conversion
        assertTrue(userRepository.existsByEmail("<EMAIL>"));
    }
    
    @Test
    void refreshToken_Success() throws Exception {
        // Register a user first to get tokens
        RegisterRequestDto registerRequest = RegisterRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .build();
        
        MvcResult registerResult = mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isCreated())
                .andReturn();
        
        AuthResponseDto authResponse = objectMapper.readValue(
                registerResult.getResponse().getContentAsString(), 
                AuthResponseDto.class
        );
        
        // Refresh the token
        Map<String, String> refreshRequest = Map.of("refreshToken", authResponse.getRefreshToken());
        
        mockMvc.perform(post("/api/v1/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(refreshRequest)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").isNotEmpty())
                .andExpect(jsonPath("$.refreshToken").isNotEmpty())
                .andExpect(jsonPath("$.accessToken").value(not(authResponse.getAccessToken())));
    }
    
    @Test
    void getCurrentUser_Success() throws Exception {
        // Register a user first
        RegisterRequestDto registerRequest = RegisterRequestDto.builder()
                .email("<EMAIL>")
                .password("password123")
                .displayName("Current User")
                .build();
        
        MvcResult registerResult = mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isCreated())
                .andReturn();
        
        AuthResponseDto authResponse = objectMapper.readValue(
                registerResult.getResponse().getContentAsString(), 
                AuthResponseDto.class
        );
        
        // Get current user
        mockMvc.perform(get("/api/v1/auth/me")
                .header("Authorization", "Bearer " + authResponse.getAccessToken()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.displayName").value("Current User"));
    }
    
    @Test
    void getCurrentUser_Unauthorized() throws Exception {
        mockMvc.perform(get("/api/v1/auth/me"))
                .andDo(print())
                .andExpect(status().isForbidden());
    }
    
    @Test
    void validateEmail_ValidEmail() throws Exception {
        mockMvc.perform(get("/api/v1/auth/validate-email")
                .param("email", "<EMAIL>"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid").value(true));
    }
    
    @Test
    void validateEmail_InvalidEmail() throws Exception {
        mockMvc.perform(get("/api/v1/auth/validate-email")
                .param("email", "invalid-email"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid").value(false));
    }
}