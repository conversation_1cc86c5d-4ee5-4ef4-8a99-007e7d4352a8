# Application Configuration
spring.application.name=FoodMagic
server.port=${SERVER_PORT:8080}

# Database Configuration
spring.datasource.url=${DB_URL:******************************************}
spring.datasource.username=${DB_USER:postgres}
spring.datasource.password=${DB_PASSWORD:postgres}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Redis Configuration
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD:redis_password}

# Security Configuration
jwt.secret=${JWT_SECRET:your_jwt_secret_key_here}
jwt.expiration=86400000

# API Configuration
api.version=v1
api.base-path=/api/${api.version}

# AI Configuration
ai.api.key=${AI_API_KEY:your_ai_api_key}

# Logging Configuration
logging.level.com.foodmagic=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.springframework.security=INFO
