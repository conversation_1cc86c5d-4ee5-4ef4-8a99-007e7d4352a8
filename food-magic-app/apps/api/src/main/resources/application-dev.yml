spring:
  # 开发环境数据源配置
  datasource:
    url: **********************************************
    username: foodmagic_dev
    password: dev123456
  
  # 开发环境 JPA 配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境允许自动更新表结构
    show-sql: true  # 开发环境显示 SQL
    properties:
      hibernate:
        format_sql: true
  
  # 开发环境 Flyway 配置
  flyway:
    clean-disabled: false  # 开发环境允许清理数据库

# 开发环境 JWT 配置
jwt:
  secret: dev-secret-key-for-testing-only-do-not-use-in-production
  expiration: 3600000  # 1 hour for dev
  refresh-expiration: 86400000  # 1 day for dev

# 开发环境服务器配置
server:
  port: 8080
  error:
    include-stacktrace: always  # 开发环境包含堆栈跟踪

# 开发环境日志配置
logging:
  level:
    root: INFO
    com.foodmagic: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.flywaydb: DEBUG
  file:
    name: logs/food-magic-dev.log

# 开发环境 CORS 配置
cors:
  allowed-origins: http://localhost:3000,http://localhost:8081,http://localhost:19006
  
# 开发环境 AI 服务配置
ai:
  service:
    # 智谱AI配置示例
    # api-url: https://open.bigmodel.cn/api/paas/v4/chat/completions
    # 阿里通义千问配置示例
    # api-url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
    # 开发环境需要通过环境变量设置具体的URL

# 开发环境应用配置
app:
  environment: development
  debug: true