spring:
  # 生产环境数据源配置 - 通过环境变量注入
  datasource:
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10
  
  # 生产环境 JPA 配置
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境只验证，不修改表结构
    show-sql: false  # 生产环境不显示 SQL
    properties:
      hibernate:
        format_sql: false
  
  # 生产环境 Flyway 配置
  flyway:
    clean-disabled: true  # 生产环境禁止清理数据库
    out-of-order: false

# 生产环境 JWT 配置 - 必须通过环境变量设置
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:86400000}  # 24 hours
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:2592000000}  # 30 days

# 生产环境服务器配置
server:
  port: ${SERVER_PORT:8080}
  error:
    include-stacktrace: never  # 生产环境不包含堆栈跟踪
    include-message: never
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
    min-response-size: 1024

# 生产环境日志配置
logging:
  level:
    root: WARN
    com.foodmagic: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  file:
    name: ${LOG_PATH:/var/log/food-magic}/food-magic.log
    max-size: 100MB
    max-history: 30

# 生产环境 CORS 配置 - 通过环境变量设置允许的源
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS}

# 生产环境 OpenAPI 配置
springdoc:
  swagger-ui:
    enabled: ${SWAGGER_ENABLED:false}  # 生产环境默认关闭 Swagger

# 生产环境应用配置
app:
  environment: production
  debug: false