-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255),
    display_name VARCHAR(100),
    avatar_url VARCHAR(255),
    auth_provider VARCHAR(50) NOT NULL,
    provider_id VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    subscription_tier VARCHAR(50) NOT NULL DEFAULT 'FREE',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT uk_auth_provider_id UNIQUE (auth_provider, provider_id),
    CONSTRAINT chk_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED')),
    CONSTRAINT chk_subscription_tier CHECK (subscription_tier IN ('FREE', 'PREMIUM')),
    CONSTRAINT chk_auth_provider CHECK (auth_provider IN ('LOCAL', 'GUEST', 'GOOGLE', 'APPLE', 'WECHAT'))
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email) WHERE email IS NOT NULL;
CREATE INDEX idx_users_auth_provider ON users(auth_provider);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE users IS 'User accounts table for authentication and profile management';
COMMENT ON COLUMN users.id IS 'Unique identifier for the user';
COMMENT ON COLUMN users.email IS 'User email address, nullable for guest users';
COMMENT ON COLUMN users.password_hash IS 'Hashed password for local authentication, nullable for OAuth and guest users';
COMMENT ON COLUMN users.display_name IS 'User display name shown in the application';
COMMENT ON COLUMN users.avatar_url IS 'URL to user avatar image';
COMMENT ON COLUMN users.auth_provider IS 'Authentication provider: LOCAL, GUEST, GOOGLE, APPLE, WECHAT';
COMMENT ON COLUMN users.provider_id IS 'External provider user ID for OAuth authentication';
COMMENT ON COLUMN users.status IS 'User account status: ACTIVE, INACTIVE, SUSPENDED, DELETED';
COMMENT ON COLUMN users.subscription_tier IS 'User subscription level: FREE, PREMIUM';
COMMENT ON COLUMN users.created_at IS 'Timestamp when the user account was created';
COMMENT ON COLUMN users.updated_at IS 'Timestamp when the user account was last updated';