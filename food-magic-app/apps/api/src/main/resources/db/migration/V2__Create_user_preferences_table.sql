-- Create user_preferences table
CREATE TABLE user_preferences (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    preferences_json JSONB DEFAULT '{}',
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index for JSONB queries
CREATE INDEX idx_user_preferences_json ON user_preferences USING GIN (preferences_json);

-- Create trigger for user_preferences table
CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE user_preferences IS 'User preferences and settings stored as JSONB';
COMMENT ON COLUMN user_preferences.user_id IS 'Foreign key reference to users table';
COMMENT ON COLUMN user_preferences.preferences_json IS 'User preferences stored as J<PERSON><PERSON><PERSON> including: allergies, isVegetarian, diet, preferredCuisines, unitSystem, locale';
COMMENT ON COLUMN user_preferences.updated_at IS 'Timestamp when preferences were last updated';

-- Insert default preferences for new users (handled by application logic but this is backup)
CREATE OR REPLACE FUNCTION create_default_user_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_preferences (user_id, preferences_json)
    VALUES (NEW.id, '{
        "allergies": [],
        "isVegetarian": false,
        "diet": null,
        "preferredCuisines": [],
        "unitSystem": "METRIC",
        "locale": "zh-CN"
    }')
    ON CONFLICT (user_id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create preferences for new users
CREATE TRIGGER create_user_preferences_on_user_insert
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_user_preferences();