package com.foodmagic.ai.service;

import java.util.concurrent.CompletableFuture;

/**
 * AI服务客户端接口
 * 封装与外部AI大语言模型通信的复杂逻辑
 */
public interface AiServiceClient {
    
    /**
     * 生成AI响应
     * @param prompt 用户提示词
     * @return AI响应的原始文本
     */
    String generateResponse(String prompt);
    
    /**
     * 异步生成AI响应
     * @param prompt 用户提示词
     * @return AI响应的CompletableFuture
     */
    CompletableFuture<String> generateResponseAsync(String prompt);
    
    /**
     * 根据食材生成食谱建议
     * @param ingredient 食材
     * @return 食谱建议的原始文本
     */
    String generateRecipeSuggestion(String ingredient);
}