package com.foodmagic.ai.interceptor;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 包装ClientHttpResponse以支持多次读取响应体
 */
public class BufferingClientHttpResponseWrapper implements ClientHttpResponse {
    
    private final ClientHttpResponse response;
    private byte[] body;
    
    public BufferingClientHttpResponseWrapper(ClientHttpResponse response) throws IOException {
        this.response = response;
        // 缓存响应体
        this.body = StreamUtils.copyToByteArray(response.getBody());
    }
    
    @Override
    public HttpStatusCode getStatusCode() throws IOException {
        return response.getStatusCode();
    }
    
    @Override
    public String getStatusText() throws IOException {
        return response.getStatusText();
    }
    
    @Override
    public void close() {
        response.close();
    }
    
    @Override
    public InputStream getBody() throws IOException {
        return new ByteArrayInputStream(body);
    }
    
    @Override
    public HttpHeaders getHeaders() {
        return response.getHeaders();
    }
}