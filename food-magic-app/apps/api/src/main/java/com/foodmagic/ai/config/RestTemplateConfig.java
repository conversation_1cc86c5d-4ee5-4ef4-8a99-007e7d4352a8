package com.foodmagic.ai.config;

import com.foodmagic.ai.interceptor.AiApiLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * RestTemplate配置类
 * 用于HTTP客户端配置
 */
@Configuration
public class RestTemplateConfig {
    
    @Autowired
    private AiConfig aiConfig;
    
    @Autowired
    private AiApiLoggingInterceptor loggingInterceptor;
    
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        // 创建支持多次读取响应体的请求工厂
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(aiConfig.getTimeout());
        BufferingClientHttpRequestFactory bufferingFactory = new BufferingClientHttpRequestFactory(factory);
        
        return builder
            .requestFactory(() -> bufferingFactory)
            .interceptors(loggingInterceptor)
            .build();
    }
}