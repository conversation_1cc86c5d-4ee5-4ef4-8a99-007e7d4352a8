package com.foodmagic.auth.service;

import com.foodmagic.auth.dto.*;

import java.util.UUID;

public interface AuthService {
    
    AuthResponseDto register(RegisterRequestDto request);
    
    AuthResponseDto login(LoginRequestDto request);
    
    AuthResponseDto createGuestSession(CreateGuestSessionRequestDto request);
    
    AuthResponseDto convertGuestToUser(UUID guestUserId, ConvertGuestRequestDto request);
    
    AuthResponseDto refreshToken(String refreshToken);
    
    UserDto getCurrentUser(UUID userId);
    
    void logout(UUID userId);
    
    boolean validateEmail(String email);
}