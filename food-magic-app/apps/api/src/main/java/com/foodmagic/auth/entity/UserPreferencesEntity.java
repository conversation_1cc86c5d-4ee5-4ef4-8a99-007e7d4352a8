package com.foodmagic.auth.entity;

import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "user_preferences")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode(of = {"userId"})
public class UserPreferencesEntity {
    
    @Id
    @Column(name = "user_id", nullable = false)
    private UUID userId;
    
    @OneToOne(fetch = FetchType.LAZY)
    @MapsId
    @JoinColumn(name = "user_id")
    private UserEntity user;
    
    @Type(JsonType.class)
    @Column(name = "preferences_json", columnDefinition = "jsonb")
    @Builder.Default
    private JsonNode preferencesJson = createDefaultPreferences();
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;
    
    private static JsonNode createDefaultPreferences() {
        var factory = com.fasterxml.jackson.databind.node.JsonNodeFactory.instance;
        var objectNode = factory.objectNode();
        
        objectNode.set("allergies", factory.arrayNode());
        objectNode.put("isVegetarian", false);
        objectNode.putNull("diet");
        objectNode.set("preferredCuisines", factory.arrayNode());
        objectNode.put("unitSystem", "METRIC");
        objectNode.put("locale", "zh-CN");
        
        return objectNode;
    }
    
    @PrePersist
    protected void onCreate() {
        if (preferencesJson == null) {
            preferencesJson = createDefaultPreferences();
        }
    }
}