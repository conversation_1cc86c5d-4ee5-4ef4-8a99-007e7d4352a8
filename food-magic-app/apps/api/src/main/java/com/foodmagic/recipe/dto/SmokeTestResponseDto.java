package com.foodmagic.recipe.dto;

import java.time.LocalDateTime;

/**
 * 冒烟测试响应DTO
 */
public class SmokeTestResponseDto {
    
    private String ingredient;
    private String aiResponse;
    private LocalDateTime timestamp;
    private Long processingTimeMs;
    
    public SmokeTestResponseDto() {
        this.timestamp = LocalDateTime.now();
    }
    
    public SmokeTestResponseDto(String ingredient, String aiResponse, Long processingTimeMs) {
        this.ingredient = ingredient;
        this.aiResponse = aiResponse;
        this.timestamp = LocalDateTime.now();
        this.processingTimeMs = processingTimeMs;
    }
    
    public String getIngredient() {
        return ingredient;
    }
    
    public void setIngredient(String ingredient) {
        this.ingredient = ingredient;
    }
    
    public String getAiResponse() {
        return aiResponse;
    }
    
    public void setAiResponse(String aiResponse) {
        this.aiResponse = aiResponse;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }
    
    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
    
    @Override
    public String toString() {
        return "SmokeTestResponseDto{" +
                "ingredient='" + ingredient + '\'' +
                ", aiResponse='" + (aiResponse != null ? aiResponse.substring(0, Math.min(aiResponse.length(), 50)) + "..." : null) + '\'' +
                ", timestamp=" + timestamp +
                ", processingTimeMs=" + processingTimeMs +
                '}';
    }
}