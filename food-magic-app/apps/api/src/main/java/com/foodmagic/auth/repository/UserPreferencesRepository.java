package com.foodmagic.auth.repository;

import com.foodmagic.auth.entity.UserPreferencesEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface UserPreferencesRepository extends JpaRepository<UserPreferencesEntity, UUID> {
    
    @Modifying
    @Query(value = "UPDATE user_preferences SET preferences_json = jsonb_set(preferences_json, :path, :value) WHERE user_id = :userId", 
           nativeQuery = true)
    void updateJsonField(@Param("userId") UUID userId, @Param("path") String path, @Param("value") String value);
    
    @Query(value = "SELECT preferences_json->:key FROM user_preferences WHERE user_id = :userId", 
           nativeQuery = true)
    String getJsonField(@Param("userId") UUID userId, @Param("key") String key);
    
    @Modifying
    @Query(value = "UPDATE user_preferences SET preferences_json = preferences_json || :jsonPatch::jsonb WHERE user_id = :userId", 
           nativeQuery = true)
    void patchPreferences(@Param("userId") UUID userId, @Param("jsonPatch") String jsonPatch);
}