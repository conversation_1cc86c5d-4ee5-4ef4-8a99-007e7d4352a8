package com.foodmagic.recipe.controller;

import com.foodmagic.ai.service.AiServiceClient;
import com.foodmagic.recipe.dto.SmokeTestRequestDto;
import com.foodmagic.recipe.dto.SmokeTestResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 冒烟测试控制器
 * 用于验证端到端技术栈连通性
 */
@RestController
@RequestMapping("/api/v1/smoke-test")
@Tag(name = "Smoke Test", description = "冒烟测试API - 验证端到端技术栈")
@Validated
public class SmokeTestController {
    
    private static final Logger logger = LoggerFactory.getLogger(SmokeTestController.class);
    
    @Autowired
    private AiServiceClient aiServiceClient;
    
    @PostMapping("/generate")
    @Operation(
        summary = "生成AI响应",
        description = "根据输入的食材调用AI服务并返回原始响应文本"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功生成AI响应",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = SmokeTestResponseDto.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数错误",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "503",
            description = "AI服务不可用",
            content = @Content(mediaType = "application/json")
        )
    })
    public ResponseEntity<SmokeTestResponseDto> generateAiResponse(
            @Valid @RequestBody SmokeTestRequestDto request) {
        
        logger.info("Received smoke test request for ingredient: {}", request.getIngredient());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 调用AI服务
            String aiResponse = aiServiceClient.generateRecipeSuggestion(request.getIngredient());
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            // 构建响应
            SmokeTestResponseDto response = new SmokeTestResponseDto(
                request.getIngredient(),
                aiResponse,
                processingTime
            );
            
            logger.info("Successfully generated AI response for ingredient: {} in {}ms", 
                request.getIngredient(), processingTime);
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalStateException e) {
            // 配置错误
            logger.error("Configuration error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(createErrorResponse(request.getIngredient(), "AI服务配置错误: " + e.getMessage(), startTime));
                
        } catch (RuntimeException e) {
            // AI服务调用失败
            logger.error("Failed to generate AI response: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(createErrorResponse(request.getIngredient(), "AI服务暂时不可用: " + e.getMessage(), startTime));
                
        } catch (Exception e) {
            // 其他未预期错误
            logger.error("Unexpected error: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(createErrorResponse(request.getIngredient(), "服务器内部错误: " + e.getMessage(), startTime));
        }
    }
    
    @GetMapping("/health")
    @Operation(
        summary = "健康检查",
        description = "检查冒烟测试端点是否正常工作"
    )
    @ApiResponse(
        responseCode = "200",
        description = "服务正常",
        content = @Content(mediaType = "text/plain")
    )
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Smoke test endpoint is healthy");
    }
    
    private SmokeTestResponseDto createErrorResponse(String ingredient, String errorMessage, long startTime) {
        SmokeTestResponseDto response = new SmokeTestResponseDto();
        response.setIngredient(ingredient);
        response.setAiResponse("错误: " + errorMessage);
        response.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        return response;
    }
}