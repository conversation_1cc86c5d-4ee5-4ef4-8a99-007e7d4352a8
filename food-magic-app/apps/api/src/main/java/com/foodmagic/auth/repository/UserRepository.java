package com.foodmagic.auth.repository;

import com.foodmagic.auth.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, UUID> {
    
    Optional<UserEntity> findByEmail(String email);
    
    Optional<UserEntity> findByAuthProviderAndProviderId(
        UserEntity.AuthProvider authProvider, 
        String providerId
    );
    
    boolean existsByEmail(String email);
    
    @Query("SELECT u FROM UserEntity u LEFT JOIN FETCH u.preferences WHERE u.id = :id")
    Optional<UserEntity> findByIdWithPreferences(@Param("id") UUID id);
    
    @Query("SELECT u FROM UserEntity u LEFT JOIN FETCH u.preferences WHERE u.email = :email")
    Optional<UserEntity> findByEmailWithPreferences(@Param("email") String email);
    
    @Query("SELECT COUNT(u) FROM UserEntity u WHERE u.authProvider = :provider")
    long countByAuthProvider(@Param("provider") UserEntity.AuthProvider provider);
    
    @Query("SELECT u FROM UserEntity u WHERE u.authProvider = 'GUEST' AND u.createdAt < :expirationTime")
    java.util.List<UserEntity> findExpiredGuestUsers(@Param("expirationTime") java.time.Instant expirationTime);
}