package com.foodmagic.ai.config;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeoutException;

/**
 * Resilience4j配置类
 * 配置断路器、重试等弹性机制
 */
@Configuration
public class Resilience4jConfig {
    
    /**
     * 配置AI服务断路器
     */
    @Bean
    public CircuitBreaker aiServiceCircuitBreaker() {
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
            .failureRateThreshold(50) // 失败率阈值：50%
            .waitDurationInOpenState(Duration.ofSeconds(30)) // 断路器打开后等待30秒
            .slidingWindowSize(10) // 滑动窗口大小：10次调用
            .minimumNumberOfCalls(5) // 最少调用次数：5次
            .permittedNumberOfCallsInHalfOpenState(3) // 半开状态允许通过的调用次数
            .automaticTransitionFromOpenToHalfOpenEnabled(true) // 自动从打开状态转换到半开状态
            .recordExceptions(IOException.class, TimeoutException.class, RuntimeException.class) // 记录的异常类型
            .build();
        
        CircuitBreakerRegistry registry = CircuitBreakerRegistry.of(config);
        return registry.circuitBreaker("aiService");
    }
    
    /**
     * 配置AI服务重试机制
     */
    @Bean
    public Retry aiServiceRetry() {
        RetryConfig config = RetryConfig.custom()
            .maxAttempts(3) // 最大重试次数
            .waitDuration(Duration.ofMillis(1000)) // 重试间隔
            .intervalFunction(intervalAttempt -> {
                // 指数退避：1s, 2s, 4s
                return Duration.ofMillis(1000L * (1L << (intervalAttempt - 1))).toMillis();
            })
            .retryExceptions(IOException.class, TimeoutException.class) // 需要重试的异常
            .ignoreExceptions(IllegalArgumentException.class) // 不需要重试的异常
            .build();
        
        RetryRegistry registry = RetryRegistry.of(config);
        return registry.retry("aiService");
    }
}