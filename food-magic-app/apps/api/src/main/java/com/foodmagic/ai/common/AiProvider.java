package com.foodmagic.ai.common;

/**
 * AI服务提供商枚举
 */
public enum AiProvider {
    ZHIPU("zhipu", "智谱AI"),
    QWEN("qwen", "阿里通义千问"),
    DEEPSEEK("deepseek", "DeepSeek"),
    GEMINI("gemini", "Google Gemini"),
    OPENAI("openai", "OpenAI");
    
    private final String value;
    private final String displayName;
    
    AiProvider(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public static AiProvider fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (AiProvider provider : AiProvider.values()) {
            if (provider.value.equalsIgnoreCase(value)) {
                return provider;
            }
        }
        throw new IllegalArgumentException("Unknown AI provider: " + value);
    }
}