package com.foodmagic.ai.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * AI API请求响应日志拦截器
 * 提供请求和响应的日志记录，同时对敏感信息进行脱敏处理
 */
@Component
public class AiApiLoggingInterceptor implements ClientHttpRequestInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(AiApiLoggingInterceptor.class);
    
    // 用于脱敏的正则表达式
    private static final Pattern API_KEY_PATTERN = Pattern.compile(
        "(Bearer\\s+|api[_-]?key[\"']?[:=]\\s*[\"']?)([A-Za-z0-9-._~+/]+=*)",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final int MAX_LOG_LENGTH = 1000; // 最大日志长度
    
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, 
                                       ClientHttpRequestExecution execution) throws IOException {
        
        long startTime = System.currentTimeMillis();
        
        // 记录请求信息（脱敏处理）
        logRequest(request, body);
        
        ClientHttpResponse response = execution.execute(request, body);
        
        // 记录响应信息
        long duration = System.currentTimeMillis() - startTime;
        logResponse(response, duration);
        
        return response;
    }
    
    private void logRequest(HttpRequest request, byte[] body) {
        if (logger.isDebugEnabled()) {
            String requestBody = new String(body, StandardCharsets.UTF_8);
            String sanitizedBody = sanitizeContent(requestBody);
            
            // 截断过长的内容
            if (sanitizedBody.length() > MAX_LOG_LENGTH) {
                sanitizedBody = sanitizedBody.substring(0, MAX_LOG_LENGTH) + "... [truncated]";
            }
            
            logger.debug("AI API Request: {} {} - Headers: {} - Body: {}", 
                request.getMethod(),
                request.getURI(),
                sanitizeHeaders(request.getHeaders().toString()),
                sanitizedBody
            );
        }
    }
    
    private void logResponse(ClientHttpResponse response, long duration) throws IOException {
        if (logger.isDebugEnabled()) {
            // 读取响应体（注意：这会消耗流，需要包装响应以支持多次读取）
            String responseBody = StreamUtils.copyToString(response.getBody(), StandardCharsets.UTF_8);
            String sanitizedBody = sanitizeContent(responseBody);
            
            // 截断过长的内容
            if (sanitizedBody.length() > MAX_LOG_LENGTH) {
                sanitizedBody = sanitizedBody.substring(0, MAX_LOG_LENGTH) + "... [truncated]";
            }
            
            logger.debug("AI API Response: Status {} - Duration: {}ms - Body: {}", 
                response.getStatusCode(),
                duration,
                sanitizedBody
            );
        }
        
        // 对于非调试模式，只记录基本信息
        if (!logger.isDebugEnabled() && logger.isInfoEnabled()) {
            logger.info("AI API Call completed: Status {} - Duration: {}ms", 
                response.getStatusCode(), duration);
        }
    }
    
    /**
     * 对敏感内容进行脱敏处理
     */
    private String sanitizeContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 替换API密钥
        content = API_KEY_PATTERN.matcher(content).replaceAll("$1[REDACTED]");
        
        // 替换可能的密码字段
        content = content.replaceAll(
            "\"(password|secret|token|key)\"\\s*:\\s*\"[^\"]+\"",
            "\"$1\":\"[REDACTED]\""
        );
        
        return content;
    }
    
    /**
     * 对请求头进行脱敏处理
     */
    private String sanitizeHeaders(String headers) {
        if (headers == null) {
            return "";
        }
        
        // 替换Authorization头
        headers = headers.replaceAll(
            "Authorization=\\[Bearer [^\\]]+\\]",
            "Authorization=[Bearer [REDACTED]]"
        );
        
        return headers;
    }
}