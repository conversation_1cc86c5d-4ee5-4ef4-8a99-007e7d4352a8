package com.foodmagic.ai.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 重试处理器，实现指数退避策略
 */
@Component
public class RetryHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(RetryHandler.class);
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    /**
     * 执行带重试的异步操作
     * 
     * @param operation 要执行的操作
     * @param maxRetries 最大重试次数
     * @param initialDelayMs 初始延迟（毫秒）
     * @param maxDelayMs 最大延迟（毫秒）
     * @param <T> 返回类型
     * @return CompletableFuture
     */
    public <T> CompletableFuture<T> executeWithRetry(
            Supplier<T> operation,
            int maxRetries,
            long initialDelayMs,
            long maxDelayMs) {
        
        return executeWithRetryInternal(operation, 0, maxRetries, initialDelayMs, maxDelayMs);
    }
    
    private <T> CompletableFuture<T> executeWithRetryInternal(
            Supplier<T> operation,
            int attemptNumber,
            int maxRetries,
            long initialDelayMs,
            long maxDelayMs) {
        
        return CompletableFuture
            .supplyAsync(operation)
            .exceptionally(throwable -> {
                if (attemptNumber >= maxRetries - 1) {
                    logger.error("All {} retry attempts exhausted", maxRetries);
                    throw new RuntimeException("Max retries exceeded: " + throwable.getMessage(), throwable);
                }
                
                long delay = calculateExponentialBackoff(attemptNumber, initialDelayMs, maxDelayMs);
                logger.warn("Attempt {} failed, retrying after {}ms: {}", 
                    attemptNumber + 1, delay, throwable.getMessage());
                
                CompletableFuture<T> future = new CompletableFuture<>();
                scheduler.schedule(() -> {
                    executeWithRetryInternal(operation, attemptNumber + 1, maxRetries, initialDelayMs, maxDelayMs)
                        .whenComplete((result, error) -> {
                            if (error != null) {
                                future.completeExceptionally(error);
                            } else {
                                future.complete(result);
                            }
                        });
                }, delay, TimeUnit.MILLISECONDS);
                
                try {
                    return future.get();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
    }
    
    /**
     * 计算指数退避延迟时间
     * 
     * @param attemptNumber 当前重试次数
     * @param initialDelayMs 初始延迟
     * @param maxDelayMs 最大延迟
     * @return 延迟时间（毫秒）
     */
    private long calculateExponentialBackoff(int attemptNumber, long initialDelayMs, long maxDelayMs) {
        // 指数退避: delay = min(initialDelay * 2^attempt, maxDelay)
        long delay = initialDelayMs * (1L << attemptNumber);
        
        // 添加抖动以避免雷鸣群效应
        long jitter = (long) (Math.random() * delay * 0.1);
        delay = delay + jitter;
        
        return Math.min(delay, maxDelayMs);
    }
    
    /**
     * 关闭调度器
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}