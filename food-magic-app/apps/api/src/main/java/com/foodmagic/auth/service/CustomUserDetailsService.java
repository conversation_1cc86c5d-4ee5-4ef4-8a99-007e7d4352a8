package com.foodmagic.auth.service;

import com.foodmagic.auth.entity.UserEntity;
import com.foodmagic.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CustomUserDetailsService implements UserDetailsService {
    
    private final UserRepository userRepository;
    
    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserEntity user;
        
        // Check if username is a UUID (for guest users or ID-based auth)
        try {
            UUID userId = UUID.fromString(username);
            user = userRepository.findById(userId)
                    .orElseThrow(() -> new UsernameNotFoundException("User not found with id: " + username));
        } catch (IllegalArgumentException e) {
            // Not a UUID, treat as email
            user = userRepository.findByEmail(username)
                    .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + username));
        }
        
        if (!user.canLogin()) {
            throw new UsernameNotFoundException("User is not allowed to login: " + username);
        }
        
        List<SimpleGrantedAuthority> authorities = Collections.singletonList(
                new SimpleGrantedAuthority("ROLE_USER")
        );
        
        // For guest users without email, use UUID as username
        String effectiveUsername = user.hasEmail() ? user.getEmail() : user.getId().toString();
        
        return User.builder()
                .username(effectiveUsername)
                .password(user.getPasswordHash() != null ? user.getPasswordHash() : "")
                .authorities(authorities)
                .accountExpired(false)
                .accountLocked(user.getStatus() == UserEntity.UserStatus.SUSPENDED)
                .credentialsExpired(false)
                .disabled(user.getStatus() != UserEntity.UserStatus.ACTIVE)
                .build();
    }
    
    @Transactional(readOnly = true)
    public UserDetails loadUserById(UUID userId) throws UsernameNotFoundException {
        return loadUserByUsername(userId.toString());
    }
}