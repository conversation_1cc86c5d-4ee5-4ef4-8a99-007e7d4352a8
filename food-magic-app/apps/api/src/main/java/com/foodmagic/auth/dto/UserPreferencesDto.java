package com.foodmagic.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserPreferencesDto {
    private List<String> allergies;
    private Boolean isVegetarian;
    private String diet;
    private List<String> preferredCuisines;
    private String unitSystem;
    private String locale;
}