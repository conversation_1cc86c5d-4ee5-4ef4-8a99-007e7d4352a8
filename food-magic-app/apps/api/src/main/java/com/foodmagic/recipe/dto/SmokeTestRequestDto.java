package com.foodmagic.recipe.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 冒烟测试请求DTO
 */
public class SmokeTestRequestDto {
    
    @NotBlank(message = "食材不能为空")
    @Size(min = 1, max = 100, message = "食材长度必须在1-100个字符之间")
    private String ingredient;
    
    public SmokeTestRequestDto() {
    }
    
    public SmokeTestRequestDto(String ingredient) {
        this.ingredient = ingredient;
    }
    
    public String getIngredient() {
        return ingredient;
    }
    
    public void setIngredient(String ingredient) {
        this.ingredient = ingredient;
    }
    
    @Override
    public String toString() {
        return "SmokeTestRequestDto{" +
                "ingredient='" + ingredient + '\'' +
                '}';
    }
}