package com.foodmagic.auth.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.auth.dto.*;
import com.foodmagic.auth.entity.UserEntity;
import com.foodmagic.auth.entity.UserPreferencesEntity;
import com.foodmagic.auth.repository.UserPreferencesRepository;
import com.foodmagic.auth.repository.UserRepository;
import com.foodmagic.common.exception.BadRequestException;
import com.foodmagic.common.exception.ConflictException;
import com.foodmagic.common.exception.UnauthorizedException;
import com.foodmagic.common.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {
    
    private final UserRepository userRepository;
    private final UserPreferencesRepository userPreferencesRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final AuthenticationManager authenticationManager;
    private final ObjectMapper objectMapper;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    // Validation constants
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    );
    private static final int MIN_PASSWORD_LENGTH = 8;
    private static final int MAX_PASSWORD_LENGTH = 128;
    private static final int MAX_DISPLAY_NAME_LENGTH = 100;
    
    @Override
    @Transactional
    public AuthResponseDto register(RegisterRequestDto request) {
        log.debug("Registering new user with email: {}", request.getEmail());
        
        // Validate email format
        if (!validateEmail(request.getEmail())) {
            throw new BadRequestException("Invalid email format");
        }
        
        // Validate password strength
        if (request.getPassword() == null || 
            request.getPassword().length() < MIN_PASSWORD_LENGTH ||
            request.getPassword().length() > MAX_PASSWORD_LENGTH) {
            throw new BadRequestException(
                String.format("Password must be between %d and %d characters", 
                    MIN_PASSWORD_LENGTH, MAX_PASSWORD_LENGTH)
            );
        }
        
        // Validate display name length if provided
        if (request.getDisplayName() != null && 
            request.getDisplayName().length() > MAX_DISPLAY_NAME_LENGTH) {
            throw new BadRequestException(
                String.format("Display name must not exceed %d characters", 
                    MAX_DISPLAY_NAME_LENGTH)
            );
        }
        
        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ConflictException("Email already registered");
        }
        
        // Create new user entity
        UserEntity user = UserEntity.builder()
                .email(request.getEmail())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .displayName(request.getDisplayName() != null ? request.getDisplayName() : extractNameFromEmail(request.getEmail()))
                .authProvider(UserEntity.AuthProvider.LOCAL)
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        user = userRepository.save(user);
        
        // Create default preferences
        UserPreferencesEntity preferences = UserPreferencesEntity.builder()
                .user(user)
                .userId(user.getId())
                .build();
        userPreferencesRepository.save(preferences);
        
        // Generate JWT tokens
        String accessToken = jwtUtil.generateToken(user.getEmail(), user.getId());
        String refreshToken = jwtUtil.generateRefreshToken(user.getEmail(), user.getId());
        
        log.info("User registered successfully with ID: {}", user.getId());
        
        return AuthResponseDto.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtExpiration)
                .user(convertToUserDto(user))
                .build();
    }
    
    @Override
    public AuthResponseDto login(LoginRequestDto request) {
        log.debug("User login attempt for email: {}", request.getEmail());
        
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
            );
            
            // Get user from database
            UserEntity user = userRepository.findByEmail(request.getEmail())
                    .orElseThrow(() -> new UnauthorizedException("Invalid credentials"));
            
            // Generate JWT tokens
            String accessToken = jwtUtil.generateToken(user.getEmail(), user.getId());
            String refreshToken = jwtUtil.generateRefreshToken(user.getEmail(), user.getId());
            
            log.info("User logged in successfully: {}", user.getId());
            
            return AuthResponseDto.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(jwtExpiration)
                    .user(convertToUserDto(user))
                    .build();
                    
        } catch (AuthenticationException e) {
            log.warn("Failed login attempt for email: {}", request.getEmail());
            throw new UnauthorizedException("Invalid credentials");
        }
    }
    
    @Override
    @Transactional
    public AuthResponseDto createGuestSession(CreateGuestSessionRequestDto request) {
        log.debug("Creating guest session");
        
        // Create guest user
        UserEntity guestUser = UserEntity.builder()
                .authProvider(UserEntity.AuthProvider.GUEST)
                .providerId(request.getDeviceId() != null ? request.getDeviceId() : UUID.randomUUID().toString())
                .displayName("Guest User")
                .status(UserEntity.UserStatus.ACTIVE)
                .subscriptionTier(UserEntity.SubscriptionTier.FREE)
                .build();
        
        guestUser = userRepository.save(guestUser);
        
        // Create default preferences
        UserPreferencesEntity preferences = UserPreferencesEntity.builder()
                .user(guestUser)
                .userId(guestUser.getId())
                .build();
        userPreferencesRepository.save(preferences);
        
        // Generate JWT tokens (using UUID as username for guest)
        String accessToken = jwtUtil.generateToken(guestUser.getId().toString(), guestUser.getId());
        String refreshToken = jwtUtil.generateRefreshToken(guestUser.getId().toString(), guestUser.getId());
        
        log.info("Guest session created with ID: {}", guestUser.getId());
        
        return AuthResponseDto.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtExpiration)
                .user(convertToUserDto(guestUser))
                .build();
    }
    
    @Override
    @Transactional
    public AuthResponseDto convertGuestToUser(UUID guestUserId, ConvertGuestRequestDto request) {
        log.debug("Converting guest user {} to registered user", guestUserId);
        
        // Find guest user
        UserEntity guestUser = userRepository.findById(guestUserId)
                .orElseThrow(() -> new BadRequestException("Guest user not found"));
        
        // Verify it's a guest user
        if (guestUser.getAuthProvider() != UserEntity.AuthProvider.GUEST) {
            throw new BadRequestException("User is not a guest");
        }
        
        // Validate email format
        if (!validateEmail(request.getEmail())) {
            throw new BadRequestException("Invalid email format");
        }
        
        // Validate password strength
        if (request.getPassword() == null || 
            request.getPassword().length() < MIN_PASSWORD_LENGTH ||
            request.getPassword().length() > MAX_PASSWORD_LENGTH) {
            throw new BadRequestException(
                String.format("Password must be between %d and %d characters", 
                    MIN_PASSWORD_LENGTH, MAX_PASSWORD_LENGTH)
            );
        }
        
        // Validate display name length if provided
        if (request.getDisplayName() != null && 
            request.getDisplayName().length() > MAX_DISPLAY_NAME_LENGTH) {
            throw new BadRequestException(
                String.format("Display name must not exceed %d characters", 
                    MAX_DISPLAY_NAME_LENGTH)
            );
        }
        
        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ConflictException("Email already registered");
        }
        
        // Update guest user to registered user
        guestUser.setEmail(request.getEmail());
        guestUser.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        guestUser.setAuthProvider(UserEntity.AuthProvider.LOCAL);
        if (request.getDisplayName() != null) {
            guestUser.setDisplayName(request.getDisplayName());
        } else {
            guestUser.setDisplayName(extractNameFromEmail(request.getEmail()));
        }
        
        guestUser = userRepository.save(guestUser);
        
        // Generate new JWT tokens
        String accessToken = jwtUtil.generateToken(guestUser.getEmail(), guestUser.getId());
        String refreshToken = jwtUtil.generateRefreshToken(guestUser.getEmail(), guestUser.getId());
        
        log.info("Guest user {} converted to registered user", guestUser.getId());
        
        return AuthResponseDto.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtExpiration)
                .user(convertToUserDto(guestUser))
                .build();
    }
    
    @Override
    public AuthResponseDto refreshToken(String refreshToken) {
        log.debug("Refreshing token");
        
        // Validate refresh token
        if (!jwtUtil.validateToken(refreshToken)) {
            throw new UnauthorizedException("Invalid refresh token");
        }
        
        // Extract user information
        UUID userId = jwtUtil.extractUserId(refreshToken);
        String username = jwtUtil.extractUsername(refreshToken);
        
        // Find user
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new UnauthorizedException("User not found"));
        
        // Generate new tokens
        String newAccessToken = jwtUtil.generateToken(username, userId);
        String newRefreshToken = jwtUtil.generateRefreshToken(username, userId);
        
        log.info("Token refreshed for user: {}", userId);
        
        return AuthResponseDto.builder()
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtExpiration)
                .user(convertToUserDto(user))
                .build();
    }
    
    @Override
    @Transactional(readOnly = true)
    public UserDto getCurrentUser(UUID userId) {
        log.debug("Getting current user: {}", userId);
        
        UserEntity user = userRepository.findById(userId)
                .orElseThrow(() -> new BadRequestException("User not found"));
        
        return convertToUserDto(user);
    }
    
    @Override
    public void logout(UUID userId) {
        log.info("User {} logged out", userId);
        // In a stateless JWT system, logout is typically handled client-side
        // You could implement a token blacklist here if needed
    }
    
    @Override
    public boolean validateEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
    
    private UserDto convertToUserDto(UserEntity user) {
        return UserDto.builder()
                .id(user.getId().toString())
                .email(user.getEmail())
                .displayName(user.getDisplayName())
                .avatarUrl(user.getAvatarUrl())
                .subscriptionTier(user.getSubscriptionTier().toString())
                .status(user.getStatus().toString())
                .authProvider(user.getAuthProvider().toString())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }
    
    private String extractNameFromEmail(String email) {
        return email.substring(0, email.indexOf('@'));
    }
}