package com.foodmagic.auth.controller;

import com.foodmagic.auth.dto.*;
import com.foodmagic.auth.service.AuthService;
import com.foodmagic.common.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Authentication", description = "Authentication and authorization endpoints")
public class AuthController {
    
    private final AuthService authService;
    private final JwtUtil jwtUtil;
    
    @PostMapping("/register")
    @Operation(summary = "Register a new user", description = "Create a new user account with email and password")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User registered successfully",
            content = @Content(schema = @Schema(implementation = AuthResponseDto.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "409", description = "Email already exists")
    })
    public ResponseEntity<AuthResponseDto> register(@Valid @RequestBody RegisterRequestDto request) {
        log.info("Registration request for email: {}", request.getEmail());
        AuthResponseDto response = authService.register(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user with email and password")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Login successful",
            content = @Content(schema = @Schema(implementation = AuthResponseDto.class))),
        @ApiResponse(responseCode = "401", description = "Invalid credentials")
    })
    public ResponseEntity<AuthResponseDto> login(@Valid @RequestBody LoginRequestDto request) {
        log.info("Login request for email: {}", request.getEmail());
        AuthResponseDto response = authService.login(request);
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/session")
    @Operation(summary = "Create guest session", description = "Create a temporary guest user session")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Guest session created successfully",
            content = @Content(schema = @Schema(implementation = AuthResponseDto.class)))
    })
    public ResponseEntity<AuthResponseDto> createGuestSession(@RequestBody(required = false) CreateGuestSessionRequestDto request) {
        log.info("Creating guest session");
        if (request == null) {
            request = new CreateGuestSessionRequestDto();
        }
        AuthResponseDto response = authService.createGuestSession(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PostMapping("/session/convert")
    @Operation(summary = "Convert guest to user", description = "Convert a guest session to a registered user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Guest converted successfully",
            content = @Content(schema = @Schema(implementation = AuthResponseDto.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request or not a guest user"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "409", description = "Email already exists")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<AuthResponseDto> convertGuestToUser(
            @Valid @RequestBody ConvertGuestRequestDto request,
            HttpServletRequest httpRequest) {
        
        UUID userId = extractUserIdFromToken(httpRequest);
        log.info("Converting guest user {} to registered user", userId);
        AuthResponseDto response = authService.convertGuestToUser(userId, request);
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/refresh")
    @Operation(summary = "Refresh access token", description = "Get a new access token using refresh token")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Token refreshed successfully",
            content = @Content(schema = @Schema(implementation = AuthResponseDto.class))),
        @ApiResponse(responseCode = "401", description = "Invalid refresh token")
    })
    public ResponseEntity<AuthResponseDto> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");
        if (refreshToken == null || refreshToken.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
        log.info("Refreshing token");
        AuthResponseDto response = authService.refreshToken(refreshToken);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/me")
    @Operation(summary = "Get current user", description = "Get the currently authenticated user's information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User information retrieved",
            content = @Content(schema = @Schema(implementation = UserDto.class))),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<UserDto> getCurrentUser(HttpServletRequest request) {
        UUID userId = extractUserIdFromToken(request);
        log.info("Getting current user: {}", userId);
        UserDto user = authService.getCurrentUser(userId);
        return ResponseEntity.ok(user);
    }
    
    @PostMapping("/logout")
    @Operation(summary = "User logout", description = "Logout the current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Logout successful"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Void> logout(HttpServletRequest request) {
        UUID userId = extractUserIdFromToken(request);
        log.info("User {} logging out", userId);
        authService.logout(userId);
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/validate-email")
    @Operation(summary = "Validate email", description = "Check if an email address is valid and available")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Email validation result"),
        @ApiResponse(responseCode = "400", description = "Invalid email format")
    })
    public ResponseEntity<Map<String, Boolean>> validateEmail(@RequestParam String email) {
        log.info("Validating email: {}", email);
        boolean isValid = authService.validateEmail(email);
        return ResponseEntity.ok(Map.of("valid", isValid));
    }
    
    private UUID extractUserIdFromToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            return jwtUtil.extractUserId(token);
        }
        throw new com.foodmagic.common.exception.UnauthorizedException("No valid token found");
    }
}