package com.foodmagic.ai.client;

/**
 * AI提示词构建工具类
 */
public class PromptBuilder {
    
    private static final String RECIPE_PROMPT_TEMPLATE = 
        "你是一个专业的美食顾问。请根据以下食材提供美食建议：\n" +
        "食材：%s\n" +
        "请提供：\n" +
        "1. 可以制作的菜品名称\n" +
        "2. 简单的制作步骤\n" +
        "3. 营养价值说明\n" +
        "4. 烹饪小贴士";
    
    private static final String SIMPLE_PROMPT_TEMPLATE = 
        "食材：%s\n请提供相关的美食建议。";
    
    /**
     * 构建食谱建议提示词
     * @param ingredient 食材
     * @return 格式化的提示词
     */
    public static String buildRecipePrompt(String ingredient) {
        if (ingredient == null || ingredient.trim().isEmpty()) {
            throw new IllegalArgumentException("食材不能为空");
        }
        return String.format(RECIPE_PROMPT_TEMPLATE, ingredient.trim());
    }
    
    /**
     * 构建简单提示词
     * @param ingredient 食材
     * @return 简单格式的提示词
     */
    public static String buildSimplePrompt(String ingredient) {
        if (ingredient == null || ingredient.trim().isEmpty()) {
            throw new IllegalArgumentException("食材不能为空");
        }
        return String.format(SIMPLE_PROMPT_TEMPLATE, ingredient.trim());
    }
    
    /**
     * 构建自定义提示词
     * @param template 提示词模板
     * @param args 参数
     * @return 格式化的提示词
     */
    public static String buildCustomPrompt(String template, Object... args) {
        if (template == null || template.isEmpty()) {
            throw new IllegalArgumentException("提示词模板不能为空");
        }
        return String.format(template, args);
    }
}