package com.foodmagic.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserDto {
    private String id;
    private String email;
    private String displayName;
    private String avatarUrl;
    private String subscriptionTier;
    private String status;
    private String authProvider;
    private Instant createdAt;
    private Instant updatedAt;
}