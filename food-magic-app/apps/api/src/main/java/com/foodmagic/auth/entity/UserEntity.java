package com.foodmagic.auth.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "users")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString(exclude = {"passwordHash"})
@EqualsAndHashCode(of = {"id"})
public class UserEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;
    
    @Column(name = "email", unique = true)
    private String email;
    
    @Column(name = "password_hash")
    private String passwordHash;
    
    @Column(name = "display_name", length = 100)
    private String displayName;
    
    @Column(name = "avatar_url")
    private String avatarUrl;
    
    @Column(name = "auth_provider", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private AuthProvider authProvider;
    
    @Column(name = "provider_id")
    private String providerId;
    
    @Column(name = "status", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private UserStatus status = UserStatus.ACTIVE;
    
    @Column(name = "subscription_tier", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private SubscriptionTier subscriptionTier = SubscriptionTier.FREE;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;
    
    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private UserPreferencesEntity preferences;
    
    public enum AuthProvider {
        LOCAL,
        GUEST,
        GOOGLE,
        APPLE,
        WECHAT
    }
    
    public enum UserStatus {
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        DELETED
    }
    
    public enum SubscriptionTier {
        FREE,
        PREMIUM
    }
    
    @PrePersist
    protected void onCreate() {
        if (status == null) {
            status = UserStatus.ACTIVE;
        }
        if (subscriptionTier == null) {
            subscriptionTier = SubscriptionTier.FREE;
        }
    }
    
    public boolean isGuest() {
        return authProvider == AuthProvider.GUEST;
    }
    
    public boolean hasEmail() {
        return email != null && !email.isEmpty();
    }
    
    public boolean canLogin() {
        return status == UserStatus.ACTIVE && 
               (authProvider != AuthProvider.GUEST || 
                (hasEmail() && passwordHash != null));
    }
}