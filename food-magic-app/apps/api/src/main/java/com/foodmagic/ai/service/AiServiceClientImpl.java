package com.foodmagic.ai.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodmagic.ai.client.PromptBuilder;
import com.foodmagic.ai.common.AiProvider;
import com.foodmagic.ai.common.RetryHandler;
import com.foodmagic.ai.config.AiConfig;
import com.foodmagic.common.exception.BadRequestException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

/**
 * AI服务客户端实现类
 * 支持智谱AI(GLM)和阿里通义千问(Qwen)
 */
@Service
public class AiServiceClientImpl implements AiServiceClient {
    
    private static final Logger logger = LoggerFactory.getLogger(AiServiceClientImpl.class);
    private static final int PROMPT_LOG_LENGTH = 50;
    
    @Autowired
    private AiConfig aiConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private RetryHandler retryHandler;
    
    @Autowired
    private CircuitBreaker aiServiceCircuitBreaker;
    
    @Override
    public String generateResponse(String prompt) {
        logger.info("Generating AI response for prompt: {}", 
            prompt.substring(0, Math.min(prompt.length(), PROMPT_LOG_LENGTH)) + "...");
        
        validateConfig();
        
        // 使用断路器包装API调用
        Supplier<String> supplier = () -> callAiApi(prompt);
        
        try {
            String response = aiServiceCircuitBreaker.executeSupplier(supplier);
            logger.info("Successfully generated AI response");
            return response;
        } catch (Exception e) {
            // 断路器打开时，返回降级响应
            if (aiServiceCircuitBreaker.getState() == CircuitBreaker.State.OPEN) {
                logger.error("Circuit breaker is OPEN, returning fallback response");
                return getFallbackResponse(prompt);
            }
            logger.error("Failed to generate AI response: {}", e.getMessage());
            throw new RuntimeException("Failed to generate AI response", e);
        }
    }
    
    @Override
    public CompletableFuture<String> generateResponseAsync(String prompt) {
        validateConfig();
        
        // 使用RetryHandler进行异步重试
        return retryHandler.executeWithRetry(
            () -> {
                // 使用断路器包装API调用
                return aiServiceCircuitBreaker.executeSupplier(() -> callAiApi(prompt));
            },
            aiConfig.getMaxRetries(),
            aiConfig.getRetryDelay(),
            aiConfig.getRetryDelay() * 8 // 最大延迟为初始延迟的8倍
        ).exceptionally(throwable -> {
            logger.error("Async AI response generation failed: {}", throwable.getMessage());
            return getFallbackResponse(prompt);
        });
    }
    
    @Override
    public String generateRecipeSuggestion(String ingredient) {
        String prompt = PromptBuilder.buildRecipePrompt(ingredient);
        return generateResponse(prompt);
    }
    
    private void validateConfig() {
        if (aiConfig.getApiKey() == null || aiConfig.getApiKey().isEmpty()) {
            throw new IllegalStateException("AI API key is not configured");
        }
        if (aiConfig.getApiUrl() == null || aiConfig.getApiUrl().isEmpty()) {
            throw new IllegalStateException("AI API URL is not configured");
        }
    }
    
    private String callAiApi(String prompt) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        
        Map<String, Object> requestBody = new HashMap<>();
        AiProvider provider = AiProvider.fromValue(aiConfig.getProvider());
        
        if (provider == AiProvider.ZHIPU) {
            // 智谱AI请求格式
            headers.set("Authorization", "Bearer " + aiConfig.getApiKey());
            
            requestBody.put("model", aiConfig.getModel());
            requestBody.put("messages", List.of(
                Map.of("role", "user", "content", prompt)
            ));
            requestBody.put("max_tokens", aiConfig.getMaxTokens());
            requestBody.put("temperature", aiConfig.getTemperature());
        } else if (provider == AiProvider.QWEN) {
            // 阿里通义千问请求格式
            headers.set("Authorization", "Bearer " + aiConfig.getApiKey());
            
            Map<String, Object> input = new HashMap<>();
            input.put("messages", List.of(
                Map.of("role", "user", "content", prompt)
            ));
            
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("max_tokens", aiConfig.getMaxTokens());
            parameters.put("temperature", aiConfig.getTemperature());
            
            requestBody.put("model", aiConfig.getModel());
            requestBody.put("input", input);
            requestBody.put("parameters", parameters);
        } else {
            throw new IllegalStateException("Unsupported AI provider: " + aiConfig.getProvider());
        }
        
        try {
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(
                aiConfig.getApiUrl(),
                HttpMethod.POST,
                request,
                String.class
            );
            
            return parseAiResponse(response.getBody());
        } catch (HttpClientErrorException e) {
            logger.error("Client error calling AI API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BadRequestException("AI API client error: " + e.getMessage());
        } catch (HttpServerErrorException e) {
            logger.error("Server error calling AI API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("AI API server error: " + e.getMessage());
        } catch (ResourceAccessException e) {
            logger.error("Network error calling AI API: {}", e.getMessage());
            throw new RuntimeException("Network error calling AI API: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error calling AI API: {}", e.getMessage(), e);
            throw new RuntimeException("Unexpected error calling AI API: " + e.getMessage());
        }
    }
    
    private String parseAiResponse(String responseBody) {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            AiProvider provider = AiProvider.fromValue(aiConfig.getProvider());
            
            if (provider == AiProvider.ZHIPU) {
                // 智谱AI响应格式
                JsonNode choices = root.path("choices");
                if (choices.isArray() && choices.size() > 0) {
                    return choices.get(0).path("message").path("content").asText();
                }
            } else if (provider == AiProvider.QWEN) {
                // 阿里通义千问响应格式
                JsonNode output = root.path("output");
                if (!output.isMissingNode()) {
                    JsonNode text = output.path("text");
                    if (!text.isMissingNode()) {
                        return text.asText();
                    }
                    // 有些版本直接返回output字符串
                    return output.asText();
                }
            }
            
            // 如果无法解析，返回原始响应
            logger.warn("Could not parse AI response, returning raw response");
            return responseBody;
        } catch (Exception e) {
            logger.error("Error parsing AI response: {}", e.getMessage());
            // 解析失败时返回原始响应
            return responseBody;
        }
    }
    
    /**
     * 获取降级响应
     * 当AI服务不可用时返回的默认响应
     */
    private String getFallbackResponse(String prompt) {
        logger.warn("Returning fallback response for prompt: {}", 
            prompt.substring(0, Math.min(prompt.length(), PROMPT_LOG_LENGTH)) + "...");
        
        // 返回一个友好的降级响应
        return "抱歉，AI服务暂时不可用。请稍后再试。\n" +
               "您输入的食材是：" + extractIngredientFromPrompt(prompt) + "\n" +
               "建议您可以尝试简单的家常菜谱，如清炒、凉拌或者煲汤。";
    }
    
    /**
     * 从提示词中提取食材信息
     */
    private String extractIngredientFromPrompt(String prompt) {
        // 简单的提取逻辑，实际可以根据需要改进
        if (prompt.contains("食材") && prompt.contains("：")) {
            int start = prompt.indexOf("：") + 1;
            int end = Math.min(start + 20, prompt.length());
            return prompt.substring(start, end).trim();
        }
        return "未知食材";
    }
}