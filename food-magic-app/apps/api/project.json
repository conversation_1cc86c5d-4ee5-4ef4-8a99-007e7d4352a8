{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": ["scope:backend", "type:app", "platform:server"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/api/**/*.{ts,js}"]}, "configurations": {"boundaries": {"lintFilePatterns": ["apps/api/**/*.{ts,js}"]}}}}, "implicitDependencies": []}